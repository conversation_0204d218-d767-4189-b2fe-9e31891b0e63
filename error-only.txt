main.js:57 Vue app mounted successfully
index.js:222 Navigating to: /admin/requests AdminRequests
index.js:256 Auth status - Client: true Admin: true
index.js:294 Navigation successful to: /admin/requests
notificationService.js:36 🔗 Attempting SSE connection to: http://localhost:3000/api/notifications/stream?token=***
requests:1  Notifications permission has been blocked as the user has ignored the permission prompt several times. This can be reset in Page Info which can be accessed by clicking the tune icon next to the URL. See https://permanently-removed.invalid/feature/6443143280984064 for more information.
requests:1  Access to XMLHttpRequest at 'http://localhost:3000/api/admin/auth/profile' from origin 'http://localhost:8081' has been blocked by CORS policy: Response to preflight request doesn't pass access control check: No 'Access-Control-Allow-Origin' header is present on the requested resource.
api.js:42  API Error: {url: '/admin/auth/profile', method: 'get', status: undefined, message: 'Network Error', data: undefined}
eval @ api.js:42
Promise.then
_request @ Axios.js:163
request @ Axios.js:40
Axios.<computed> @ Axios.js:213
wrap @ bind.js:5
getProfile @ adminAuthService.js:66
loadAdminProfile @ AdminRequests.vue:1171
loadComponentData @ AdminRequests.vue:1185
mounted @ AdminRequests.vue:1055
eval @ runtime-core.esm-bundler.js:2836
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
hook.__weh.hook.__weh @ runtime-core.esm-bundler.js:2816
flushPostFlushCbs @ runtime-core.esm-bundler.js:385
flushJobs @ runtime-core.esm-bundler.js:427
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queuePostFlushCb @ runtime-core.esm-bundler.js:336
queueEffectWithSuspense @ runtime-core.esm-bundler.js:7374
baseWatchOptions.scheduler @ runtime-core.esm-bundler.js:6250
effect.scheduler @ reactivity.esm-bundler.js:1842
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:609
trigger @ reactivity.esm-bundler.js:583
set value @ reactivity.esm-bundler.js:1460
finalizeNavigation @ vue-router.mjs:3503
eval @ vue-router.mjs:3368
Promise.then
pushWithRedirect @ vue-router.mjs:3334
push @ vue-router.mjs:3260
install @ vue-router.mjs:3704
use @ runtime-core.esm-bundler.js:3886
eval @ main.js:54
./src/main.js @ app.js:138
__webpack_require__ @ app.js:418
(anonymous) @ app.js:1588
__webpack_require__.O @ app.js:460
(anonymous) @ app.js:1589
(anonymous) @ app.js:1591
AdminRequests.vue:1176  Failed to load admin profile: Error: {"message":"Network error. Please check your connection.","errors":[],"status":0}
    at AdminAuthService.handleError (adminAuthService.js:338:1)
    at AdminAuthService.getProfile (adminAuthService.js:75:1)
    at async Proxy.loadAdminProfile (AdminRequests.vue:1171:1)
    at async Promise.all (index 0)
    at async Proxy.loadComponentData (AdminRequests.vue:1185:1)
    at async Proxy.mounted (AdminRequests.vue:1055:1)
loadAdminProfile @ AdminRequests.vue:1176
await in loadAdminProfile
loadComponentData @ AdminRequests.vue:1185
mounted @ AdminRequests.vue:1055
eval @ runtime-core.esm-bundler.js:2836
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
hook.__weh.hook.__weh @ runtime-core.esm-bundler.js:2816
flushPostFlushCbs @ runtime-core.esm-bundler.js:385
flushJobs @ runtime-core.esm-bundler.js:427
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queuePostFlushCb @ runtime-core.esm-bundler.js:336
queueEffectWithSuspense @ runtime-core.esm-bundler.js:7374
baseWatchOptions.scheduler @ runtime-core.esm-bundler.js:6250
effect.scheduler @ reactivity.esm-bundler.js:1842
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:609
trigger @ reactivity.esm-bundler.js:583
set value @ reactivity.esm-bundler.js:1460
finalizeNavigation @ vue-router.mjs:3503
eval @ vue-router.mjs:3368
Promise.then
pushWithRedirect @ vue-router.mjs:3334
push @ vue-router.mjs:3260
install @ vue-router.mjs:3704
use @ runtime-core.esm-bundler.js:3886
eval @ main.js:54
./src/main.js @ app.js:138
__webpack_require__ @ app.js:418
(anonymous) @ app.js:1588
__webpack_require__.O @ app.js:460
(anonymous) @ app.js:1589
(anonymous) @ app.js:1591
adminAuthService.js:66   GET http://localhost:3000/api/admin/auth/profile net::ERR_FAILED
dispatchXhrRequest @ xhr.js:195
xhr @ xhr.js:15
dispatchRequest @ dispatchRequest.js:51
Promise.then
_request @ Axios.js:163
request @ Axios.js:40
Axios.<computed> @ Axios.js:213
wrap @ bind.js:5
getProfile @ adminAuthService.js:66
loadAdminProfile @ AdminRequests.vue:1171
loadComponentData @ AdminRequests.vue:1185
mounted @ AdminRequests.vue:1055
eval @ runtime-core.esm-bundler.js:2836
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
hook.__weh.hook.__weh @ runtime-core.esm-bundler.js:2816
flushPostFlushCbs @ runtime-core.esm-bundler.js:385
flushJobs @ runtime-core.esm-bundler.js:427
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queuePostFlushCb @ runtime-core.esm-bundler.js:336
queueEffectWithSuspense @ runtime-core.esm-bundler.js:7374
baseWatchOptions.scheduler @ runtime-core.esm-bundler.js:6250
effect.scheduler @ reactivity.esm-bundler.js:1842
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:609
trigger @ reactivity.esm-bundler.js:583
set value @ reactivity.esm-bundler.js:1460
finalizeNavigation @ vue-router.mjs:3503
eval @ vue-router.mjs:3368
Promise.then
pushWithRedirect @ vue-router.mjs:3334
push @ vue-router.mjs:3260
install @ vue-router.mjs:3704
use @ runtime-core.esm-bundler.js:3886
eval @ main.js:54
./src/main.js @ app.js:138
__webpack_require__ @ app.js:418
(anonymous) @ app.js:1588
__webpack_require__.O @ app.js:460
(anonymous) @ app.js:1589
(anonymous) @ app.js:1591
requests:1  Access to XMLHttpRequest at 'http://localhost:3000/api/admin/documents/status-options' from origin 'http://localhost:8081' has been blocked by CORS policy: Response to preflight request doesn't pass access control check: No 'Access-Control-Allow-Origin' header is present on the requested resource.
api.js:42  API Error: {url: '/admin/documents/status-options', method: 'get', status: undefined, message: 'Network Error', data: undefined}
eval @ api.js:42
Promise.then
_request @ Axios.js:163
request @ Axios.js:40
Axios.<computed> @ Axios.js:213
wrap @ bind.js:5
getStatusOptions @ adminDocumentService.js:94
loadStatusOptions @ AdminRequests.vue:1208
loadComponentData @ AdminRequests.vue:1185
mounted @ AdminRequests.vue:1055
eval @ runtime-core.esm-bundler.js:2836
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
hook.__weh.hook.__weh @ runtime-core.esm-bundler.js:2816
flushPostFlushCbs @ runtime-core.esm-bundler.js:385
flushJobs @ runtime-core.esm-bundler.js:427
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queuePostFlushCb @ runtime-core.esm-bundler.js:336
queueEffectWithSuspense @ runtime-core.esm-bundler.js:7374
baseWatchOptions.scheduler @ runtime-core.esm-bundler.js:6250
effect.scheduler @ reactivity.esm-bundler.js:1842
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:609
trigger @ reactivity.esm-bundler.js:583
set value @ reactivity.esm-bundler.js:1460
finalizeNavigation @ vue-router.mjs:3503
eval @ vue-router.mjs:3368
Promise.then
pushWithRedirect @ vue-router.mjs:3334
push @ vue-router.mjs:3260
install @ vue-router.mjs:3704
use @ runtime-core.esm-bundler.js:3886
eval @ main.js:54
./src/main.js @ app.js:138
__webpack_require__ @ app.js:418
(anonymous) @ app.js:1588
__webpack_require__.O @ app.js:460
(anonymous) @ app.js:1589
(anonymous) @ app.js:1591
AdminRequests.vue:1213  Failed to load status options: Error: {"message":"Network error. Please check your connection.","errors":[],"status":0}
    at AdminDocumentService.handleError (adminDocumentService.js:354:1)
    at AdminDocumentService.getStatusOptions (adminDocumentService.js:97:1)
    at async Proxy.loadStatusOptions (AdminRequests.vue:1208:1)
    at async Promise.all (index 1)
    at async Proxy.loadComponentData (AdminRequests.vue:1185:1)
    at async Proxy.mounted (AdminRequests.vue:1055:1)
loadStatusOptions @ AdminRequests.vue:1213
await in loadStatusOptions
loadComponentData @ AdminRequests.vue:1185
mounted @ AdminRequests.vue:1055
eval @ runtime-core.esm-bundler.js:2836
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
hook.__weh.hook.__weh @ runtime-core.esm-bundler.js:2816
flushPostFlushCbs @ runtime-core.esm-bundler.js:385
flushJobs @ runtime-core.esm-bundler.js:427
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queuePostFlushCb @ runtime-core.esm-bundler.js:336
queueEffectWithSuspense @ runtime-core.esm-bundler.js:7374
baseWatchOptions.scheduler @ runtime-core.esm-bundler.js:6250
effect.scheduler @ reactivity.esm-bundler.js:1842
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:609
trigger @ reactivity.esm-bundler.js:583
set value @ reactivity.esm-bundler.js:1460
finalizeNavigation @ vue-router.mjs:3503
eval @ vue-router.mjs:3368
Promise.then
pushWithRedirect @ vue-router.mjs:3334
push @ vue-router.mjs:3260
install @ vue-router.mjs:3704
use @ runtime-core.esm-bundler.js:3886
eval @ main.js:54
./src/main.js @ app.js:138
__webpack_require__ @ app.js:418
(anonymous) @ app.js:1588
__webpack_require__.O @ app.js:460
(anonymous) @ app.js:1589
(anonymous) @ app.js:1591
adminDocumentService.js:94   GET http://localhost:3000/api/admin/documents/status-options net::ERR_FAILED
dispatchXhrRequest @ xhr.js:195
xhr @ xhr.js:15
dispatchRequest @ dispatchRequest.js:51
Promise.then
_request @ Axios.js:163
request @ Axios.js:40
Axios.<computed> @ Axios.js:213
wrap @ bind.js:5
getStatusOptions @ adminDocumentService.js:94
loadStatusOptions @ AdminRequests.vue:1208
loadComponentData @ AdminRequests.vue:1185
mounted @ AdminRequests.vue:1055
eval @ runtime-core.esm-bundler.js:2836
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
hook.__weh.hook.__weh @ runtime-core.esm-bundler.js:2816
flushPostFlushCbs @ runtime-core.esm-bundler.js:385
flushJobs @ runtime-core.esm-bundler.js:427
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queuePostFlushCb @ runtime-core.esm-bundler.js:336
queueEffectWithSuspense @ runtime-core.esm-bundler.js:7374
baseWatchOptions.scheduler @ runtime-core.esm-bundler.js:6250
effect.scheduler @ reactivity.esm-bundler.js:1842
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:609
trigger @ reactivity.esm-bundler.js:583
set value @ reactivity.esm-bundler.js:1460
finalizeNavigation @ vue-router.mjs:3503
eval @ vue-router.mjs:3368
Promise.then
pushWithRedirect @ vue-router.mjs:3334
push @ vue-router.mjs:3260
install @ vue-router.mjs:3704
use @ runtime-core.esm-bundler.js:3886
eval @ main.js:54
./src/main.js @ app.js:138
__webpack_require__ @ app.js:418
(anonymous) @ app.js:1588
__webpack_require__.O @ app.js:460
(anonymous) @ app.js:1589
(anonymous) @ app.js:1591
requests:1  Access to XMLHttpRequest at 'http://localhost:3000/api/admin/documents/requests?page=1&limit=10&status=&document_type=&priority=&search=&date_from=&date_to=' from origin 'http://localhost:8081' has been blocked by CORS policy: Response to preflight request doesn't pass access control check: No 'Access-Control-Allow-Origin' header is present on the requested resource.
api.js:42  API Error: {url: '/admin/documents/requests?page=1&limit=10&status=&…ument_type=&priority=&search=&date_from=&date_to=', method: 'get', status: undefined, message: 'Network Error', data: undefined}
eval @ api.js:42
Promise.then
_request @ Axios.js:163
request @ Axios.js:40
Axios.<computed> @ Axios.js:213
wrap @ bind.js:5
getAllRequests @ adminDocumentService.js:51
loadRequests @ AdminRequests.vue:1245
loadComponentData @ AdminRequests.vue:1185
mounted @ AdminRequests.vue:1055
eval @ runtime-core.esm-bundler.js:2836
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
hook.__weh.hook.__weh @ runtime-core.esm-bundler.js:2816
flushPostFlushCbs @ runtime-core.esm-bundler.js:385
flushJobs @ runtime-core.esm-bundler.js:427
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queuePostFlushCb @ runtime-core.esm-bundler.js:336
queueEffectWithSuspense @ runtime-core.esm-bundler.js:7374
baseWatchOptions.scheduler @ runtime-core.esm-bundler.js:6250
effect.scheduler @ reactivity.esm-bundler.js:1842
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:609
trigger @ reactivity.esm-bundler.js:583
set value @ reactivity.esm-bundler.js:1460
finalizeNavigation @ vue-router.mjs:3503
eval @ vue-router.mjs:3368
Promise.then
pushWithRedirect @ vue-router.mjs:3334
push @ vue-router.mjs:3260
install @ vue-router.mjs:3704
use @ runtime-core.esm-bundler.js:3886
eval @ main.js:54
./src/main.js @ app.js:138
__webpack_require__ @ app.js:418
(anonymous) @ app.js:1588
__webpack_require__.O @ app.js:460
(anonymous) @ app.js:1589
(anonymous) @ app.js:1591
AdminRequests.vue:1256  Failed to load requests: Error: {"message":"Network error. Please check your connection.","errors":[],"status":0}
    at AdminDocumentService.handleError (adminDocumentService.js:354:1)
    at AdminDocumentService.getAllRequests (adminDocumentService.js:54:1)
    at async Proxy.loadRequests (AdminRequests.vue:1245:1)
    at async Promise.all (index 2)
    at async Proxy.loadComponentData (AdminRequests.vue:1185:1)
    at async Proxy.mounted (AdminRequests.vue:1055:1)
loadRequests @ AdminRequests.vue:1256
await in loadRequests
loadComponentData @ AdminRequests.vue:1185
mounted @ AdminRequests.vue:1055
eval @ runtime-core.esm-bundler.js:2836
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
hook.__weh.hook.__weh @ runtime-core.esm-bundler.js:2816
flushPostFlushCbs @ runtime-core.esm-bundler.js:385
flushJobs @ runtime-core.esm-bundler.js:427
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queuePostFlushCb @ runtime-core.esm-bundler.js:336
queueEffectWithSuspense @ runtime-core.esm-bundler.js:7374
baseWatchOptions.scheduler @ runtime-core.esm-bundler.js:6250
effect.scheduler @ reactivity.esm-bundler.js:1842
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:609
trigger @ reactivity.esm-bundler.js:583
set value @ reactivity.esm-bundler.js:1460
finalizeNavigation @ vue-router.mjs:3503
eval @ vue-router.mjs:3368
Promise.then
pushWithRedirect @ vue-router.mjs:3334
push @ vue-router.mjs:3260
install @ vue-router.mjs:3704
use @ runtime-core.esm-bundler.js:3886
eval @ main.js:54
./src/main.js @ app.js:138
__webpack_require__ @ app.js:418
(anonymous) @ app.js:1588
__webpack_require__.O @ app.js:460
(anonymous) @ app.js:1589
(anonymous) @ app.js:1591
adminDocumentService.js:51   GET http://localhost:3000/api/admin/documents/requests?page=1&limit=10&status=&document_type=&priority=&search=&date_from=&date_to= net::ERR_FAILED
dispatchXhrRequest @ xhr.js:195
xhr @ xhr.js:15
dispatchRequest @ dispatchRequest.js:51
Promise.then
_request @ Axios.js:163
request @ Axios.js:40
Axios.<computed> @ Axios.js:213
wrap @ bind.js:5
getAllRequests @ adminDocumentService.js:51
loadRequests @ AdminRequests.vue:1245
loadComponentData @ AdminRequests.vue:1185
mounted @ AdminRequests.vue:1055
eval @ runtime-core.esm-bundler.js:2836
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
hook.__weh.hook.__weh @ runtime-core.esm-bundler.js:2816
flushPostFlushCbs @ runtime-core.esm-bundler.js:385
flushJobs @ runtime-core.esm-bundler.js:427
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queuePostFlushCb @ runtime-core.esm-bundler.js:336
queueEffectWithSuspense @ runtime-core.esm-bundler.js:7374
baseWatchOptions.scheduler @ runtime-core.esm-bundler.js:6250
effect.scheduler @ reactivity.esm-bundler.js:1842
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:609
trigger @ reactivity.esm-bundler.js:583
set value @ reactivity.esm-bundler.js:1460
finalizeNavigation @ vue-router.mjs:3503
eval @ vue-router.mjs:3368
Promise.then
pushWithRedirect @ vue-router.mjs:3334
push @ vue-router.mjs:3260
install @ vue-router.mjs:3704
use @ runtime-core.esm-bundler.js:3886
eval @ main.js:54
./src/main.js @ app.js:138
__webpack_require__ @ app.js:418
(anonymous) @ app.js:1588
__webpack_require__.O @ app.js:460
(anonymous) @ app.js:1589
(anonymous) @ app.js:1591
requests:1  Access to XMLHttpRequest at 'http://localhost:3000/api/admin/documents/dashboard/stats' from origin 'http://localhost:8081' has been blocked by CORS policy: Response to preflight request doesn't pass access control check: No 'Access-Control-Allow-Origin' header is present on the requested resource.
api.js:42  API Error: {url: '/admin/documents/dashboard/stats', method: 'get', status: undefined, message: 'Network Error', data: undefined}
eval @ api.js:42
Promise.then
_request @ Axios.js:163
request @ Axios.js:40
Axios.<computed> @ Axios.js:213
wrap @ bind.js:5
getDashboardStats @ adminDocumentService.js:12
loadDashboardStats @ AdminRequests.vue:1221
loadComponentData @ AdminRequests.vue:1185
mounted @ AdminRequests.vue:1055
eval @ runtime-core.esm-bundler.js:2836
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
hook.__weh.hook.__weh @ runtime-core.esm-bundler.js:2816
flushPostFlushCbs @ runtime-core.esm-bundler.js:385
flushJobs @ runtime-core.esm-bundler.js:427
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queuePostFlushCb @ runtime-core.esm-bundler.js:336
queueEffectWithSuspense @ runtime-core.esm-bundler.js:7374
baseWatchOptions.scheduler @ runtime-core.esm-bundler.js:6250
effect.scheduler @ reactivity.esm-bundler.js:1842
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:609
trigger @ reactivity.esm-bundler.js:583
set value @ reactivity.esm-bundler.js:1460
finalizeNavigation @ vue-router.mjs:3503
eval @ vue-router.mjs:3368
Promise.then
pushWithRedirect @ vue-router.mjs:3334
push @ vue-router.mjs:3260
install @ vue-router.mjs:3704
use @ runtime-core.esm-bundler.js:3886
eval @ main.js:54
./src/main.js @ app.js:138
__webpack_require__ @ app.js:418
(anonymous) @ app.js:1588
__webpack_require__.O @ app.js:460
(anonymous) @ app.js:1589
(anonymous) @ app.js:1591
AdminRequests.vue:1232  Failed to load dashboard stats: Error: {"message":"Network error. Please check your connection.","errors":[],"status":0}
    at AdminDocumentService.handleError (adminDocumentService.js:354:1)
    at AdminDocumentService.getDashboardStats (adminDocumentService.js:15:1)
    at async Proxy.loadDashboardStats (AdminRequests.vue:1221:1)
    at async Promise.all (index 3)
    at async Proxy.loadComponentData (AdminRequests.vue:1185:1)
    at async Proxy.mounted (AdminRequests.vue:1055:1)
loadDashboardStats @ AdminRequests.vue:1232
await in loadDashboardStats
loadComponentData @ AdminRequests.vue:1185
mounted @ AdminRequests.vue:1055
eval @ runtime-core.esm-bundler.js:2836
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
hook.__weh.hook.__weh @ runtime-core.esm-bundler.js:2816
flushPostFlushCbs @ runtime-core.esm-bundler.js:385
flushJobs @ runtime-core.esm-bundler.js:427
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queuePostFlushCb @ runtime-core.esm-bundler.js:336
queueEffectWithSuspense @ runtime-core.esm-bundler.js:7374
baseWatchOptions.scheduler @ runtime-core.esm-bundler.js:6250
effect.scheduler @ reactivity.esm-bundler.js:1842
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:609
trigger @ reactivity.esm-bundler.js:583
set value @ reactivity.esm-bundler.js:1460
finalizeNavigation @ vue-router.mjs:3503
eval @ vue-router.mjs:3368
Promise.then
pushWithRedirect @ vue-router.mjs:3334
push @ vue-router.mjs:3260
install @ vue-router.mjs:3704
use @ runtime-core.esm-bundler.js:3886
eval @ main.js:54
./src/main.js @ app.js:138
__webpack_require__ @ app.js:418
(anonymous) @ app.js:1588
__webpack_require__.O @ app.js:460
(anonymous) @ app.js:1589
(anonymous) @ app.js:1591
AdminRequests.vue:1571 Initializing real-time features for AdminRequests
notificationService.js:36 🔗 Attempting SSE connection to: http://localhost:3000/api/notifications/stream?token=***
AdminRequests.vue:1611 Auto-refresh started with 30s interval
adminDocumentService.js:12   GET http://localhost:3000/api/admin/documents/dashboard/stats net::ERR_FAILED
dispatchXhrRequest @ xhr.js:195
xhr @ xhr.js:15
dispatchRequest @ dispatchRequest.js:51
Promise.then
_request @ Axios.js:163
request @ Axios.js:40
Axios.<computed> @ Axios.js:213
wrap @ bind.js:5
getDashboardStats @ adminDocumentService.js:12
loadDashboardStats @ AdminRequests.vue:1221
loadComponentData @ AdminRequests.vue:1185
mounted @ AdminRequests.vue:1055
eval @ runtime-core.esm-bundler.js:2836
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
hook.__weh.hook.__weh @ runtime-core.esm-bundler.js:2816
flushPostFlushCbs @ runtime-core.esm-bundler.js:385
flushJobs @ runtime-core.esm-bundler.js:427
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queuePostFlushCb @ runtime-core.esm-bundler.js:336
queueEffectWithSuspense @ runtime-core.esm-bundler.js:7374
baseWatchOptions.scheduler @ runtime-core.esm-bundler.js:6250
effect.scheduler @ reactivity.esm-bundler.js:1842
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:609
trigger @ reactivity.esm-bundler.js:583
set value @ reactivity.esm-bundler.js:1460
finalizeNavigation @ vue-router.mjs:3503
eval @ vue-router.mjs:3368
Promise.then
pushWithRedirect @ vue-router.mjs:3334
push @ vue-router.mjs:3260
install @ vue-router.mjs:3704
use @ runtime-core.esm-bundler.js:3886
eval @ main.js:54
./src/main.js @ app.js:138
__webpack_require__ @ app.js:418
(anonymous) @ app.js:1588
__webpack_require__.O @ app.js:460
(anonymous) @ app.js:1589
(anonymous) @ app.js:1591
requests:1  Access to XMLHttpRequest at 'http://localhost:3000/api/notifications/unread-count' from origin 'http://localhost:8081' has been blocked by CORS policy: Response to preflight request doesn't pass access control check: No 'Access-Control-Allow-Origin' header is present on the requested resource.
notificationService.js:231  Failed to get unread count: AxiosError {message: 'Network Error', name: 'AxiosError', code: 'ERR_NETWORK', config: {…}, request: XMLHttpRequest, …}
getUnreadCount @ notificationService.js:231
await in getUnreadCount
loadUnreadCount @ AdminNotifications.vue:181
initializeNotifications @ AdminNotifications.vue:121
await in initializeNotifications
mounted @ AdminNotifications.vue:101
eval @ runtime-core.esm-bundler.js:2836
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
hook.__weh.hook.__weh @ runtime-core.esm-bundler.js:2816
flushPostFlushCbs @ runtime-core.esm-bundler.js:385
flushJobs @ runtime-core.esm-bundler.js:427
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queuePostFlushCb @ runtime-core.esm-bundler.js:336
queueEffectWithSuspense @ runtime-core.esm-bundler.js:7374
baseWatchOptions.scheduler @ runtime-core.esm-bundler.js:6250
effect.scheduler @ reactivity.esm-bundler.js:1842
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:609
trigger @ reactivity.esm-bundler.js:583
set value @ reactivity.esm-bundler.js:1460
finalizeNavigation @ vue-router.mjs:3503
eval @ vue-router.mjs:3368
Promise.then
pushWithRedirect @ vue-router.mjs:3334
push @ vue-router.mjs:3260
install @ vue-router.mjs:3704
use @ runtime-core.esm-bundler.js:3886
eval @ main.js:54
./src/main.js @ app.js:138
__webpack_require__ @ app.js:418
(anonymous) @ app.js:1588
__webpack_require__.O @ app.js:460
(anonymous) @ app.js:1589
(anonymous) @ app.js:1591
AdminNotifications.vue:183  Failed to load unread count: AxiosError {message: 'Network Error', name: 'AxiosError', code: 'ERR_NETWORK', config: {…}, request: XMLHttpRequest, …}
loadUnreadCount @ AdminNotifications.vue:183
await in loadUnreadCount
initializeNotifications @ AdminNotifications.vue:121
await in initializeNotifications
mounted @ AdminNotifications.vue:101
eval @ runtime-core.esm-bundler.js:2836
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
hook.__weh.hook.__weh @ runtime-core.esm-bundler.js:2816
flushPostFlushCbs @ runtime-core.esm-bundler.js:385
flushJobs @ runtime-core.esm-bundler.js:427
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queuePostFlushCb @ runtime-core.esm-bundler.js:336
queueEffectWithSuspense @ runtime-core.esm-bundler.js:7374
baseWatchOptions.scheduler @ runtime-core.esm-bundler.js:6250
effect.scheduler @ reactivity.esm-bundler.js:1842
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:609
trigger @ reactivity.esm-bundler.js:583
set value @ reactivity.esm-bundler.js:1460
finalizeNavigation @ vue-router.mjs:3503
eval @ vue-router.mjs:3368
Promise.then
pushWithRedirect @ vue-router.mjs:3334
push @ vue-router.mjs:3260
install @ vue-router.mjs:3704
use @ runtime-core.esm-bundler.js:3886
eval @ main.js:54
./src/main.js @ app.js:138
__webpack_require__ @ app.js:418
(anonymous) @ app.js:1588
__webpack_require__.O @ app.js:460
(anonymous) @ app.js:1589
(anonymous) @ app.js:1591
notificationService.js:224   GET http://localhost:3000/api/notifications/unread-count net::ERR_FAILED
dispatchXhrRequest @ xhr.js:195
xhr @ xhr.js:15
dispatchRequest @ dispatchRequest.js:51
_request @ Axios.js:187
request @ Axios.js:40
Axios.<computed> @ Axios.js:213
wrap @ bind.js:5
getUnreadCount @ notificationService.js:224
loadUnreadCount @ AdminNotifications.vue:181
initializeNotifications @ AdminNotifications.vue:121
await in initializeNotifications
mounted @ AdminNotifications.vue:101
eval @ runtime-core.esm-bundler.js:2836
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
hook.__weh.hook.__weh @ runtime-core.esm-bundler.js:2816
flushPostFlushCbs @ runtime-core.esm-bundler.js:385
flushJobs @ runtime-core.esm-bundler.js:427
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queuePostFlushCb @ runtime-core.esm-bundler.js:336
queueEffectWithSuspense @ runtime-core.esm-bundler.js:7374
baseWatchOptions.scheduler @ runtime-core.esm-bundler.js:6250
effect.scheduler @ reactivity.esm-bundler.js:1842
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:609
trigger @ reactivity.esm-bundler.js:583
set value @ reactivity.esm-bundler.js:1460
finalizeNavigation @ vue-router.mjs:3503
eval @ vue-router.mjs:3368
Promise.then
pushWithRedirect @ vue-router.mjs:3334
push @ vue-router.mjs:3260
install @ vue-router.mjs:3704
use @ runtime-core.esm-bundler.js:3886
eval @ main.js:54
./src/main.js @ app.js:138
__webpack_require__ @ app.js:418
(anonymous) @ app.js:1588
__webpack_require__.O @ app.js:460
(anonymous) @ app.js:1589
(anonymous) @ app.js:1591
requests:1  Access to resource at 'http://localhost:3000/api/notifications/stream?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MzIsInVzZXJuYW1lIjoiYWRtaW4xMjM0NSIsInJvbGUiOiJhZG1pbiIsInR5cGUiOiJhZG1pbiIsImlhdCI6MTc1MTg5NzM1MiwiZXhwIjoxNzU0NDg5MzUyfQ.1nESAPUbg2huJpsFsu-xPWtp6KK19SHoZJ0aGAdxpcU' from origin 'http://localhost:8081' has been blocked by CORS policy: No 'Access-Control-Allow-Origin' header is present on the requested resource.
notificationService.js:58  ❌ SSE connection error: Event {isTrusted: true, type: 'error', target: EventSource, currentTarget: EventSource, eventPhase: 2, …}
eventSource.onerror @ notificationService.js:58
notificationService.js:59  EventSource readyState: 0
eventSource.onerror @ notificationService.js:59
AdminNotifications.vue:245  Notification stream error: Event {isTrusted: true, type: 'error', target: EventSource, currentTarget: EventSource, eventPhase: 2, …}
onError @ AdminNotifications.vue:245
eval @ notificationService.js:191
emit @ notificationService.js:189
eventSource.onerror @ notificationService.js:61
AdminHeader.vue:233  Notification error: Connection to notification stream failed
handleNotificationError @ AdminHeader.vue:233
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
emit @ runtime-core.esm-bundler.js:6439
onError @ AdminNotifications.vue:246
eval @ notificationService.js:191
emit @ notificationService.js:189
eventSource.onerror @ notificationService.js:61
notificationService.js:99 Attempting to reconnect in 1000ms (attempt 1/5)
:3000/api/notifications/stream?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MzIsInVzZXJuYW1lIjoiYWRtaW4xMjM0NSIsInJvbGUiOiJhZG1pbiIsInR5cGUiOiJhZG1pbiIsImlhdCI6MTc1MTg5NzM1MiwiZXhwIjoxNzU0NDg5MzUyfQ.1nESAPUbg2huJpsFsu-xPWtp6KK19SHoZJ0aGAdxpcU:1   GET http://localhost:3000/api/notifications/stream?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MzIsInVzZXJuYW1lIjoiYWRtaW4xMjM0NSIsInJvbGUiOiJhZG1pbiIsInR5cGUiOiJhZG1pbiIsImlhdCI6MTc1MTg5NzM1MiwiZXhwIjoxNzU0NDg5MzUyfQ.1nESAPUbg2huJpsFsu-xPWtp6KK19SHoZJ0aGAdxpcU net::ERR_FAILED 429 (Too Many Requests)
requests:1  Access to resource at 'http://localhost:3000/api/notifications/stream?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MzIsInVzZXJuYW1lIjoiYWRtaW4xMjM0NSIsInJvbGUiOiJhZG1pbiIsInR5cGUiOiJhZG1pbiIsImlhdCI6MTc1MTg5NzM1MiwiZXhwIjoxNzU0NDg5MzUyfQ.1nESAPUbg2huJpsFsu-xPWtp6KK19SHoZJ0aGAdxpcU' from origin 'http://localhost:8081' has been blocked by CORS policy: No 'Access-Control-Allow-Origin' header is present on the requested resource.
notificationService.js:58  ❌ SSE connection error: Event {isTrusted: true, type: 'error', target: EventSource, currentTarget: EventSource, eventPhase: 2, …}
eventSource.onerror @ notificationService.js:58
notificationService.js:59  EventSource readyState: 2
eventSource.onerror @ notificationService.js:59
AdminNotifications.vue:245  Notification stream error: Event {isTrusted: true, type: 'error', target: EventSource, currentTarget: EventSource, eventPhase: 2, …}
onError @ AdminNotifications.vue:245
eval @ notificationService.js:191
emit @ notificationService.js:189
eventSource.onerror @ notificationService.js:61
AdminHeader.vue:233  Notification error: Connection to notification stream failed
handleNotificationError @ AdminHeader.vue:233
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
emit @ runtime-core.esm-bundler.js:6439
onError @ AdminNotifications.vue:246
eval @ notificationService.js:191
emit @ notificationService.js:189
eventSource.onerror @ notificationService.js:61
notificationService.js:99 Attempting to reconnect in 2000ms (attempt 2/5)
:3000/api/notifications/stream?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MzIsInVzZXJuYW1lIjoiYWRtaW4xMjM0NSIsInJvbGUiOiJhZG1pbiIsInR5cGUiOiJhZG1pbiIsImlhdCI6MTc1MTg5NzM1MiwiZXhwIjoxNzU0NDg5MzUyfQ.1nESAPUbg2huJpsFsu-xPWtp6KK19SHoZJ0aGAdxpcU:1   GET http://localhost:3000/api/notifications/stream?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MzIsInVzZXJuYW1lIjoiYWRtaW4xMjM0NSIsInJvbGUiOiJhZG1pbiIsInR5cGUiOiJhZG1pbiIsImlhdCI6MTc1MTg5NzM1MiwiZXhwIjoxNzU0NDg5MzUyfQ.1nESAPUbg2huJpsFsu-xPWtp6KK19SHoZJ0aGAdxpcU net::ERR_FAILED 429 (Too Many Requests)
notificationService.js:36 🔗 Attempting SSE connection to: http://localhost:3000/api/notifications/stream?token=***
requests:1  Access to resource at 'http://localhost:3000/api/notifications/stream?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MzIsInVzZXJuYW1lIjoiYWRtaW4xMjM0NSIsInJvbGUiOiJhZG1pbiIsInR5cGUiOiJhZG1pbiIsImlhdCI6MTc1MTg5NzM1MiwiZXhwIjoxNzU0NDg5MzUyfQ.1nESAPUbg2huJpsFsu-xPWtp6KK19SHoZJ0aGAdxpcU' from origin 'http://localhost:8081' has been blocked by CORS policy: No 'Access-Control-Allow-Origin' header is present on the requested resource.
notificationService.js:58  ❌ SSE connection error: Event {isTrusted: true, type: 'error', target: EventSource, currentTarget: EventSource, eventPhase: 2, …}
eventSource.onerror @ notificationService.js:58
notificationService.js:59  EventSource readyState: 2
eventSource.onerror @ notificationService.js:59
AdminNotifications.vue:245  Notification stream error: Event {isTrusted: true, type: 'error', target: EventSource, currentTarget: EventSource, eventPhase: 2, …}
onError @ AdminNotifications.vue:245
eval @ notificationService.js:191
emit @ notificationService.js:189
eventSource.onerror @ notificationService.js:61
AdminHeader.vue:233  Notification error: Connection to notification stream failed
handleNotificationError @ AdminHeader.vue:233
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
emit @ runtime-core.esm-bundler.js:6439
onError @ AdminNotifications.vue:246
eval @ notificationService.js:191
emit @ notificationService.js:189
eventSource.onerror @ notificationService.js:61
notificationService.js:99 Attempting to reconnect in 4000ms (attempt 3/5)
:3000/api/notifications/stream?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MzIsInVzZXJuYW1lIjoiYWRtaW4xMjM0NSIsInJvbGUiOiJhZG1pbiIsInR5cGUiOiJhZG1pbiIsImlhdCI6MTc1MTg5NzM1MiwiZXhwIjoxNzU0NDg5MzUyfQ.1nESAPUbg2huJpsFsu-xPWtp6KK19SHoZJ0aGAdxpcU:1   GET http://localhost:3000/api/notifications/stream?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MzIsInVzZXJuYW1lIjoiYWRtaW4xMjM0NSIsInJvbGUiOiJhZG1pbiIsInR5cGUiOiJhZG1pbiIsImlhdCI6MTc1MTg5NzM1MiwiZXhwIjoxNzU0NDg5MzUyfQ.1nESAPUbg2huJpsFsu-xPWtp6KK19SHoZJ0aGAdxpcU net::ERR_FAILED 429 (Too Many Requests)
notificationService.js:36 🔗 Attempting SSE connection to: http://localhost:3000/api/notifications/stream?token=***
requests:1  Access to resource at 'http://localhost:3000/api/notifications/stream?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MzIsInVzZXJuYW1lIjoiYWRtaW4xMjM0NSIsInJvbGUiOiJhZG1pbiIsInR5cGUiOiJhZG1pbiIsImlhdCI6MTc1MTg5NzM1MiwiZXhwIjoxNzU0NDg5MzUyfQ.1nESAPUbg2huJpsFsu-xPWtp6KK19SHoZJ0aGAdxpcU' from origin 'http://localhost:8081' has been blocked by CORS policy: No 'Access-Control-Allow-Origin' header is present on the requested resource.
notificationService.js:58  ❌ SSE connection error: Event {isTrusted: true, type: 'error', target: EventSource, currentTarget: EventSource, eventPhase: 2, …}
eventSource.onerror @ notificationService.js:58
notificationService.js:59  EventSource readyState: 2
eventSource.onerror @ notificationService.js:59
AdminNotifications.vue:245  Notification stream error: Event {isTrusted: true, type: 'error', target: EventSource, currentTarget: EventSource, eventPhase: 2, …}
onError @ AdminNotifications.vue:245
eval @ notificationService.js:191
emit @ notificationService.js:189
eventSource.onerror @ notificationService.js:61
AdminHeader.vue:233  Notification error: Connection to notification stream failed
handleNotificationError @ AdminHeader.vue:233
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
emit @ runtime-core.esm-bundler.js:6439
onError @ AdminNotifications.vue:246
eval @ notificationService.js:191
emit @ notificationService.js:189
eventSource.onerror @ notificationService.js:61
notificationService.js:99 Attempting to reconnect in 8000ms (attempt 4/5)
:3000/api/notifications/stream?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MzIsInVzZXJuYW1lIjoiYWRtaW4xMjM0NSIsInJvbGUiOiJhZG1pbiIsInR5cGUiOiJhZG1pbiIsImlhdCI6MTc1MTg5NzM1MiwiZXhwIjoxNzU0NDg5MzUyfQ.1nESAPUbg2huJpsFsu-xPWtp6KK19SHoZJ0aGAdxpcU:1   GET http://localhost:3000/api/notifications/stream?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MzIsInVzZXJuYW1lIjoiYWRtaW4xMjM0NSIsInJvbGUiOiJhZG1pbiIsInR5cGUiOiJhZG1pbiIsImlhdCI6MTc1MTg5NzM1MiwiZXhwIjoxNzU0NDg5MzUyfQ.1nESAPUbg2huJpsFsu-xPWtp6KK19SHoZJ0aGAdxpcU net::ERR_FAILED 429 (Too Many Requests)
notificationService.js:36 🔗 Attempting SSE connection to: http://localhost:3000/api/notifications/stream?token=***
requests:1  Access to resource at 'http://localhost:3000/api/notifications/stream?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MzIsInVzZXJuYW1lIjoiYWRtaW4xMjM0NSIsInJvbGUiOiJhZG1pbiIsInR5cGUiOiJhZG1pbiIsImlhdCI6MTc1MTg5NzM1MiwiZXhwIjoxNzU0NDg5MzUyfQ.1nESAPUbg2huJpsFsu-xPWtp6KK19SHoZJ0aGAdxpcU' from origin 'http://localhost:8081' has been blocked by CORS policy: No 'Access-Control-Allow-Origin' header is present on the requested resource.
notificationService.js:58  ❌ SSE connection error: Event {isTrusted: true, type: 'error', target: EventSource, currentTarget: EventSource, eventPhase: 2, …}
eventSource.onerror @ notificationService.js:58
notificationService.js:59  EventSource readyState: 2
eventSource.onerror @ notificationService.js:59
AdminNotifications.vue:245  Notification stream error: Event {isTrusted: true, type: 'error', target: EventSource, currentTarget: EventSource, eventPhase: 2, …}
onError @ AdminNotifications.vue:245
eval @ notificationService.js:191
emit @ notificationService.js:189
eventSource.onerror @ notificationService.js:61
AdminHeader.vue:233  Notification error: Connection to notification stream failed
handleNotificationError @ AdminHeader.vue:233
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
emit @ runtime-core.esm-bundler.js:6439
onError @ AdminNotifications.vue:246
eval @ notificationService.js:191
emit @ notificationService.js:189
eventSource.onerror @ notificationService.js:61
notificationService.js:99 Attempting to reconnect in 16000ms (attempt 5/5)
:3000/api/notifications/stream?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MzIsInVzZXJuYW1lIjoiYWRtaW4xMjM0NSIsInJvbGUiOiJhZG1pbiIsInR5cGUiOiJhZG1pbiIsImlhdCI6MTc1MTg5NzM1MiwiZXhwIjoxNzU0NDg5MzUyfQ.1nESAPUbg2huJpsFsu-xPWtp6KK19SHoZJ0aGAdxpcU:1   GET http://localhost:3000/api/notifications/stream?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MzIsInVzZXJuYW1lIjoiYWRtaW4xMjM0NSIsInJvbGUiOiJhZG1pbiIsInR5cGUiOiJhZG1pbiIsImlhdCI6MTc1MTg5NzM1MiwiZXhwIjoxNzU0NDg5MzUyfQ.1nESAPUbg2huJpsFsu-xPWtp6KK19SHoZJ0aGAdxpcU net::ERR_FAILED 429 (Too Many Requests)
