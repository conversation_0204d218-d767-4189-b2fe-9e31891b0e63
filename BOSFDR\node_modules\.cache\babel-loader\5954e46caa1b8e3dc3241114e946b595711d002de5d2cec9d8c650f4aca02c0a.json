{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.set.difference.v2.js\";\nimport \"core-js/modules/es.set.intersection.v2.js\";\nimport \"core-js/modules/es.set.is-disjoint-from.v2.js\";\nimport \"core-js/modules/es.set.is-subset-of.v2.js\";\nimport \"core-js/modules/es.set.is-superset-of.v2.js\";\nimport \"core-js/modules/es.set.symmetric-difference.v2.js\";\nimport \"core-js/modules/es.set.union.v2.js\";\nimport axios from 'axios';\nclass NotificationService {\n  constructor() {\n    this.eventSource = null;\n    this.listeners = new Map();\n    this.isConnected = false;\n    this.reconnectAttempts = 0;\n    this.maxReconnectAttempts = 5;\n    this.reconnectDelay = 1000; // Start with 1 second\n    this.maxReconnectDelay = 30000; // Max 30 seconds\n    this.baseURL = process.env.VUE_APP_API_URL || 'http://localhost:3000/api';\n    this.connectionRefs = 0; // Track how many components are using the connection\n    this.currentUserType = null; // Track current connection type\n    this.isConnecting = false; // Prevent multiple simultaneous connection attempts\n  }\n\n  /**\n   * Add a reference to the connection (called by components)\n   */\n  addConnectionRef(userType = 'admin') {\n    this.connectionRefs++;\n    console.log(`📊 Connection refs: ${this.connectionRefs} (added by ${userType})`);\n\n    // Connect if not already connected\n    if (!this.isConnected && !this.eventSource) {\n      this.currentUserType = userType;\n      return this.connect(userType);\n    }\n    return Promise.resolve();\n  }\n\n  /**\n   * Remove a reference to the connection (called by components on unmount)\n   */\n  removeConnectionRef() {\n    if (this.connectionRefs > 0) {\n      this.connectionRefs--;\n      console.log(`📊 Connection refs: ${this.connectionRefs} (removed)`);\n    }\n\n    // Only disconnect if no components are using the connection\n    if (this.connectionRefs === 0) {\n      console.log('🔌 No more refs, disconnecting...');\n      this.disconnect();\n    }\n  }\n\n  /**\n   * Connect to SSE stream (singleton pattern)\n   */\n  connect(userType = 'admin') {\n    // If already connected or connecting, just return\n    if (this.eventSource && (this.isConnected || this.eventSource.readyState === EventSource.CONNECTING)) {\n      console.log('Already connected or connecting to notification stream');\n      return Promise.resolve();\n    }\n\n    // Prevent multiple simultaneous connection attempts\n    if (this.isConnecting) {\n      console.log('Connection attempt already in progress');\n      return Promise.resolve();\n    }\n    this.isConnecting = true;\n\n    // If there's an existing connection in a bad state, clean it up first\n    if (this.eventSource) {\n      console.log('Cleaning up existing connection before reconnecting');\n      this.eventSource.close();\n      this.eventSource = null;\n      this.isConnected = false;\n    }\n    return new Promise((resolve, reject) => {\n      try {\n        const token = userType === 'admin' ? localStorage.getItem('adminToken') : localStorage.getItem('clientToken');\n        if (!token) {\n          console.error('No authentication token found');\n          reject(new Error('No authentication token found'));\n          return;\n        }\n\n        // EventSource doesn't support custom headers, so we pass the token as a query parameter\n        const url = `${this.baseURL}/notifications/stream?token=${encodeURIComponent(token)}`;\n        console.log('🔗 Attempting SSE connection to:', url.replace(/token=[^&]+/, 'token=***'));\n        this.eventSource = new EventSource(url);\n        this.eventSource.onopen = () => {\n          console.log('✅ Connected to notification stream');\n          console.log('📊 EventSource readyState:', this.eventSource.readyState);\n          console.log('📊 Connection refs:', this.connectionRefs);\n          this.isConnected = true;\n          this.isConnecting = false; // Connection successful\n          this.reconnectAttempts = 0;\n          this.reconnectDelay = 1000;\n          this.emit('connected');\n          resolve();\n        };\n        this.eventSource.onmessage = event => {\n          try {\n            const notification = JSON.parse(event.data);\n            console.log('📨 Received notification:', notification);\n            this.handleNotification(notification);\n          } catch (error) {\n            console.error('Failed to parse notification:', error);\n          }\n        };\n        this.eventSource.onerror = error => {\n          console.error('❌ SSE connection error:', error);\n          console.error('📊 EventSource readyState:', this.eventSource?.readyState);\n          console.error('📊 Connection refs:', this.connectionRefs);\n          this.isConnected = false;\n          this.emit('error', error);\n\n          // Only reject if this is the initial connection attempt\n          if (this.reconnectAttempts === 0) {\n            reject(error);\n          }\n\n          // Attempt to reconnect only if we still have refs\n          if (this.connectionRefs > 0) {\n            this.scheduleReconnect();\n          }\n        };\n      } catch (error) {\n        console.error('Failed to establish SSE connection:', error);\n        reject(error);\n        this.scheduleReconnect();\n      }\n    });\n  }\n\n  /**\n   * Disconnect from SSE stream\n   */\n  disconnect() {\n    console.log('🔌 disconnect() called');\n    console.log('📊 Connection refs:', this.connectionRefs);\n    console.log('📊 Stack trace:', new Error().stack);\n    if (this.eventSource) {\n      console.log('🔌 Closing EventSource connection');\n      this.eventSource.close();\n      this.eventSource = null;\n      this.isConnected = false;\n      this.connectionRefs = 0; // Reset refs when manually disconnecting\n      console.log('Disconnected from notification stream');\n      this.emit('disconnected');\n    }\n  }\n\n  /**\n   * Schedule reconnection attempt\n   */\n  scheduleReconnect() {\n    if (this.reconnectAttempts >= this.maxReconnectAttempts) {\n      console.error('Max reconnection attempts reached');\n      this.emit('max_reconnect_attempts');\n      return;\n    }\n    this.reconnectAttempts++;\n    const delay = Math.min(this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1), this.maxReconnectDelay);\n    console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);\n    setTimeout(() => {\n      if (!this.isConnected) {\n        this.connect();\n      }\n    }, delay);\n  }\n\n  /**\n   * Handle incoming notification\n   */\n  handleNotification(notification) {\n    console.log('📢 Received notification:', notification);\n\n    // Emit to specific type listeners\n    this.emit(notification.type, notification);\n\n    // Emit to general notification listeners\n    this.emit('notification', notification);\n\n    // Show browser notification if permission granted\n    this.showBrowserNotification(notification);\n  }\n\n  /**\n   * Show browser notification\n   */\n  showBrowserNotification(notification) {\n    if ('Notification' in window && Notification.permission === 'granted') {\n      const options = {\n        body: notification.message,\n        icon: '/favicon.ico',\n        badge: '/favicon.ico',\n        tag: `notification-${notification.id}`,\n        requireInteraction: notification.priority === 'high' || notification.priority === 'urgent'\n      };\n      const browserNotification = new Notification(notification.title, options);\n      browserNotification.onclick = () => {\n        window.focus();\n        this.emit('notification_click', notification);\n        browserNotification.close();\n      };\n\n      // Auto close after 5 seconds for normal priority\n      if (notification.priority !== 'high' && notification.priority !== 'urgent') {\n        setTimeout(() => {\n          browserNotification.close();\n        }, 5000);\n      }\n    }\n  }\n\n  /**\n   * Request browser notification permission\n   */\n  async requestNotificationPermission() {\n    if ('Notification' in window) {\n      const permission = await Notification.requestPermission();\n      return permission === 'granted';\n    }\n    return false;\n  }\n\n  /**\n   * Subscribe to notification events\n   */\n  on(event, callback) {\n    if (!this.listeners.has(event)) {\n      this.listeners.set(event, new Set());\n    }\n    this.listeners.get(event).add(callback);\n  }\n\n  /**\n   * Unsubscribe from notification events\n   */\n  off(event, callback) {\n    if (this.listeners.has(event)) {\n      this.listeners.get(event).delete(callback);\n    }\n  }\n\n  /**\n   * Emit event to listeners\n   */\n  emit(event, data = null) {\n    if (this.listeners.has(event)) {\n      this.listeners.get(event).forEach(callback => {\n        try {\n          callback(data);\n        } catch (error) {\n          console.error(`Error in notification listener for ${event}:`, error);\n        }\n      });\n    }\n  }\n\n  /**\n   * Get user notifications\n   */\n  async getNotifications(page = 1, limit = 20, unreadOnly = false) {\n    try {\n      const token = localStorage.getItem('adminToken') || localStorage.getItem('clientToken');\n      const response = await axios.get(`${this.baseURL}/notifications`, {\n        params: {\n          page,\n          limit,\n          unread_only: unreadOnly\n        },\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Failed to get notifications:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get unread notification count\n   */\n  async getUnreadCount() {\n    try {\n      const token = localStorage.getItem('adminToken') || localStorage.getItem('clientToken');\n      const response = await axios.get(`${this.baseURL}/notifications/unread-count`, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      return response.data.data.count;\n    } catch (error) {\n      console.error('Failed to get unread count:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Mark notification as read\n   */\n  async markAsRead(notificationId) {\n    try {\n      const token = localStorage.getItem('adminToken') || localStorage.getItem('clientToken');\n      const response = await axios.put(`${this.baseURL}/notifications/${notificationId}/read`, {}, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Failed to mark notification as read:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Mark all notifications as read\n   */\n  async markAllAsRead() {\n    try {\n      const token = localStorage.getItem('adminToken') || localStorage.getItem('clientToken');\n      const response = await axios.put(`${this.baseURL}/notifications/mark-all-read`, {}, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Failed to mark all notifications as read:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Send test notification (admin only)\n   */\n  async sendTestNotification(data) {\n    try {\n      const token = localStorage.getItem('adminToken');\n      const response = await axios.post(`${this.baseURL}/notifications/test`, data, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Failed to send test notification:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get notification statistics (admin only)\n   */\n  async getStatistics() {\n    try {\n      const token = localStorage.getItem('adminToken');\n      const response = await axios.get(`${this.baseURL}/notifications/statistics`, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Failed to get notification statistics:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Clean up old notifications (admin only)\n   */\n  async cleanupOldNotifications(days = 90) {\n    try {\n      const token = localStorage.getItem('adminToken');\n      const response = await axios.delete(`${this.baseURL}/notifications/cleanup`, {\n        params: {\n          days\n        },\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Failed to cleanup old notifications:', error);\n      throw error;\n    }\n  }\n}\n\n// Create singleton instance\nconst notificationService = new NotificationService();\nexport default notificationService;", "map": {"version": 3, "names": ["axios", "NotificationService", "constructor", "eventSource", "listeners", "Map", "isConnected", "reconnectAttempts", "maxReconnectAttempts", "reconnectDelay", "maxReconnectDelay", "baseURL", "process", "env", "VUE_APP_API_URL", "connectionRefs", "currentUserType", "isConnecting", "addConnectionRef", "userType", "console", "log", "connect", "Promise", "resolve", "removeConnectionRef", "disconnect", "readyState", "EventSource", "CONNECTING", "close", "reject", "token", "localStorage", "getItem", "error", "Error", "url", "encodeURIComponent", "replace", "onopen", "emit", "onmessage", "event", "notification", "JSON", "parse", "data", "handleNotification", "onerror", "scheduleReconnect", "stack", "delay", "Math", "min", "pow", "setTimeout", "type", "showBrowserNotification", "window", "Notification", "permission", "options", "body", "message", "icon", "badge", "tag", "id", "requireInteraction", "priority", "browserNotification", "title", "onclick", "focus", "requestNotificationPermission", "requestPermission", "on", "callback", "has", "set", "Set", "get", "add", "off", "delete", "for<PERSON>ach", "getNotifications", "page", "limit", "unreadOnly", "response", "params", "unread_only", "headers", "getUnreadCount", "count", "mark<PERSON><PERSON><PERSON>", "notificationId", "put", "markAllAsRead", "sendTestNotification", "post", "getStatistics", "cleanupOldNotifications", "days", "notificationService"], "sources": ["D:/rhai_front_and_back/BOSFDR/src/services/notificationService.js"], "sourcesContent": ["import axios from 'axios';\n\nclass NotificationService {\n  constructor() {\n    this.eventSource = null;\n    this.listeners = new Map();\n    this.isConnected = false;\n    this.reconnectAttempts = 0;\n    this.maxReconnectAttempts = 5;\n    this.reconnectDelay = 1000; // Start with 1 second\n    this.maxReconnectDelay = 30000; // Max 30 seconds\n    this.baseURL = process.env.VUE_APP_API_URL || 'http://localhost:3000/api';\n    this.connectionRefs = 0; // Track how many components are using the connection\n    this.currentUserType = null; // Track current connection type\n    this.isConnecting = false; // Prevent multiple simultaneous connection attempts\n  }\n\n  /**\n   * Add a reference to the connection (called by components)\n   */\n  addConnectionRef(userType = 'admin') {\n    this.connectionRefs++;\n    console.log(`📊 Connection refs: ${this.connectionRefs} (added by ${userType})`);\n\n    // Connect if not already connected\n    if (!this.isConnected && !this.eventSource) {\n      this.currentUserType = userType;\n      return this.connect(userType);\n    }\n\n    return Promise.resolve();\n  }\n\n  /**\n   * Remove a reference to the connection (called by components on unmount)\n   */\n  removeConnectionRef() {\n    if (this.connectionRefs > 0) {\n      this.connectionRefs--;\n      console.log(`📊 Connection refs: ${this.connectionRefs} (removed)`);\n    }\n\n    // Only disconnect if no components are using the connection\n    if (this.connectionRefs === 0) {\n      console.log('🔌 No more refs, disconnecting...');\n      this.disconnect();\n    }\n  }\n\n  /**\n   * Connect to SSE stream (singleton pattern)\n   */\n  connect(userType = 'admin') {\n    // If already connected or connecting, just return\n    if (this.eventSource && (this.isConnected || this.eventSource.readyState === EventSource.CONNECTING)) {\n      console.log('Already connected or connecting to notification stream');\n      return Promise.resolve();\n    }\n\n    // Prevent multiple simultaneous connection attempts\n    if (this.isConnecting) {\n      console.log('Connection attempt already in progress');\n      return Promise.resolve();\n    }\n\n    this.isConnecting = true;\n\n    // If there's an existing connection in a bad state, clean it up first\n    if (this.eventSource) {\n      console.log('Cleaning up existing connection before reconnecting');\n      this.eventSource.close();\n      this.eventSource = null;\n      this.isConnected = false;\n    }\n\n    return new Promise((resolve, reject) => {\n      try {\n        const token = userType === 'admin'\n          ? localStorage.getItem('adminToken')\n          : localStorage.getItem('clientToken');\n\n        if (!token) {\n          console.error('No authentication token found');\n          reject(new Error('No authentication token found'));\n          return;\n        }\n\n        // EventSource doesn't support custom headers, so we pass the token as a query parameter\n        const url = `${this.baseURL}/notifications/stream?token=${encodeURIComponent(token)}`;\n        console.log('🔗 Attempting SSE connection to:', url.replace(/token=[^&]+/, 'token=***'));\n\n        this.eventSource = new EventSource(url);\n\n        this.eventSource.onopen = () => {\n          console.log('✅ Connected to notification stream');\n          console.log('📊 EventSource readyState:', this.eventSource.readyState);\n          console.log('📊 Connection refs:', this.connectionRefs);\n          this.isConnected = true;\n          this.isConnecting = false; // Connection successful\n          this.reconnectAttempts = 0;\n          this.reconnectDelay = 1000;\n          this.emit('connected');\n          resolve();\n        };\n\n        this.eventSource.onmessage = (event) => {\n          try {\n            const notification = JSON.parse(event.data);\n            console.log('📨 Received notification:', notification);\n            this.handleNotification(notification);\n          } catch (error) {\n            console.error('Failed to parse notification:', error);\n          }\n        };\n\n        this.eventSource.onerror = (error) => {\n          console.error('❌ SSE connection error:', error);\n          console.error('📊 EventSource readyState:', this.eventSource?.readyState);\n          console.error('📊 Connection refs:', this.connectionRefs);\n          this.isConnected = false;\n          this.emit('error', error);\n\n          // Only reject if this is the initial connection attempt\n          if (this.reconnectAttempts === 0) {\n            reject(error);\n          }\n\n          // Attempt to reconnect only if we still have refs\n          if (this.connectionRefs > 0) {\n            this.scheduleReconnect();\n          }\n        };\n\n      } catch (error) {\n        console.error('Failed to establish SSE connection:', error);\n        reject(error);\n        this.scheduleReconnect();\n      }\n    });\n  }\n\n  /**\n   * Disconnect from SSE stream\n   */\n  disconnect() {\n    console.log('🔌 disconnect() called');\n    console.log('📊 Connection refs:', this.connectionRefs);\n    console.log('📊 Stack trace:', new Error().stack);\n\n    if (this.eventSource) {\n      console.log('🔌 Closing EventSource connection');\n      this.eventSource.close();\n      this.eventSource = null;\n      this.isConnected = false;\n      this.connectionRefs = 0; // Reset refs when manually disconnecting\n      console.log('Disconnected from notification stream');\n      this.emit('disconnected');\n    }\n  }\n\n  /**\n   * Schedule reconnection attempt\n   */\n  scheduleReconnect() {\n    if (this.reconnectAttempts >= this.maxReconnectAttempts) {\n      console.error('Max reconnection attempts reached');\n      this.emit('max_reconnect_attempts');\n      return;\n    }\n\n    this.reconnectAttempts++;\n    const delay = Math.min(this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1), this.maxReconnectDelay);\n    \n    console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);\n    \n    setTimeout(() => {\n      if (!this.isConnected) {\n        this.connect();\n      }\n    }, delay);\n  }\n\n  /**\n   * Handle incoming notification\n   */\n  handleNotification(notification) {\n    console.log('📢 Received notification:', notification);\n    \n    // Emit to specific type listeners\n    this.emit(notification.type, notification);\n    \n    // Emit to general notification listeners\n    this.emit('notification', notification);\n    \n    // Show browser notification if permission granted\n    this.showBrowserNotification(notification);\n  }\n\n  /**\n   * Show browser notification\n   */\n  showBrowserNotification(notification) {\n    if ('Notification' in window && Notification.permission === 'granted') {\n      const options = {\n        body: notification.message,\n        icon: '/favicon.ico',\n        badge: '/favicon.ico',\n        tag: `notification-${notification.id}`,\n        requireInteraction: notification.priority === 'high' || notification.priority === 'urgent'\n      };\n\n      const browserNotification = new Notification(notification.title, options);\n      \n      browserNotification.onclick = () => {\n        window.focus();\n        this.emit('notification_click', notification);\n        browserNotification.close();\n      };\n\n      // Auto close after 5 seconds for normal priority\n      if (notification.priority !== 'high' && notification.priority !== 'urgent') {\n        setTimeout(() => {\n          browserNotification.close();\n        }, 5000);\n      }\n    }\n  }\n\n  /**\n   * Request browser notification permission\n   */\n  async requestNotificationPermission() {\n    if ('Notification' in window) {\n      const permission = await Notification.requestPermission();\n      return permission === 'granted';\n    }\n    return false;\n  }\n\n  /**\n   * Subscribe to notification events\n   */\n  on(event, callback) {\n    if (!this.listeners.has(event)) {\n      this.listeners.set(event, new Set());\n    }\n    this.listeners.get(event).add(callback);\n  }\n\n  /**\n   * Unsubscribe from notification events\n   */\n  off(event, callback) {\n    if (this.listeners.has(event)) {\n      this.listeners.get(event).delete(callback);\n    }\n  }\n\n  /**\n   * Emit event to listeners\n   */\n  emit(event, data = null) {\n    if (this.listeners.has(event)) {\n      this.listeners.get(event).forEach(callback => {\n        try {\n          callback(data);\n        } catch (error) {\n          console.error(`Error in notification listener for ${event}:`, error);\n        }\n      });\n    }\n  }\n\n  /**\n   * Get user notifications\n   */\n  async getNotifications(page = 1, limit = 20, unreadOnly = false) {\n    try {\n      const token = localStorage.getItem('adminToken') || localStorage.getItem('clientToken');\n      const response = await axios.get(`${this.baseURL}/notifications`, {\n        params: { page, limit, unread_only: unreadOnly },\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Failed to get notifications:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get unread notification count\n   */\n  async getUnreadCount() {\n    try {\n      const token = localStorage.getItem('adminToken') || localStorage.getItem('clientToken');\n      const response = await axios.get(`${this.baseURL}/notifications/unread-count`, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      return response.data.data.count;\n    } catch (error) {\n      console.error('Failed to get unread count:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Mark notification as read\n   */\n  async markAsRead(notificationId) {\n    try {\n      const token = localStorage.getItem('adminToken') || localStorage.getItem('clientToken');\n      const response = await axios.put(`${this.baseURL}/notifications/${notificationId}/read`, {}, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Failed to mark notification as read:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Mark all notifications as read\n   */\n  async markAllAsRead() {\n    try {\n      const token = localStorage.getItem('adminToken') || localStorage.getItem('clientToken');\n      const response = await axios.put(`${this.baseURL}/notifications/mark-all-read`, {}, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Failed to mark all notifications as read:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Send test notification (admin only)\n   */\n  async sendTestNotification(data) {\n    try {\n      const token = localStorage.getItem('adminToken');\n      const response = await axios.post(`${this.baseURL}/notifications/test`, data, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Failed to send test notification:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get notification statistics (admin only)\n   */\n  async getStatistics() {\n    try {\n      const token = localStorage.getItem('adminToken');\n      const response = await axios.get(`${this.baseURL}/notifications/statistics`, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Failed to get notification statistics:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Clean up old notifications (admin only)\n   */\n  async cleanupOldNotifications(days = 90) {\n    try {\n      const token = localStorage.getItem('adminToken');\n      const response = await axios.delete(`${this.baseURL}/notifications/cleanup`, {\n        params: { days },\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Failed to cleanup old notifications:', error);\n      throw error;\n    }\n  }\n}\n\n// Create singleton instance\nconst notificationService = new NotificationService();\n\nexport default notificationService;\n"], "mappings": ";;;;;;;;;AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,mBAAmB,CAAC;EACxBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,WAAW,GAAG,IAAI;IACvB,IAAI,CAACC,SAAS,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC1B,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,iBAAiB,GAAG,CAAC;IAC1B,IAAI,CAACC,oBAAoB,GAAG,CAAC;IAC7B,IAAI,CAACC,cAAc,GAAG,IAAI,CAAC,CAAC;IAC5B,IAAI,CAACC,iBAAiB,GAAG,KAAK,CAAC,CAAC;IAChC,IAAI,CAACC,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,eAAe,IAAI,2BAA2B;IACzE,IAAI,CAACC,cAAc,GAAG,CAAC,CAAC,CAAC;IACzB,IAAI,CAACC,eAAe,GAAG,IAAI,CAAC,CAAC;IAC7B,IAAI,CAACC,YAAY,GAAG,KAAK,CAAC,CAAC;EAC7B;;EAEA;AACF;AACA;EACEC,gBAAgBA,CAACC,QAAQ,GAAG,OAAO,EAAE;IACnC,IAAI,CAACJ,cAAc,EAAE;IACrBK,OAAO,CAACC,GAAG,CAAC,uBAAuB,IAAI,CAACN,cAAc,cAAcI,QAAQ,GAAG,CAAC;;IAEhF;IACA,IAAI,CAAC,IAAI,CAACb,WAAW,IAAI,CAAC,IAAI,CAACH,WAAW,EAAE;MAC1C,IAAI,CAACa,eAAe,GAAGG,QAAQ;MAC/B,OAAO,IAAI,CAACG,OAAO,CAACH,QAAQ,CAAC;IAC/B;IAEA,OAAOI,OAAO,CAACC,OAAO,CAAC,CAAC;EAC1B;;EAEA;AACF;AACA;EACEC,mBAAmBA,CAAA,EAAG;IACpB,IAAI,IAAI,CAACV,cAAc,GAAG,CAAC,EAAE;MAC3B,IAAI,CAACA,cAAc,EAAE;MACrBK,OAAO,CAACC,GAAG,CAAC,uBAAuB,IAAI,CAACN,cAAc,YAAY,CAAC;IACrE;;IAEA;IACA,IAAI,IAAI,CAACA,cAAc,KAAK,CAAC,EAAE;MAC7BK,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;MAChD,IAAI,CAACK,UAAU,CAAC,CAAC;IACnB;EACF;;EAEA;AACF;AACA;EACEJ,OAAOA,CAACH,QAAQ,GAAG,OAAO,EAAE;IAC1B;IACA,IAAI,IAAI,CAAChB,WAAW,KAAK,IAAI,CAACG,WAAW,IAAI,IAAI,CAACH,WAAW,CAACwB,UAAU,KAAKC,WAAW,CAACC,UAAU,CAAC,EAAE;MACpGT,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;MACrE,OAAOE,OAAO,CAACC,OAAO,CAAC,CAAC;IAC1B;;IAEA;IACA,IAAI,IAAI,CAACP,YAAY,EAAE;MACrBG,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;MACrD,OAAOE,OAAO,CAACC,OAAO,CAAC,CAAC;IAC1B;IAEA,IAAI,CAACP,YAAY,GAAG,IAAI;;IAExB;IACA,IAAI,IAAI,CAACd,WAAW,EAAE;MACpBiB,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;MAClE,IAAI,CAAClB,WAAW,CAAC2B,KAAK,CAAC,CAAC;MACxB,IAAI,CAAC3B,WAAW,GAAG,IAAI;MACvB,IAAI,CAACG,WAAW,GAAG,KAAK;IAC1B;IAEA,OAAO,IAAIiB,OAAO,CAAC,CAACC,OAAO,EAAEO,MAAM,KAAK;MACtC,IAAI;QACF,MAAMC,KAAK,GAAGb,QAAQ,KAAK,OAAO,GAC9Bc,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,GAClCD,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;QAEvC,IAAI,CAACF,KAAK,EAAE;UACVZ,OAAO,CAACe,KAAK,CAAC,+BAA+B,CAAC;UAC9CJ,MAAM,CAAC,IAAIK,KAAK,CAAC,+BAA+B,CAAC,CAAC;UAClD;QACF;;QAEA;QACA,MAAMC,GAAG,GAAG,GAAG,IAAI,CAAC1B,OAAO,+BAA+B2B,kBAAkB,CAACN,KAAK,CAAC,EAAE;QACrFZ,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEgB,GAAG,CAACE,OAAO,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;QAExF,IAAI,CAACpC,WAAW,GAAG,IAAIyB,WAAW,CAACS,GAAG,CAAC;QAEvC,IAAI,CAAClC,WAAW,CAACqC,MAAM,GAAG,MAAM;UAC9BpB,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;UACjDD,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAAClB,WAAW,CAACwB,UAAU,CAAC;UACtEP,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE,IAAI,CAACN,cAAc,CAAC;UACvD,IAAI,CAACT,WAAW,GAAG,IAAI;UACvB,IAAI,CAACW,YAAY,GAAG,KAAK,CAAC,CAAC;UAC3B,IAAI,CAACV,iBAAiB,GAAG,CAAC;UAC1B,IAAI,CAACE,cAAc,GAAG,IAAI;UAC1B,IAAI,CAACgC,IAAI,CAAC,WAAW,CAAC;UACtBjB,OAAO,CAAC,CAAC;QACX,CAAC;QAED,IAAI,CAACrB,WAAW,CAACuC,SAAS,GAAIC,KAAK,IAAK;UACtC,IAAI;YACF,MAAMC,YAAY,GAAGC,IAAI,CAACC,KAAK,CAACH,KAAK,CAACI,IAAI,CAAC;YAC3C3B,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEuB,YAAY,CAAC;YACtD,IAAI,CAACI,kBAAkB,CAACJ,YAAY,CAAC;UACvC,CAAC,CAAC,OAAOT,KAAK,EAAE;YACdf,OAAO,CAACe,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;UACvD;QACF,CAAC;QAED,IAAI,CAAChC,WAAW,CAAC8C,OAAO,GAAId,KAAK,IAAK;UACpCf,OAAO,CAACe,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;UAC/Cf,OAAO,CAACe,KAAK,CAAC,4BAA4B,EAAE,IAAI,CAAChC,WAAW,EAAEwB,UAAU,CAAC;UACzEP,OAAO,CAACe,KAAK,CAAC,qBAAqB,EAAE,IAAI,CAACpB,cAAc,CAAC;UACzD,IAAI,CAACT,WAAW,GAAG,KAAK;UACxB,IAAI,CAACmC,IAAI,CAAC,OAAO,EAAEN,KAAK,CAAC;;UAEzB;UACA,IAAI,IAAI,CAAC5B,iBAAiB,KAAK,CAAC,EAAE;YAChCwB,MAAM,CAACI,KAAK,CAAC;UACf;;UAEA;UACA,IAAI,IAAI,CAACpB,cAAc,GAAG,CAAC,EAAE;YAC3B,IAAI,CAACmC,iBAAiB,CAAC,CAAC;UAC1B;QACF,CAAC;MAEH,CAAC,CAAC,OAAOf,KAAK,EAAE;QACdf,OAAO,CAACe,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;QAC3DJ,MAAM,CAACI,KAAK,CAAC;QACb,IAAI,CAACe,iBAAiB,CAAC,CAAC;MAC1B;IACF,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;EACExB,UAAUA,CAAA,EAAG;IACXN,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;IACrCD,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE,IAAI,CAACN,cAAc,CAAC;IACvDK,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE,IAAIe,KAAK,CAAC,CAAC,CAACe,KAAK,CAAC;IAEjD,IAAI,IAAI,CAAChD,WAAW,EAAE;MACpBiB,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;MAChD,IAAI,CAAClB,WAAW,CAAC2B,KAAK,CAAC,CAAC;MACxB,IAAI,CAAC3B,WAAW,GAAG,IAAI;MACvB,IAAI,CAACG,WAAW,GAAG,KAAK;MACxB,IAAI,CAACS,cAAc,GAAG,CAAC,CAAC,CAAC;MACzBK,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;MACpD,IAAI,CAACoB,IAAI,CAAC,cAAc,CAAC;IAC3B;EACF;;EAEA;AACF;AACA;EACES,iBAAiBA,CAAA,EAAG;IAClB,IAAI,IAAI,CAAC3C,iBAAiB,IAAI,IAAI,CAACC,oBAAoB,EAAE;MACvDY,OAAO,CAACe,KAAK,CAAC,mCAAmC,CAAC;MAClD,IAAI,CAACM,IAAI,CAAC,wBAAwB,CAAC;MACnC;IACF;IAEA,IAAI,CAAClC,iBAAiB,EAAE;IACxB,MAAM6C,KAAK,GAAGC,IAAI,CAACC,GAAG,CAAC,IAAI,CAAC7C,cAAc,GAAG4C,IAAI,CAACE,GAAG,CAAC,CAAC,EAAE,IAAI,CAAChD,iBAAiB,GAAG,CAAC,CAAC,EAAE,IAAI,CAACG,iBAAiB,CAAC;IAE7GU,OAAO,CAACC,GAAG,CAAC,8BAA8B+B,KAAK,eAAe,IAAI,CAAC7C,iBAAiB,IAAI,IAAI,CAACC,oBAAoB,GAAG,CAAC;IAErHgD,UAAU,CAAC,MAAM;MACf,IAAI,CAAC,IAAI,CAAClD,WAAW,EAAE;QACrB,IAAI,CAACgB,OAAO,CAAC,CAAC;MAChB;IACF,CAAC,EAAE8B,KAAK,CAAC;EACX;;EAEA;AACF;AACA;EACEJ,kBAAkBA,CAACJ,YAAY,EAAE;IAC/BxB,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEuB,YAAY,CAAC;;IAEtD;IACA,IAAI,CAACH,IAAI,CAACG,YAAY,CAACa,IAAI,EAAEb,YAAY,CAAC;;IAE1C;IACA,IAAI,CAACH,IAAI,CAAC,cAAc,EAAEG,YAAY,CAAC;;IAEvC;IACA,IAAI,CAACc,uBAAuB,CAACd,YAAY,CAAC;EAC5C;;EAEA;AACF;AACA;EACEc,uBAAuBA,CAACd,YAAY,EAAE;IACpC,IAAI,cAAc,IAAIe,MAAM,IAAIC,YAAY,CAACC,UAAU,KAAK,SAAS,EAAE;MACrE,MAAMC,OAAO,GAAG;QACdC,IAAI,EAAEnB,YAAY,CAACoB,OAAO;QAC1BC,IAAI,EAAE,cAAc;QACpBC,KAAK,EAAE,cAAc;QACrBC,GAAG,EAAE,gBAAgBvB,YAAY,CAACwB,EAAE,EAAE;QACtCC,kBAAkB,EAAEzB,YAAY,CAAC0B,QAAQ,KAAK,MAAM,IAAI1B,YAAY,CAAC0B,QAAQ,KAAK;MACpF,CAAC;MAED,MAAMC,mBAAmB,GAAG,IAAIX,YAAY,CAAChB,YAAY,CAAC4B,KAAK,EAAEV,OAAO,CAAC;MAEzES,mBAAmB,CAACE,OAAO,GAAG,MAAM;QAClCd,MAAM,CAACe,KAAK,CAAC,CAAC;QACd,IAAI,CAACjC,IAAI,CAAC,oBAAoB,EAAEG,YAAY,CAAC;QAC7C2B,mBAAmB,CAACzC,KAAK,CAAC,CAAC;MAC7B,CAAC;;MAED;MACA,IAAIc,YAAY,CAAC0B,QAAQ,KAAK,MAAM,IAAI1B,YAAY,CAAC0B,QAAQ,KAAK,QAAQ,EAAE;QAC1Ed,UAAU,CAAC,MAAM;UACfe,mBAAmB,CAACzC,KAAK,CAAC,CAAC;QAC7B,CAAC,EAAE,IAAI,CAAC;MACV;IACF;EACF;;EAEA;AACF;AACA;EACE,MAAM6C,6BAA6BA,CAAA,EAAG;IACpC,IAAI,cAAc,IAAIhB,MAAM,EAAE;MAC5B,MAAME,UAAU,GAAG,MAAMD,YAAY,CAACgB,iBAAiB,CAAC,CAAC;MACzD,OAAOf,UAAU,KAAK,SAAS;IACjC;IACA,OAAO,KAAK;EACd;;EAEA;AACF;AACA;EACEgB,EAAEA,CAAClC,KAAK,EAAEmC,QAAQ,EAAE;IAClB,IAAI,CAAC,IAAI,CAAC1E,SAAS,CAAC2E,GAAG,CAACpC,KAAK,CAAC,EAAE;MAC9B,IAAI,CAACvC,SAAS,CAAC4E,GAAG,CAACrC,KAAK,EAAE,IAAIsC,GAAG,CAAC,CAAC,CAAC;IACtC;IACA,IAAI,CAAC7E,SAAS,CAAC8E,GAAG,CAACvC,KAAK,CAAC,CAACwC,GAAG,CAACL,QAAQ,CAAC;EACzC;;EAEA;AACF;AACA;EACEM,GAAGA,CAACzC,KAAK,EAAEmC,QAAQ,EAAE;IACnB,IAAI,IAAI,CAAC1E,SAAS,CAAC2E,GAAG,CAACpC,KAAK,CAAC,EAAE;MAC7B,IAAI,CAACvC,SAAS,CAAC8E,GAAG,CAACvC,KAAK,CAAC,CAAC0C,MAAM,CAACP,QAAQ,CAAC;IAC5C;EACF;;EAEA;AACF;AACA;EACErC,IAAIA,CAACE,KAAK,EAAEI,IAAI,GAAG,IAAI,EAAE;IACvB,IAAI,IAAI,CAAC3C,SAAS,CAAC2E,GAAG,CAACpC,KAAK,CAAC,EAAE;MAC7B,IAAI,CAACvC,SAAS,CAAC8E,GAAG,CAACvC,KAAK,CAAC,CAAC2C,OAAO,CAACR,QAAQ,IAAI;QAC5C,IAAI;UACFA,QAAQ,CAAC/B,IAAI,CAAC;QAChB,CAAC,CAAC,OAAOZ,KAAK,EAAE;UACdf,OAAO,CAACe,KAAK,CAAC,sCAAsCQ,KAAK,GAAG,EAAER,KAAK,CAAC;QACtE;MACF,CAAC,CAAC;IACJ;EACF;;EAEA;AACF;AACA;EACE,MAAMoD,gBAAgBA,CAACC,IAAI,GAAG,CAAC,EAAEC,KAAK,GAAG,EAAE,EAAEC,UAAU,GAAG,KAAK,EAAE;IAC/D,IAAI;MACF,MAAM1D,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,IAAID,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;MACvF,MAAMyD,QAAQ,GAAG,MAAM3F,KAAK,CAACkF,GAAG,CAAC,GAAG,IAAI,CAACvE,OAAO,gBAAgB,EAAE;QAChEiF,MAAM,EAAE;UAAEJ,IAAI;UAAEC,KAAK;UAAEI,WAAW,EAAEH;QAAW,CAAC;QAChDI,OAAO,EAAE;UACP,eAAe,EAAE,UAAU9D,KAAK;QAClC;MACF,CAAC,CAAC;MACF,OAAO2D,QAAQ,CAAC5C,IAAI;IACtB,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACdf,OAAO,CAACe,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAM4D,cAAcA,CAAA,EAAG;IACrB,IAAI;MACF,MAAM/D,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,IAAID,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;MACvF,MAAMyD,QAAQ,GAAG,MAAM3F,KAAK,CAACkF,GAAG,CAAC,GAAG,IAAI,CAACvE,OAAO,6BAA6B,EAAE;QAC7EmF,OAAO,EAAE;UACP,eAAe,EAAE,UAAU9D,KAAK;QAClC;MACF,CAAC,CAAC;MACF,OAAO2D,QAAQ,CAAC5C,IAAI,CAACA,IAAI,CAACiD,KAAK;IACjC,CAAC,CAAC,OAAO7D,KAAK,EAAE;MACdf,OAAO,CAACe,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAM8D,UAAUA,CAACC,cAAc,EAAE;IAC/B,IAAI;MACF,MAAMlE,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,IAAID,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;MACvF,MAAMyD,QAAQ,GAAG,MAAM3F,KAAK,CAACmG,GAAG,CAAC,GAAG,IAAI,CAACxF,OAAO,kBAAkBuF,cAAc,OAAO,EAAE,CAAC,CAAC,EAAE;QAC3FJ,OAAO,EAAE;UACP,eAAe,EAAE,UAAU9D,KAAK;QAClC;MACF,CAAC,CAAC;MACF,OAAO2D,QAAQ,CAAC5C,IAAI;IACtB,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACdf,OAAO,CAACe,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5D,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMiE,aAAaA,CAAA,EAAG;IACpB,IAAI;MACF,MAAMpE,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,IAAID,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;MACvF,MAAMyD,QAAQ,GAAG,MAAM3F,KAAK,CAACmG,GAAG,CAAC,GAAG,IAAI,CAACxF,OAAO,8BAA8B,EAAE,CAAC,CAAC,EAAE;QAClFmF,OAAO,EAAE;UACP,eAAe,EAAE,UAAU9D,KAAK;QAClC;MACF,CAAC,CAAC;MACF,OAAO2D,QAAQ,CAAC5C,IAAI;IACtB,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACdf,OAAO,CAACe,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;MACjE,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMkE,oBAAoBA,CAACtD,IAAI,EAAE;IAC/B,IAAI;MACF,MAAMf,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;MAChD,MAAMyD,QAAQ,GAAG,MAAM3F,KAAK,CAACsG,IAAI,CAAC,GAAG,IAAI,CAAC3F,OAAO,qBAAqB,EAAEoC,IAAI,EAAE;QAC5E+C,OAAO,EAAE;UACP,eAAe,EAAE,UAAU9D,KAAK;QAClC;MACF,CAAC,CAAC;MACF,OAAO2D,QAAQ,CAAC5C,IAAI;IACtB,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACdf,OAAO,CAACe,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMoE,aAAaA,CAAA,EAAG;IACpB,IAAI;MACF,MAAMvE,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;MAChD,MAAMyD,QAAQ,GAAG,MAAM3F,KAAK,CAACkF,GAAG,CAAC,GAAG,IAAI,CAACvE,OAAO,2BAA2B,EAAE;QAC3EmF,OAAO,EAAE;UACP,eAAe,EAAE,UAAU9D,KAAK;QAClC;MACF,CAAC,CAAC;MACF,OAAO2D,QAAQ,CAAC5C,IAAI;IACtB,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACdf,OAAO,CAACe,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;MAC9D,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMqE,uBAAuBA,CAACC,IAAI,GAAG,EAAE,EAAE;IACvC,IAAI;MACF,MAAMzE,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;MAChD,MAAMyD,QAAQ,GAAG,MAAM3F,KAAK,CAACqF,MAAM,CAAC,GAAG,IAAI,CAAC1E,OAAO,wBAAwB,EAAE;QAC3EiF,MAAM,EAAE;UAAEa;QAAK,CAAC;QAChBX,OAAO,EAAE;UACP,eAAe,EAAE,UAAU9D,KAAK;QAClC;MACF,CAAC,CAAC;MACF,OAAO2D,QAAQ,CAAC5C,IAAI;IACtB,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACdf,OAAO,CAACe,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5D,MAAMA,KAAK;IACb;EACF;AACF;;AAEA;AACA,MAAMuE,mBAAmB,GAAG,IAAIzG,mBAAmB,CAAC,CAAC;AAErD,eAAeyG,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}