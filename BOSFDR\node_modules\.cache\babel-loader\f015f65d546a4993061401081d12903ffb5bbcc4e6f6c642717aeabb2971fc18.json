{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, normalizeClass as _normalizeClass, toDisplayString as _toDisplayString, vModelText as _vModelText, withDirectives as _withDirectives, vModelSelect as _vModelSelect, openBlock as _openBlock, createElementBlock as _createElementBlock, renderList as _renderList, Fragment as _Fragment, vModelRadio as _vModelRadio, vModelCheckbox as _vModelCheckbox, withModifiers as _withModifiers, createStaticVNode as _createStaticVNode } from \"vue\";\nconst _hoisted_1 = {\n  class: \"barangay-clearance-request\"\n};\nconst _hoisted_2 = {\n  class: \"page-header\"\n};\nconst _hoisted_3 = {\n  class: \"header-content\"\n};\nconst _hoisted_4 = {\n  class: \"progress-steps\"\n};\nconst _hoisted_5 = {\n  class: \"form-container\"\n};\nconst _hoisted_6 = {\n  key: 0,\n  class: \"form-step\"\n};\nconst _hoisted_7 = {\n  class: \"profile-preview\"\n};\nconst _hoisted_8 = {\n  class: \"profile-card\"\n};\nconst _hoisted_9 = {\n  class: \"profile-info\"\n};\nconst _hoisted_10 = {\n  class: \"info-grid\"\n};\nconst _hoisted_11 = {\n  class: \"info-item\"\n};\nconst _hoisted_12 = {\n  class: \"info-item\"\n};\nconst _hoisted_13 = {\n  class: \"info-item\"\n};\nconst _hoisted_14 = {\n  class: \"info-item\"\n};\nconst _hoisted_15 = {\n  class: \"profile-actions\"\n};\nconst _hoisted_16 = {\n  class: \"form-section\"\n};\nconst _hoisted_17 = {\n  class: \"form-grid\"\n};\nconst _hoisted_18 = {\n  class: \"form-group\"\n};\nconst _hoisted_19 = {\n  class: \"form-group\"\n};\nconst _hoisted_20 = {\n  class: \"form-group\"\n};\nconst _hoisted_21 = {\n  class: \"form-group\"\n};\nconst _hoisted_22 = {\n  key: 1,\n  class: \"form-step\"\n};\nconst _hoisted_23 = {\n  class: \"form-section\"\n};\nconst _hoisted_24 = {\n  class: \"form-group\"\n};\nconst _hoisted_25 = [\"value\"];\nconst _hoisted_26 = {\n  class: \"form-group\"\n};\nconst _hoisted_27 = {\n  class: \"form-group\"\n};\nconst _hoisted_28 = {\n  class: \"radio-group\"\n};\nconst _hoisted_29 = {\n  class: \"radio-option\"\n};\nconst _hoisted_30 = {\n  class: \"radio-option\"\n};\nconst _hoisted_31 = {\n  key: 0,\n  class: \"form-group\"\n};\nconst _hoisted_32 = {\n  class: \"form-group\"\n};\nconst _hoisted_33 = {\n  class: \"radio-group\"\n};\nconst _hoisted_34 = {\n  class: \"radio-option\"\n};\nconst _hoisted_35 = {\n  class: \"radio-option\"\n};\nconst _hoisted_36 = {\n  class: \"form-group\"\n};\nconst _hoisted_37 = {\n  key: 2,\n  class: \"form-step\"\n};\nconst _hoisted_38 = {\n  class: \"fee-summary\"\n};\nconst _hoisted_39 = {\n  class: \"fee-card\"\n};\nconst _hoisted_40 = {\n  class: \"fee-items\"\n};\nconst _hoisted_41 = {\n  class: \"fee-item\"\n};\nconst _hoisted_42 = {\n  class: \"fee-item total\"\n};\nconst _hoisted_43 = {\n  class: \"form-section\"\n};\nconst _hoisted_44 = {\n  class: \"payment-methods\"\n};\nconst _hoisted_45 = [\"onClick\"];\nconst _hoisted_46 = {\n  class: \"payment-icon\"\n};\nconst _hoisted_47 = {\n  class: \"payment-info\"\n};\nconst _hoisted_48 = {\n  key: 0\n};\nconst _hoisted_49 = {\n  class: \"payment-radio\"\n};\nconst _hoisted_50 = [\"value\"];\nconst _hoisted_51 = {\n  key: 3,\n  class: \"form-step\"\n};\nconst _hoisted_52 = {\n  class: \"review-sections\"\n};\nconst _hoisted_53 = {\n  class: \"review-section\"\n};\nconst _hoisted_54 = {\n  class: \"review-grid\"\n};\nconst _hoisted_55 = {\n  class: \"review-item\"\n};\nconst _hoisted_56 = {\n  class: \"review-item\"\n};\nconst _hoisted_57 = {\n  class: \"review-item\"\n};\nconst _hoisted_58 = {\n  class: \"review-section\"\n};\nconst _hoisted_59 = {\n  class: \"review-grid\"\n};\nconst _hoisted_60 = {\n  class: \"review-item\"\n};\nconst _hoisted_61 = {\n  class: \"review-item\"\n};\nconst _hoisted_62 = {\n  class: \"review-item\"\n};\nconst _hoisted_63 = {\n  class: \"review-item\"\n};\nconst _hoisted_64 = {\n  class: \"review-section\"\n};\nconst _hoisted_65 = {\n  class: \"review-grid\"\n};\nconst _hoisted_66 = {\n  class: \"review-item\"\n};\nconst _hoisted_67 = {\n  class: \"review-item\"\n};\nconst _hoisted_68 = {\n  class: \"amount\"\n};\nconst _hoisted_69 = {\n  class: \"terms-section\"\n};\nconst _hoisted_70 = {\n  class: \"checkbox-option\"\n};\nconst _hoisted_71 = {\n  class: \"form-actions\"\n};\nconst _hoisted_72 = [\"disabled\"];\nconst _hoisted_73 = [\"disabled\"];\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createCommentVNode(\" Header \"), _createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_cache[22] || (_cache[22] = _createElementVNode(\"div\", {\n    class: \"header-main\"\n  }, [_createElementVNode(\"h1\", {\n    class: \"page-title\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-certificate\"\n  }), _createTextVNode(\" Barangay Clearance Request \")]), _createElementVNode(\"p\", {\n    class: \"page-description\"\n  }, \" Apply for your Barangay Clearance certificate online \")], -1 /* HOISTED */)), _createElementVNode(\"button\", {\n    class: \"back-btn\",\n    onClick: _cache[0] || (_cache[0] = (...args) => $options.goBack && $options.goBack(...args))\n  }, _cache[21] || (_cache[21] = [_createElementVNode(\"i\", {\n    class: \"fas fa-arrow-left\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\" Back \")]))])]), _createCommentVNode(\" Progress Steps \"), _createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"div\", {\n    class: _normalizeClass([\"step\", {\n      active: $data.currentStep >= 1,\n      completed: $data.currentStep > 1\n    }])\n  }, _cache[23] || (_cache[23] = [_createElementVNode(\"div\", {\n    class: \"step-number\"\n  }, \"1\", -1 /* HOISTED */), _createElementVNode(\"span\", {\n    class: \"step-label\"\n  }, \"Personal Info\", -1 /* HOISTED */)]), 2 /* CLASS */), _createElementVNode(\"div\", {\n    class: _normalizeClass([\"step\", {\n      active: $data.currentStep >= 2,\n      completed: $data.currentStep > 2\n    }])\n  }, _cache[24] || (_cache[24] = [_createElementVNode(\"div\", {\n    class: \"step-number\"\n  }, \"2\", -1 /* HOISTED */), _createElementVNode(\"span\", {\n    class: \"step-label\"\n  }, \"Purpose & Details\", -1 /* HOISTED */)]), 2 /* CLASS */), _createElementVNode(\"div\", {\n    class: _normalizeClass([\"step\", {\n      active: $data.currentStep >= 3,\n      completed: $data.currentStep > 3\n    }])\n  }, _cache[25] || (_cache[25] = [_createElementVNode(\"div\", {\n    class: \"step-number\"\n  }, \"3\", -1 /* HOISTED */), _createElementVNode(\"span\", {\n    class: \"step-label\"\n  }, \"Payment\", -1 /* HOISTED */)]), 2 /* CLASS */), _createElementVNode(\"div\", {\n    class: _normalizeClass([\"step\", {\n      active: $data.currentStep >= 4\n    }])\n  }, _cache[26] || (_cache[26] = [_createElementVNode(\"div\", {\n    class: \"step-number\"\n  }, \"4\", -1 /* HOISTED */), _createElementVNode(\"span\", {\n    class: \"step-label\"\n  }, \"Review & Submit\", -1 /* HOISTED */)]), 2 /* CLASS */)]), _createCommentVNode(\" Form Container \"), _createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"form\", {\n    onSubmit: _cache[20] || (_cache[20] = _withModifiers((...args) => $options.handleSubmit && $options.handleSubmit(...args), [\"prevent\"]))\n  }, [_createCommentVNode(\" Step 1: Personal Information \"), $data.currentStep === 1 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_6, [_cache[38] || (_cache[38] = _createElementVNode(\"div\", {\n    class: \"step-header\"\n  }, [_createElementVNode(\"h2\", null, \"Personal Information\"), _createElementVNode(\"p\", null, \"Your profile information will be used for this request\")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"div\", _hoisted_8, [_createElementVNode(\"div\", _hoisted_9, [_createElementVNode(\"h3\", null, _toDisplayString($options.getFullName()), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_10, [_createElementVNode(\"div\", _hoisted_11, [_cache[27] || (_cache[27] = _createElementVNode(\"label\", null, \"Email:\", -1 /* HOISTED */)), _createElementVNode(\"span\", null, _toDisplayString($options.clientData?.profile?.email || 'Not provided'), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_12, [_cache[28] || (_cache[28] = _createElementVNode(\"label\", null, \"Phone:\", -1 /* HOISTED */)), _createElementVNode(\"span\", null, _toDisplayString($options.clientData?.profile?.phone_number || 'Not provided'), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_13, [_cache[29] || (_cache[29] = _createElementVNode(\"label\", null, \"Address:\", -1 /* HOISTED */)), _createElementVNode(\"span\", null, _toDisplayString($options.getFullAddress()), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_14, [_cache[30] || (_cache[30] = _createElementVNode(\"label\", null, \"Date of Birth:\", -1 /* HOISTED */)), _createElementVNode(\"span\", null, _toDisplayString($options.formatDate($options.clientData?.profile?.date_of_birth)), 1 /* TEXT */)])])]), _createElementVNode(\"div\", _hoisted_15, [_createElementVNode(\"button\", {\n    type: \"button\",\n    class: \"update-profile-btn\",\n    onClick: _cache[1] || (_cache[1] = (...args) => $options.updateProfile && $options.updateProfile(...args))\n  }, _cache[31] || (_cache[31] = [_createElementVNode(\"i\", {\n    class: \"fas fa-edit\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\" Update Profile \")]))])])]), _createCommentVNode(\" Emergency Contact Information \"), _createElementVNode(\"div\", _hoisted_16, [_cache[37] || (_cache[37] = _createElementVNode(\"h3\", null, \"Emergency Contact Information\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_17, [_createElementVNode(\"div\", _hoisted_18, [_cache[32] || (_cache[32] = _createElementVNode(\"label\", {\n    for: \"emergency_contact_name\"\n  }, \"Contact Name *\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n    id: \"emergency_contact_name\",\n    \"onUpdate:modelValue\": _cache[2] || (_cache[2] = $event => $data.formData.emergency_contact_name = $event),\n    type: \"text\",\n    required: \"\",\n    placeholder: \"Full name of emergency contact\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.formData.emergency_contact_name]])]), _createElementVNode(\"div\", _hoisted_19, [_cache[34] || (_cache[34] = _createElementVNode(\"label\", {\n    for: \"emergency_contact_relationship\"\n  }, \"Relationship *\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n    id: \"emergency_contact_relationship\",\n    \"onUpdate:modelValue\": _cache[3] || (_cache[3] = $event => $data.formData.emergency_contact_relationship = $event),\n    required: \"\"\n  }, _cache[33] || (_cache[33] = [_createStaticVNode(\"<option value=\\\"\\\" data-v-3a008456>Select relationship</option><option value=\\\"Spouse\\\" data-v-3a008456>Spouse</option><option value=\\\"Parent\\\" data-v-3a008456>Parent</option><option value=\\\"Child\\\" data-v-3a008456>Child</option><option value=\\\"Sibling\\\" data-v-3a008456>Sibling</option><option value=\\\"Relative\\\" data-v-3a008456>Relative</option><option value=\\\"Friend\\\" data-v-3a008456>Friend</option><option value=\\\"Other\\\" data-v-3a008456>Other</option>\", 8)]), 512 /* NEED_PATCH */), [[_vModelSelect, $data.formData.emergency_contact_relationship]])]), _createElementVNode(\"div\", _hoisted_20, [_cache[35] || (_cache[35] = _createElementVNode(\"label\", {\n    for: \"emergency_contact_phone\"\n  }, \"Contact Phone *\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n    id: \"emergency_contact_phone\",\n    \"onUpdate:modelValue\": _cache[4] || (_cache[4] = $event => $data.formData.emergency_contact_phone = $event),\n    type: \"tel\",\n    required: \"\",\n    placeholder: \"09XXXXXXXXX\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.formData.emergency_contact_phone]])]), _createElementVNode(\"div\", _hoisted_21, [_cache[36] || (_cache[36] = _createElementVNode(\"label\", {\n    for: \"emergency_contact_address\"\n  }, \"Contact Address\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"textarea\", {\n    id: \"emergency_contact_address\",\n    \"onUpdate:modelValue\": _cache[5] || (_cache[5] = $event => $data.formData.emergency_contact_address = $event),\n    rows: \"2\",\n    placeholder: \"Complete address of emergency contact\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.formData.emergency_contact_address]])])])])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" Step 2: Purpose and Details \"), $data.currentStep === 2 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_22, [_cache[54] || (_cache[54] = _createElementVNode(\"div\", {\n    class: \"step-header\"\n  }, [_createElementVNode(\"h2\", null, \"Purpose and Additional Details\"), _createElementVNode(\"p\", null, \"Please provide the purpose and any additional information\")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_23, [_createElementVNode(\"div\", _hoisted_24, [_cache[40] || (_cache[40] = _createElementVNode(\"label\", {\n    for: \"purpose_category\"\n  }, \"Purpose Category *\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n    id: \"purpose_category\",\n    \"onUpdate:modelValue\": _cache[6] || (_cache[6] = $event => $data.formData.purpose_category_id = $event),\n    required: \"\",\n    onChange: _cache[7] || (_cache[7] = (...args) => $options.onPurposeChange && $options.onPurposeChange(...args))\n  }, [_cache[39] || (_cache[39] = _createElementVNode(\"option\", {\n    value: \"\"\n  }, \"Select purpose\", -1 /* HOISTED */)), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.purposeCategories, category => {\n    return _openBlock(), _createElementBlock(\"option\", {\n      key: category.id,\n      value: category.id\n    }, _toDisplayString(category.category_name), 9 /* TEXT, PROPS */, _hoisted_25);\n  }), 128 /* KEYED_FRAGMENT */))], 544 /* NEED_HYDRATION, NEED_PATCH */), [[_vModelSelect, $data.formData.purpose_category_id]])]), _createElementVNode(\"div\", _hoisted_26, [_cache[41] || (_cache[41] = _createElementVNode(\"label\", {\n    for: \"purpose_details\"\n  }, \"Purpose Details *\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"textarea\", {\n    id: \"purpose_details\",\n    \"onUpdate:modelValue\": _cache[8] || (_cache[8] = $event => $data.formData.purpose_details = $event),\n    rows: \"3\",\n    required: \"\",\n    placeholder: \"Please provide specific details about the purpose of this clearance\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.formData.purpose_details]])]), _createElementVNode(\"div\", _hoisted_27, [_cache[46] || (_cache[46] = _createElementVNode(\"label\", {\n    for: \"pending_cases\"\n  }, \"Pending Cases Declaration *\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_28, [_createElementVNode(\"label\", _hoisted_29, [_withDirectives(_createElementVNode(\"input\", {\n    type: \"radio\",\n    \"onUpdate:modelValue\": _cache[9] || (_cache[9] = $event => $data.formData.has_pending_cases = $event),\n    value: false,\n    required: \"\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelRadio, $data.formData.has_pending_cases]]), _cache[42] || (_cache[42] = _createElementVNode(\"span\", {\n    class: \"radio-custom\"\n  }, null, -1 /* HOISTED */)), _cache[43] || (_cache[43] = _createTextVNode(\" No pending cases \"))]), _createElementVNode(\"label\", _hoisted_30, [_withDirectives(_createElementVNode(\"input\", {\n    type: \"radio\",\n    \"onUpdate:modelValue\": _cache[10] || (_cache[10] = $event => $data.formData.has_pending_cases = $event),\n    value: true,\n    required: \"\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelRadio, $data.formData.has_pending_cases]]), _cache[44] || (_cache[44] = _createElementVNode(\"span\", {\n    class: \"radio-custom\"\n  }, null, -1 /* HOISTED */)), _cache[45] || (_cache[45] = _createTextVNode(\" Has pending cases \"))])])]), $data.formData.has_pending_cases ? (_openBlock(), _createElementBlock(\"div\", _hoisted_31, [_cache[47] || (_cache[47] = _createElementVNode(\"label\", {\n    for: \"pending_cases_details\"\n  }, \"Pending Cases Details *\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"textarea\", {\n    id: \"pending_cases_details\",\n    \"onUpdate:modelValue\": _cache[11] || (_cache[11] = $event => $data.formData.pending_cases_details = $event),\n    rows: \"3\",\n    required: \"\",\n    placeholder: \"Please provide details about pending cases\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.formData.pending_cases_details]])])) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"div\", _hoisted_32, [_cache[52] || (_cache[52] = _createElementVNode(\"label\", {\n    for: \"voter_registration\"\n  }, \"Voter Registration Status\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_33, [_createElementVNode(\"label\", _hoisted_34, [_withDirectives(_createElementVNode(\"input\", {\n    type: \"radio\",\n    \"onUpdate:modelValue\": _cache[12] || (_cache[12] = $event => $data.formData.is_registered_voter = $event),\n    value: true\n  }, null, 512 /* NEED_PATCH */), [[_vModelRadio, $data.formData.is_registered_voter]]), _cache[48] || (_cache[48] = _createElementVNode(\"span\", {\n    class: \"radio-custom\"\n  }, null, -1 /* HOISTED */)), _cache[49] || (_cache[49] = _createTextVNode(\" Registered voter \"))]), _createElementVNode(\"label\", _hoisted_35, [_withDirectives(_createElementVNode(\"input\", {\n    type: \"radio\",\n    \"onUpdate:modelValue\": _cache[13] || (_cache[13] = $event => $data.formData.is_registered_voter = $event),\n    value: false\n  }, null, 512 /* NEED_PATCH */), [[_vModelRadio, $data.formData.is_registered_voter]]), _cache[50] || (_cache[50] = _createElementVNode(\"span\", {\n    class: \"radio-custom\"\n  }, null, -1 /* HOISTED */)), _cache[51] || (_cache[51] = _createTextVNode(\" Not registered \"))])])]), _createElementVNode(\"div\", _hoisted_36, [_cache[53] || (_cache[53] = _createElementVNode(\"label\", {\n    for: \"additional_notes\"\n  }, \"Additional Notes\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"textarea\", {\n    id: \"additional_notes\",\n    \"onUpdate:modelValue\": _cache[14] || (_cache[14] = $event => $data.formData.additional_notes = $event),\n    rows: \"2\",\n    placeholder: \"Any additional information or special requests\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.formData.additional_notes]])])])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" Step 3: Payment Method \"), $data.currentStep === 3 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_37, [_cache[59] || (_cache[59] = _createElementVNode(\"div\", {\n    class: \"step-header\"\n  }, [_createElementVNode(\"h2\", null, \"Payment Information\"), _createElementVNode(\"p\", null, \"Choose your preferred payment method\")], -1 /* HOISTED */)), _createCommentVNode(\" Fee Summary \"), _createElementVNode(\"div\", _hoisted_38, [_createElementVNode(\"div\", _hoisted_39, [_cache[57] || (_cache[57] = _createElementVNode(\"h3\", null, \"Fee Breakdown\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_40, [_createElementVNode(\"div\", _hoisted_41, [_cache[55] || (_cache[55] = _createElementVNode(\"span\", null, \"Barangay Clearance Fee\", -1 /* HOISTED */)), _createElementVNode(\"span\", null, \"₱\" + _toDisplayString($options.formatCurrency($data.baseFee)), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_42, [_cache[56] || (_cache[56] = _createElementVNode(\"span\", null, \"Total Amount\", -1 /* HOISTED */)), _createElementVNode(\"span\", null, \"₱\" + _toDisplayString($options.formatCurrency($data.totalFee)), 1 /* TEXT */)])])])]), _createCommentVNode(\" Payment Methods \"), _createElementVNode(\"div\", _hoisted_43, [_cache[58] || (_cache[58] = _createElementVNode(\"h3\", null, \"Select Payment Method\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_44, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.paymentMethods, method => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: method.id,\n      class: _normalizeClass([\"payment-option\", {\n        selected: $data.formData.payment_method_id === method.id\n      }]),\n      onClick: $event => $options.selectPaymentMethod(method.id)\n    }, [_createElementVNode(\"div\", _hoisted_46, [_createElementVNode(\"i\", {\n      class: _normalizeClass($options.getPaymentIcon(method.method_code))\n    }, null, 2 /* CLASS */)]), _createElementVNode(\"div\", _hoisted_47, [_createElementVNode(\"h4\", null, _toDisplayString(method.method_name), 1 /* TEXT */), method.description ? (_openBlock(), _createElementBlock(\"p\", _hoisted_48, _toDisplayString(method.description), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_49, [_withDirectives(_createElementVNode(\"input\", {\n      type: \"radio\",\n      value: method.id,\n      \"onUpdate:modelValue\": _cache[15] || (_cache[15] = $event => $data.formData.payment_method_id = $event),\n      required: \"\"\n    }, null, 8 /* PROPS */, _hoisted_50), [[_vModelRadio, $data.formData.payment_method_id]])])], 10 /* CLASS, PROPS */, _hoisted_45);\n  }), 128 /* KEYED_FRAGMENT */))])])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" Step 4: Review and Submit \"), $data.currentStep === 4 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_51, [_cache[75] || (_cache[75] = _createElementVNode(\"div\", {\n    class: \"step-header\"\n  }, [_createElementVNode(\"h2\", null, \"Review Your Request\"), _createElementVNode(\"p\", null, \"Please review all information before submitting\")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_52, [_createCommentVNode(\" Personal Information Review \"), _createElementVNode(\"div\", _hoisted_53, [_cache[63] || (_cache[63] = _createElementVNode(\"h3\", null, \"Personal Information\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_54, [_createElementVNode(\"div\", _hoisted_55, [_cache[60] || (_cache[60] = _createElementVNode(\"label\", null, \"Full Name:\", -1 /* HOISTED */)), _createElementVNode(\"span\", null, _toDisplayString($options.getFullName()), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_56, [_cache[61] || (_cache[61] = _createElementVNode(\"label\", null, \"Emergency Contact:\", -1 /* HOISTED */)), _createElementVNode(\"span\", null, _toDisplayString($data.formData.emergency_contact_name) + \" (\" + _toDisplayString($data.formData.emergency_contact_relationship) + \")\", 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_57, [_cache[62] || (_cache[62] = _createElementVNode(\"label\", null, \"Emergency Phone:\", -1 /* HOISTED */)), _createElementVNode(\"span\", null, _toDisplayString($data.formData.emergency_contact_phone), 1 /* TEXT */)])])]), _createCommentVNode(\" Purpose Review \"), _createElementVNode(\"div\", _hoisted_58, [_cache[68] || (_cache[68] = _createElementVNode(\"h3\", null, \"Purpose & Details\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_59, [_createElementVNode(\"div\", _hoisted_60, [_cache[64] || (_cache[64] = _createElementVNode(\"label\", null, \"Purpose:\", -1 /* HOISTED */)), _createElementVNode(\"span\", null, _toDisplayString($options.getPurposeCategoryName()), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_61, [_cache[65] || (_cache[65] = _createElementVNode(\"label\", null, \"Details:\", -1 /* HOISTED */)), _createElementVNode(\"span\", null, _toDisplayString($data.formData.purpose_details), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_62, [_cache[66] || (_cache[66] = _createElementVNode(\"label\", null, \"Pending Cases:\", -1 /* HOISTED */)), _createElementVNode(\"span\", null, _toDisplayString($data.formData.has_pending_cases ? 'Yes' : 'No'), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_63, [_cache[67] || (_cache[67] = _createElementVNode(\"label\", null, \"Voter Status:\", -1 /* HOISTED */)), _createElementVNode(\"span\", null, _toDisplayString($data.formData.is_registered_voter ? 'Registered' : 'Not Registered'), 1 /* TEXT */)])])]), _createCommentVNode(\" Payment Review \"), _createElementVNode(\"div\", _hoisted_64, [_cache[71] || (_cache[71] = _createElementVNode(\"h3\", null, \"Payment Information\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_65, [_createElementVNode(\"div\", _hoisted_66, [_cache[69] || (_cache[69] = _createElementVNode(\"label\", null, \"Payment Method:\", -1 /* HOISTED */)), _createElementVNode(\"span\", null, _toDisplayString($options.getPaymentMethodName()), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_67, [_cache[70] || (_cache[70] = _createElementVNode(\"label\", null, \"Total Amount:\", -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_68, \"₱\" + _toDisplayString($options.formatCurrency($data.totalFee)), 1 /* TEXT */)])])])]), _createCommentVNode(\" Terms and Conditions \"), _createElementVNode(\"div\", _hoisted_69, [_createElementVNode(\"label\", _hoisted_70, [_withDirectives(_createElementVNode(\"input\", {\n    type: \"checkbox\",\n    \"onUpdate:modelValue\": _cache[16] || (_cache[16] = $event => $data.formData.agree_to_terms = $event),\n    required: \"\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelCheckbox, $data.formData.agree_to_terms]]), _cache[72] || (_cache[72] = _createElementVNode(\"span\", {\n    class: \"checkbox-custom\"\n  }, null, -1 /* HOISTED */)), _cache[73] || (_cache[73] = _createTextVNode(\" I agree to the \")), _createElementVNode(\"a\", {\n    href: \"#\",\n    onClick: _cache[17] || (_cache[17] = _withModifiers((...args) => $options.showTerms && $options.showTerms(...args), [\"prevent\"]))\n  }, \"terms and conditions\"), _cache[74] || (_cache[74] = _createTextVNode(\" and certify that all information provided is true and accurate. \"))])])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" Form Actions \"), _createElementVNode(\"div\", _hoisted_71, [$data.currentStep > 1 ? (_openBlock(), _createElementBlock(\"button\", {\n    key: 0,\n    type: \"button\",\n    class: \"btn-secondary\",\n    onClick: _cache[18] || (_cache[18] = (...args) => $options.previousStep && $options.previousStep(...args))\n  }, _cache[76] || (_cache[76] = [_createElementVNode(\"i\", {\n    class: \"fas fa-chevron-left\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\" Previous \")]))) : _createCommentVNode(\"v-if\", true), $data.currentStep < 4 ? (_openBlock(), _createElementBlock(\"button\", {\n    key: 1,\n    type: \"button\",\n    class: \"btn-primary\",\n    onClick: _cache[19] || (_cache[19] = (...args) => $options.nextStep && $options.nextStep(...args)),\n    disabled: !$options.canProceedToNextStep()\n  }, _cache[77] || (_cache[77] = [_createTextVNode(\" Next \"), _createElementVNode(\"i\", {\n    class: \"fas fa-chevron-right\"\n  }, null, -1 /* HOISTED */)]), 8 /* PROPS */, _hoisted_72)) : _createCommentVNode(\"v-if\", true), $data.currentStep === 4 ? (_openBlock(), _createElementBlock(\"button\", {\n    key: 2,\n    type: \"submit\",\n    class: \"btn-submit\",\n    disabled: $data.submitting || !$data.formData.agree_to_terms\n  }, [$data.submitting ? (_openBlock(), _createElementBlock(_Fragment, {\n    key: 0\n  }, [_cache[78] || (_cache[78] = _createElementVNode(\"i\", {\n    class: \"fas fa-spinner fa-spin\"\n  }, null, -1 /* HOISTED */)), _cache[79] || (_cache[79] = _createTextVNode(\" Submitting... \"))], 64 /* STABLE_FRAGMENT */)) : (_openBlock(), _createElementBlock(_Fragment, {\n    key: 1\n  }, [_cache[80] || (_cache[80] = _createElementVNode(\"i\", {\n    class: \"fas fa-paper-plane\"\n  }, null, -1 /* HOISTED */)), _cache[81] || (_cache[81] = _createTextVNode(\" Submit Request \"))], 64 /* STABLE_FRAGMENT */))], 8 /* PROPS */, _hoisted_73)) : _createCommentVNode(\"v-if\", true)])], 32 /* NEED_HYDRATION */)])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createElementVNode", "_hoisted_2", "_hoisted_3", "onClick", "_cache", "args", "$options", "goBack", "_hoisted_4", "_normalizeClass", "active", "$data", "currentStep", "completed", "_hoisted_5", "onSubmit", "_withModifiers", "handleSubmit", "_hoisted_6", "_hoisted_7", "_hoisted_8", "_hoisted_9", "_toDisplayString", "getFullName", "_hoisted_10", "_hoisted_11", "clientData", "profile", "email", "_hoisted_12", "phone_number", "_hoisted_13", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_hoisted_14", "formatDate", "date_of_birth", "_hoisted_15", "type", "updateProfile", "_hoisted_16", "_hoisted_17", "_hoisted_18", "for", "id", "formData", "emergency_contact_name", "$event", "required", "placeholder", "_hoisted_19", "emergency_contact_relationship", "_hoisted_20", "emergency_contact_phone", "_hoisted_21", "emergency_contact_address", "rows", "_hoisted_22", "_hoisted_23", "_hoisted_24", "purpose_category_id", "onChange", "onPurposeChange", "value", "_Fragment", "_renderList", "purposeCategories", "category", "key", "category_name", "_hoisted_25", "_hoisted_26", "purpose_details", "_hoisted_27", "_hoisted_28", "_hoisted_29", "has_pending_cases", "_hoisted_30", "_hoisted_31", "pending_cases_details", "_hoisted_32", "_hoisted_33", "_hoisted_34", "is_registered_voter", "_hoisted_35", "_hoisted_36", "additional_notes", "_hoisted_37", "_hoisted_38", "_hoisted_39", "_hoisted_40", "_hoisted_41", "formatCurrency", "baseFee", "_hoisted_42", "totalFee", "_hoisted_43", "_hoisted_44", "paymentMethods", "method", "selected", "payment_method_id", "selectPaymentMethod", "_hoisted_46", "getPaymentIcon", "method_code", "_hoisted_47", "method_name", "description", "_hoisted_48", "_hoisted_49", "_hoisted_51", "_hoisted_52", "_hoisted_53", "_hoisted_54", "_hoisted_55", "_hoisted_56", "_hoisted_57", "_hoisted_58", "_hoisted_59", "_hoisted_60", "getPurposeCategoryName", "_hoisted_61", "_hoisted_62", "_hoisted_63", "_hoisted_64", "_hoisted_65", "_hoisted_66", "getPaymentMethodName", "_hoisted_67", "_hoisted_68", "_hoisted_69", "_hoisted_70", "agree_to_terms", "href", "showTerms", "_hoisted_71", "previousStep", "nextStep", "disabled", "canProceedToNextStep", "submitting"], "sources": ["D:\\rhai_front_and_back\\BOSFDR\\src\\components\\client\\BarangayClearanceRequest.vue"], "sourcesContent": ["<template>\n  <div class=\"barangay-clearance-request\">\n    <!-- Header -->\n    <div class=\"page-header\">\n      <div class=\"header-content\">\n        <div class=\"header-main\">\n          <h1 class=\"page-title\">\n            <i class=\"fas fa-certificate\"></i>\n            Barangay Clearance Request\n          </h1>\n          <p class=\"page-description\">\n            Apply for your Barangay Clearance certificate online\n          </p>\n        </div>\n        <button class=\"back-btn\" @click=\"goBack\">\n          <i class=\"fas fa-arrow-left\"></i>\n          Back\n        </button>\n      </div>\n    </div>\n\n    <!-- Progress Steps -->\n    <div class=\"progress-steps\">\n      <div class=\"step\" :class=\"{ active: currentStep >= 1, completed: currentStep > 1 }\">\n        <div class=\"step-number\">1</div>\n        <span class=\"step-label\">Personal Info</span>\n      </div>\n      <div class=\"step\" :class=\"{ active: currentStep >= 2, completed: currentStep > 2 }\">\n        <div class=\"step-number\">2</div>\n        <span class=\"step-label\">Purpose & Details</span>\n      </div>\n      <div class=\"step\" :class=\"{ active: currentStep >= 3, completed: currentStep > 3 }\">\n        <div class=\"step-number\">3</div>\n        <span class=\"step-label\">Payment</span>\n      </div>\n      <div class=\"step\" :class=\"{ active: currentStep >= 4 }\">\n        <div class=\"step-number\">4</div>\n        <span class=\"step-label\">Review & Submit</span>\n      </div>\n    </div>\n\n    <!-- Form Container -->\n    <div class=\"form-container\">\n      <form @submit.prevent=\"handleSubmit\">\n        \n        <!-- Step 1: Personal Information -->\n        <div v-if=\"currentStep === 1\" class=\"form-step\">\n          <div class=\"step-header\">\n            <h2>Personal Information</h2>\n            <p>Your profile information will be used for this request</p>\n          </div>\n\n          <div class=\"profile-preview\">\n            <div class=\"profile-card\">\n              <div class=\"profile-info\">\n                <h3>{{ getFullName() }}</h3>\n                <div class=\"info-grid\">\n                  <div class=\"info-item\">\n                    <label>Email:</label>\n                    <span>{{ clientData?.profile?.email || 'Not provided' }}</span>\n                  </div>\n                  <div class=\"info-item\">\n                    <label>Phone:</label>\n                    <span>{{ clientData?.profile?.phone_number || 'Not provided' }}</span>\n                  </div>\n                  <div class=\"info-item\">\n                    <label>Address:</label>\n                    <span>{{ getFullAddress() }}</span>\n                  </div>\n                  <div class=\"info-item\">\n                    <label>Date of Birth:</label>\n                    <span>{{ formatDate(clientData?.profile?.date_of_birth) }}</span>\n                  </div>\n                </div>\n              </div>\n              <div class=\"profile-actions\">\n                <button type=\"button\" class=\"update-profile-btn\" @click=\"updateProfile\">\n                  <i class=\"fas fa-edit\"></i>\n                  Update Profile\n                </button>\n              </div>\n            </div>\n          </div>\n\n          <!-- Emergency Contact Information -->\n          <div class=\"form-section\">\n            <h3>Emergency Contact Information</h3>\n            <div class=\"form-grid\">\n              <div class=\"form-group\">\n                <label for=\"emergency_contact_name\">Contact Name *</label>\n                <input\n                  id=\"emergency_contact_name\"\n                  v-model=\"formData.emergency_contact_name\"\n                  type=\"text\"\n                  required\n                  placeholder=\"Full name of emergency contact\"\n                />\n              </div>\n              <div class=\"form-group\">\n                <label for=\"emergency_contact_relationship\">Relationship *</label>\n                <select\n                  id=\"emergency_contact_relationship\"\n                  v-model=\"formData.emergency_contact_relationship\"\n                  required\n                >\n                  <option value=\"\">Select relationship</option>\n                  <option value=\"Spouse\">Spouse</option>\n                  <option value=\"Parent\">Parent</option>\n                  <option value=\"Child\">Child</option>\n                  <option value=\"Sibling\">Sibling</option>\n                  <option value=\"Relative\">Relative</option>\n                  <option value=\"Friend\">Friend</option>\n                  <option value=\"Other\">Other</option>\n                </select>\n              </div>\n              <div class=\"form-group\">\n                <label for=\"emergency_contact_phone\">Contact Phone *</label>\n                <input\n                  id=\"emergency_contact_phone\"\n                  v-model=\"formData.emergency_contact_phone\"\n                  type=\"tel\"\n                  required\n                  placeholder=\"09XXXXXXXXX\"\n                />\n              </div>\n              <div class=\"form-group\">\n                <label for=\"emergency_contact_address\">Contact Address</label>\n                <textarea\n                  id=\"emergency_contact_address\"\n                  v-model=\"formData.emergency_contact_address\"\n                  rows=\"2\"\n                  placeholder=\"Complete address of emergency contact\"\n                ></textarea>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Step 2: Purpose and Details -->\n        <div v-if=\"currentStep === 2\" class=\"form-step\">\n          <div class=\"step-header\">\n            <h2>Purpose and Additional Details</h2>\n            <p>Please provide the purpose and any additional information</p>\n          </div>\n\n          <div class=\"form-section\">\n            <div class=\"form-group\">\n              <label for=\"purpose_category\">Purpose Category *</label>\n              <select\n                id=\"purpose_category\"\n                v-model=\"formData.purpose_category_id\"\n                required\n                @change=\"onPurposeChange\"\n              >\n                <option value=\"\">Select purpose</option>\n                <option\n                  v-for=\"category in purposeCategories\"\n                  :key=\"category.id\"\n                  :value=\"category.id\"\n                >\n                  {{ category.category_name }}\n                </option>\n              </select>\n            </div>\n\n            <div class=\"form-group\">\n              <label for=\"purpose_details\">Purpose Details *</label>\n              <textarea\n                id=\"purpose_details\"\n                v-model=\"formData.purpose_details\"\n                rows=\"3\"\n                required\n                placeholder=\"Please provide specific details about the purpose of this clearance\"\n              ></textarea>\n            </div>\n\n            <div class=\"form-group\">\n              <label for=\"pending_cases\">Pending Cases Declaration *</label>\n              <div class=\"radio-group\">\n                <label class=\"radio-option\">\n                  <input\n                    type=\"radio\"\n                    v-model=\"formData.has_pending_cases\"\n                    :value=\"false\"\n                    required\n                  />\n                  <span class=\"radio-custom\"></span>\n                  No pending cases\n                </label>\n                <label class=\"radio-option\">\n                  <input\n                    type=\"radio\"\n                    v-model=\"formData.has_pending_cases\"\n                    :value=\"true\"\n                    required\n                  />\n                  <span class=\"radio-custom\"></span>\n                  Has pending cases\n                </label>\n              </div>\n            </div>\n\n            <div v-if=\"formData.has_pending_cases\" class=\"form-group\">\n              <label for=\"pending_cases_details\">Pending Cases Details *</label>\n              <textarea\n                id=\"pending_cases_details\"\n                v-model=\"formData.pending_cases_details\"\n                rows=\"3\"\n                required\n                placeholder=\"Please provide details about pending cases\"\n              ></textarea>\n            </div>\n\n            <div class=\"form-group\">\n              <label for=\"voter_registration\">Voter Registration Status</label>\n              <div class=\"radio-group\">\n                <label class=\"radio-option\">\n                  <input\n                    type=\"radio\"\n                    v-model=\"formData.is_registered_voter\"\n                    :value=\"true\"\n                  />\n                  <span class=\"radio-custom\"></span>\n                  Registered voter\n                </label>\n                <label class=\"radio-option\">\n                  <input\n                    type=\"radio\"\n                    v-model=\"formData.is_registered_voter\"\n                    :value=\"false\"\n                  />\n                  <span class=\"radio-custom\"></span>\n                  Not registered\n                </label>\n              </div>\n            </div>\n\n            <div class=\"form-group\">\n              <label for=\"additional_notes\">Additional Notes</label>\n              <textarea\n                id=\"additional_notes\"\n                v-model=\"formData.additional_notes\"\n                rows=\"2\"\n                placeholder=\"Any additional information or special requests\"\n              ></textarea>\n            </div>\n          </div>\n        </div>\n\n        <!-- Step 3: Payment Method -->\n        <div v-if=\"currentStep === 3\" class=\"form-step\">\n          <div class=\"step-header\">\n            <h2>Payment Information</h2>\n            <p>Choose your preferred payment method</p>\n          </div>\n\n          <!-- Fee Summary -->\n          <div class=\"fee-summary\">\n            <div class=\"fee-card\">\n              <h3>Fee Breakdown</h3>\n              <div class=\"fee-items\">\n                <div class=\"fee-item\">\n                  <span>Barangay Clearance Fee</span>\n                  <span>₱{{ formatCurrency(baseFee) }}</span>\n                </div>\n                <div class=\"fee-item total\">\n                  <span>Total Amount</span>\n                  <span>₱{{ formatCurrency(totalFee) }}</span>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Payment Methods -->\n          <div class=\"form-section\">\n            <h3>Select Payment Method</h3>\n            <div class=\"payment-methods\">\n              <div\n                v-for=\"method in paymentMethods\"\n                :key=\"method.id\"\n                class=\"payment-option\"\n                :class=\"{ selected: formData.payment_method_id === method.id }\"\n                @click=\"selectPaymentMethod(method.id)\"\n              >\n                <div class=\"payment-icon\">\n                  <i :class=\"getPaymentIcon(method.method_code)\"></i>\n                </div>\n                <div class=\"payment-info\">\n                  <h4>{{ method.method_name }}</h4>\n                  <p v-if=\"method.description\">{{ method.description }}</p>\n                </div>\n                <div class=\"payment-radio\">\n                  <input\n                    type=\"radio\"\n                    :value=\"method.id\"\n                    v-model=\"formData.payment_method_id\"\n                    required\n                  />\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Step 4: Review and Submit -->\n        <div v-if=\"currentStep === 4\" class=\"form-step\">\n          <div class=\"step-header\">\n            <h2>Review Your Request</h2>\n            <p>Please review all information before submitting</p>\n          </div>\n\n          <div class=\"review-sections\">\n            <!-- Personal Information Review -->\n            <div class=\"review-section\">\n              <h3>Personal Information</h3>\n              <div class=\"review-grid\">\n                <div class=\"review-item\">\n                  <label>Full Name:</label>\n                  <span>{{ getFullName() }}</span>\n                </div>\n                <div class=\"review-item\">\n                  <label>Emergency Contact:</label>\n                  <span>{{ formData.emergency_contact_name }} ({{ formData.emergency_contact_relationship }})</span>\n                </div>\n                <div class=\"review-item\">\n                  <label>Emergency Phone:</label>\n                  <span>{{ formData.emergency_contact_phone }}</span>\n                </div>\n              </div>\n            </div>\n\n            <!-- Purpose Review -->\n            <div class=\"review-section\">\n              <h3>Purpose & Details</h3>\n              <div class=\"review-grid\">\n                <div class=\"review-item\">\n                  <label>Purpose:</label>\n                  <span>{{ getPurposeCategoryName() }}</span>\n                </div>\n                <div class=\"review-item\">\n                  <label>Details:</label>\n                  <span>{{ formData.purpose_details }}</span>\n                </div>\n                <div class=\"review-item\">\n                  <label>Pending Cases:</label>\n                  <span>{{ formData.has_pending_cases ? 'Yes' : 'No' }}</span>\n                </div>\n                <div class=\"review-item\">\n                  <label>Voter Status:</label>\n                  <span>{{ formData.is_registered_voter ? 'Registered' : 'Not Registered' }}</span>\n                </div>\n              </div>\n            </div>\n\n            <!-- Payment Review -->\n            <div class=\"review-section\">\n              <h3>Payment Information</h3>\n              <div class=\"review-grid\">\n                <div class=\"review-item\">\n                  <label>Payment Method:</label>\n                  <span>{{ getPaymentMethodName() }}</span>\n                </div>\n                <div class=\"review-item\">\n                  <label>Total Amount:</label>\n                  <span class=\"amount\">₱{{ formatCurrency(totalFee) }}</span>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Terms and Conditions -->\n          <div class=\"terms-section\">\n            <label class=\"checkbox-option\">\n              <input\n                type=\"checkbox\"\n                v-model=\"formData.agree_to_terms\"\n                required\n              />\n              <span class=\"checkbox-custom\"></span>\n              I agree to the <a href=\"#\" @click.prevent=\"showTerms\">terms and conditions</a> and certify that all information provided is true and accurate.\n            </label>\n          </div>\n        </div>\n\n        <!-- Form Actions -->\n        <div class=\"form-actions\">\n          <button\n            v-if=\"currentStep > 1\"\n            type=\"button\"\n            class=\"btn-secondary\"\n            @click=\"previousStep\"\n          >\n            <i class=\"fas fa-chevron-left\"></i>\n            Previous\n          </button>\n          \n          <button\n            v-if=\"currentStep < 4\"\n            type=\"button\"\n            class=\"btn-primary\"\n            @click=\"nextStep\"\n            :disabled=\"!canProceedToNextStep()\"\n          >\n            Next\n            <i class=\"fas fa-chevron-right\"></i>\n          </button>\n          \n          <button\n            v-if=\"currentStep === 4\"\n            type=\"submit\"\n            class=\"btn-submit\"\n            :disabled=\"submitting || !formData.agree_to_terms\"\n          >\n            <template v-if=\"submitting\">\n              <i class=\"fas fa-spinner fa-spin\"></i>\n              Submitting...\n            </template>\n            <template v-else>\n              <i class=\"fas fa-paper-plane\"></i>\n              Submit Request\n            </template>\n          </button>\n        </div>\n      </form>\n    </div>\n  </div>\n</template>\n\n<script>\nimport documentRequestService from '@/services/documentRequestService';\nimport clientAuthService from '@/services/clientAuthService';\n\nexport default {\n  name: 'BarangayClearanceRequest',\n  data() {\n    return {\n      currentStep: 1,\n      submitting: false,\n      purposeCategories: [],\n      paymentMethods: [],\n      baseFee: 50.00,\n      totalFee: 50.00,\n      formData: {\n        document_type_id: 2, // Barangay Clearance\n        purpose_category_id: '',\n        purpose_details: '',\n        emergency_contact_name: '',\n        emergency_contact_relationship: '',\n        emergency_contact_phone: '',\n        emergency_contact_address: '',\n        has_pending_cases: false,\n        pending_cases_details: '',\n        is_registered_voter: null,\n        additional_notes: '',\n        payment_method_id: '',\n        agree_to_terms: false\n      }\n    };\n  },\n  computed: {\n    clientData() {\n      return clientAuthService.getCurrentUser();\n    }\n  },\n  async mounted() {\n    await this.loadFormData();\n  },\n  methods: {\n    async loadFormData() {\n      try {\n        const [purposeResponse, paymentResponse] = await Promise.all([\n          documentRequestService.getPurposeCategories(),\n          documentRequestService.getPaymentMethods()\n        ]);\n        \n        this.purposeCategories = purposeResponse.data || [];\n        this.paymentMethods = paymentResponse.data || [];\n        \n      } catch (error) {\n        console.error('Error loading form data:', error);\n        this.$toast?.error('Failed to load form data');\n      }\n    },\n\n    getFullName() {\n      const profile = this.clientData?.profile;\n      if (!profile) return 'N/A';\n      return `${profile.first_name || ''} ${profile.middle_name || ''} ${profile.last_name || ''}`.trim();\n    },\n\n    getFullAddress() {\n      const profile = this.clientData?.profile;\n      if (!profile) return 'Not provided';\n      \n      const parts = [\n        profile.house_number,\n        profile.street,\n        profile.barangay,\n        profile.city,\n        profile.province\n      ].filter(Boolean);\n      \n      return parts.length > 0 ? parts.join(', ') : 'Not provided';\n    },\n\n    formatDate(dateString) {\n      if (!dateString) return 'Not provided';\n      return new Date(dateString).toLocaleDateString();\n    },\n\n    formatCurrency(amount) {\n      return parseFloat(amount).toFixed(2);\n    },\n\n    canProceedToNextStep() {\n      switch (this.currentStep) {\n        case 1:\n          return this.formData.emergency_contact_name && \n                 this.formData.emergency_contact_relationship && \n                 this.formData.emergency_contact_phone;\n        case 2:\n          return this.formData.purpose_category_id && \n                 this.formData.purpose_details &&\n                 this.formData.has_pending_cases !== null;\n        case 3:\n          return this.formData.payment_method_id;\n        default:\n          return true;\n      }\n    },\n\n    nextStep() {\n      if (this.canProceedToNextStep() && this.currentStep < 4) {\n        this.currentStep++;\n      }\n    },\n\n    previousStep() {\n      if (this.currentStep > 1) {\n        this.currentStep--;\n      }\n    },\n\n    onPurposeChange() {\n      // Could implement dynamic fee calculation based on purpose\n    },\n\n    selectPaymentMethod(methodId) {\n      this.formData.payment_method_id = methodId;\n    },\n\n    getPaymentIcon(methodCode) {\n      const icons = {\n        'CASH': 'fas fa-money-bill',\n        'PAYMONGO_CARD': 'fas fa-credit-card',\n        'PAYMONGO_GCASH': 'fab fa-google-pay',\n        'PAYMONGO_GRABPAY': 'fas fa-mobile-alt',\n        'PAYMONGO_PAYMAYA': 'fas fa-wallet'\n      };\n      return icons[methodCode] || 'fas fa-credit-card';\n    },\n\n    getPurposeCategoryName() {\n      const category = this.purposeCategories.find(c => c.id === this.formData.purpose_category_id);\n      return category?.category_name || '';\n    },\n\n    getPaymentMethodName() {\n      const method = this.paymentMethods.find(m => m.id === this.formData.payment_method_id);\n      return method?.method_name || '';\n    },\n\n    async handleSubmit() {\n      if (!this.formData.agree_to_terms) return;\n      \n      try {\n        this.submitting = true;\n        \n        const requestData = {\n          ...this.formData,\n          total_fee: this.totalFee\n        };\n        \n        const response = await documentRequestService.submitRequest(requestData);\n        \n        this.$toast?.success('Request submitted successfully!');\n        this.$router.push({ \n          name: 'RequestDetails', \n          params: { id: response.data.id } \n        });\n        \n      } catch (error) {\n        console.error('Error submitting request:', error);\n        this.$toast?.error(error.response?.data?.message || 'Failed to submit request');\n      } finally {\n        this.submitting = false;\n      }\n    },\n\n    goBack() {\n      this.$router.push({ name: 'NewDocumentRequest' });\n    },\n\n    updateProfile() {\n      // TODO: Navigate to profile update page\n      console.log('Update profile');\n    },\n\n    showTerms() {\n      // TODO: Show terms and conditions modal\n      console.log('Show terms');\n    }\n  }\n};\n</script>\n\n<style scoped>\n.barangay-clearance-request {\n  padding: 2rem;\n  max-width: 1000px;\n  margin: 0 auto;\n}\n\n.page-header {\n  margin-bottom: 2rem;\n}\n\n.header-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  gap: 2rem;\n}\n\n.page-title {\n  font-size: 1.75rem;\n  font-weight: 700;\n  color: #1a365d;\n  margin-bottom: 0.5rem;\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n}\n\n.page-title i {\n  color: #3182ce;\n}\n\n.page-description {\n  font-size: 1rem;\n  color: #4a5568;\n  margin: 0;\n}\n\n.back-btn {\n  background: #e2e8f0;\n  border: none;\n  padding: 0.75rem 1.5rem;\n  border-radius: 0.5rem;\n  color: #4a5568;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.back-btn:hover {\n  background: #cbd5e0;\n  color: #2d3748;\n}\n\n.progress-steps {\n  display: flex;\n  justify-content: center;\n  margin-bottom: 3rem;\n  position: relative;\n}\n\n.progress-steps::before {\n  content: '';\n  position: absolute;\n  top: 1.5rem;\n  left: 25%;\n  right: 25%;\n  height: 2px;\n  background: #e2e8f0;\n  z-index: 1;\n}\n\n.step {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  flex: 1;\n  position: relative;\n  z-index: 2;\n}\n\n.step-number {\n  width: 3rem;\n  height: 3rem;\n  border-radius: 50%;\n  background: #e2e8f0;\n  color: #a0aec0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: 600;\n  margin-bottom: 0.5rem;\n  transition: all 0.3s;\n}\n\n.step.active .step-number {\n  background: #3182ce;\n  color: white;\n}\n\n.step.completed .step-number {\n  background: #38a169;\n  color: white;\n}\n\n.step-label {\n  font-size: 0.875rem;\n  color: #718096;\n  text-align: center;\n}\n\n.step.active .step-label {\n  color: #3182ce;\n  font-weight: 500;\n}\n\n.form-container {\n  background: white;\n  border-radius: 1rem;\n  padding: 2rem;\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);\n}\n\n.form-step {\n  min-height: 400px;\n}\n\n.step-header {\n  text-align: center;\n  margin-bottom: 2rem;\n}\n\n.step-header h2 {\n  font-size: 1.5rem;\n  font-weight: 600;\n  color: #1a365d;\n  margin-bottom: 0.5rem;\n}\n\n.step-header p {\n  color: #4a5568;\n  margin: 0;\n}\n\n.profile-preview {\n  margin-bottom: 2rem;\n}\n\n.profile-card {\n  background: #f7fafc;\n  border: 1px solid #e2e8f0;\n  border-radius: 0.75rem;\n  padding: 1.5rem;\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n}\n\n.profile-info h3 {\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: #1a365d;\n  margin-bottom: 1rem;\n}\n\n.info-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 1rem;\n}\n\n.info-item {\n  display: flex;\n  flex-direction: column;\n  gap: 0.25rem;\n}\n\n.info-item label {\n  font-size: 0.875rem;\n  font-weight: 500;\n  color: #718096;\n}\n\n.info-item span {\n  color: #2d3748;\n}\n\n.update-profile-btn {\n  background: #3182ce;\n  color: white;\n  border: none;\n  padding: 0.75rem 1.5rem;\n  border-radius: 0.5rem;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  transition: all 0.2s;\n}\n\n.update-profile-btn:hover {\n  background: #2c5aa0;\n}\n\n.form-section {\n  margin-bottom: 2rem;\n}\n\n.form-section h3 {\n  font-size: 1.125rem;\n  font-weight: 600;\n  color: #1a365d;\n  margin-bottom: 1rem;\n}\n\n.form-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 1rem;\n}\n\n.form-group {\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n}\n\n.form-group label {\n  font-weight: 500;\n  color: #2d3748;\n  font-size: 0.875rem;\n}\n\n.form-group input,\n.form-group select,\n.form-group textarea {\n  padding: 0.75rem;\n  border: 1px solid #e2e8f0;\n  border-radius: 0.5rem;\n  font-size: 0.875rem;\n  transition: all 0.2s;\n}\n\n.form-group input:focus,\n.form-group select:focus,\n.form-group textarea:focus {\n  outline: none;\n  border-color: #3182ce;\n  box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);\n}\n\n.radio-group {\n  display: flex;\n  flex-direction: column;\n  gap: 0.75rem;\n}\n\n.radio-option {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  cursor: pointer;\n  padding: 0.5rem;\n  border-radius: 0.5rem;\n  transition: all 0.2s;\n}\n\n.radio-option:hover {\n  background: #f7fafc;\n}\n\n.radio-option input[type=\"radio\"] {\n  display: none;\n}\n\n.radio-custom {\n  width: 1.25rem;\n  height: 1.25rem;\n  border: 2px solid #e2e8f0;\n  border-radius: 50%;\n  position: relative;\n  transition: all 0.2s;\n}\n\n.radio-option input[type=\"radio\"]:checked + .radio-custom {\n  border-color: #3182ce;\n}\n\n.radio-option input[type=\"radio\"]:checked + .radio-custom::after {\n  content: '';\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  width: 0.5rem;\n  height: 0.5rem;\n  background: #3182ce;\n  border-radius: 50%;\n}\n\n.fee-summary {\n  margin-bottom: 2rem;\n}\n\n.fee-card {\n  background: #f7fafc;\n  border: 1px solid #e2e8f0;\n  border-radius: 0.75rem;\n  padding: 1.5rem;\n}\n\n.fee-card h3 {\n  font-size: 1.125rem;\n  font-weight: 600;\n  color: #1a365d;\n  margin-bottom: 1rem;\n}\n\n.fee-items {\n  display: flex;\n  flex-direction: column;\n  gap: 0.75rem;\n}\n\n.fee-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 0.5rem 0;\n}\n\n.fee-item.total {\n  border-top: 1px solid #e2e8f0;\n  padding-top: 1rem;\n  font-weight: 600;\n  font-size: 1.125rem;\n  color: #1a365d;\n}\n\n.payment-methods {\n  display: grid;\n  gap: 1rem;\n}\n\n.payment-option {\n  border: 2px solid #e2e8f0;\n  border-radius: 0.75rem;\n  padding: 1rem;\n  cursor: pointer;\n  transition: all 0.2s;\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n}\n\n.payment-option:hover {\n  border-color: #cbd5e0;\n}\n\n.payment-option.selected {\n  border-color: #3182ce;\n  background: #ebf8ff;\n}\n\n.payment-icon {\n  width: 3rem;\n  height: 3rem;\n  background: #f7fafc;\n  border-radius: 0.5rem;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 1.25rem;\n  color: #4a5568;\n}\n\n.payment-option.selected .payment-icon {\n  background: #3182ce;\n  color: white;\n}\n\n.payment-info {\n  flex: 1;\n}\n\n.payment-info h4 {\n  font-weight: 600;\n  color: #1a365d;\n  margin-bottom: 0.25rem;\n}\n\n.payment-info p {\n  color: #718096;\n  font-size: 0.875rem;\n  margin: 0;\n}\n\n.payment-radio input {\n  width: 1.25rem;\n  height: 1.25rem;\n}\n\n.review-sections {\n  display: flex;\n  flex-direction: column;\n  gap: 2rem;\n  margin-bottom: 2rem;\n}\n\n.review-section {\n  background: #f7fafc;\n  border-radius: 0.75rem;\n  padding: 1.5rem;\n}\n\n.review-section h3 {\n  font-size: 1.125rem;\n  font-weight: 600;\n  color: #1a365d;\n  margin-bottom: 1rem;\n}\n\n.review-grid {\n  display: grid;\n  gap: 1rem;\n}\n\n.review-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  padding: 0.5rem 0;\n  border-bottom: 1px solid #e2e8f0;\n}\n\n.review-item:last-child {\n  border-bottom: none;\n}\n\n.review-item label {\n  font-weight: 500;\n  color: #4a5568;\n  min-width: 120px;\n}\n\n.review-item span {\n  color: #2d3748;\n  text-align: right;\n  flex: 1;\n}\n\n.review-item .amount {\n  font-weight: 600;\n  color: #38a169;\n  font-size: 1.125rem;\n}\n\n.terms-section {\n  margin-bottom: 2rem;\n  padding: 1rem;\n  background: #fffaf0;\n  border: 1px solid #fed7aa;\n  border-radius: 0.5rem;\n}\n\n.checkbox-option {\n  display: flex;\n  align-items: flex-start;\n  gap: 0.75rem;\n  cursor: pointer;\n  line-height: 1.5;\n}\n\n.checkbox-option input[type=\"checkbox\"] {\n  display: none;\n}\n\n.checkbox-custom {\n  width: 1.25rem;\n  height: 1.25rem;\n  border: 2px solid #e2e8f0;\n  border-radius: 0.25rem;\n  position: relative;\n  flex-shrink: 0;\n  margin-top: 0.125rem;\n  transition: all 0.2s;\n}\n\n.checkbox-option input[type=\"checkbox\"]:checked + .checkbox-custom {\n  border-color: #3182ce;\n  background: #3182ce;\n}\n\n.checkbox-option input[type=\"checkbox\"]:checked + .checkbox-custom::after {\n  content: '✓';\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  color: white;\n  font-size: 0.875rem;\n  font-weight: bold;\n}\n\n.checkbox-option a {\n  color: #3182ce;\n  text-decoration: underline;\n}\n\n.form-actions {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  gap: 1rem;\n  padding-top: 2rem;\n  border-top: 1px solid #e2e8f0;\n}\n\n.btn-secondary,\n.btn-primary,\n.btn-submit {\n  padding: 0.75rem 2rem;\n  border-radius: 0.5rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  border: none;\n}\n\n.btn-secondary {\n  background: #e2e8f0;\n  color: #4a5568;\n}\n\n.btn-secondary:hover {\n  background: #cbd5e0;\n}\n\n.btn-primary {\n  background: #3182ce;\n  color: white;\n}\n\n.btn-primary:hover:not(:disabled) {\n  background: #2c5aa0;\n}\n\n.btn-submit {\n  background: #38a169;\n  color: white;\n}\n\n.btn-submit:hover:not(:disabled) {\n  background: #2f855a;\n}\n\n.btn-primary:disabled,\n.btn-submit:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n}\n\n@media (max-width: 768px) {\n  .barangay-clearance-request {\n    padding: 1rem;\n  }\n\n  .header-content {\n    flex-direction: column;\n    align-items: stretch;\n  }\n\n  .progress-steps {\n    flex-wrap: wrap;\n    gap: 1rem;\n  }\n\n  .progress-steps::before {\n    display: none;\n  }\n\n  .form-container {\n    padding: 1.5rem;\n  }\n\n  .form-grid {\n    grid-template-columns: 1fr;\n  }\n\n  .info-grid {\n    grid-template-columns: 1fr;\n  }\n\n  .profile-card {\n    flex-direction: column;\n    gap: 1rem;\n  }\n\n  .form-actions {\n    flex-direction: column;\n    align-items: stretch;\n  }\n\n  .review-item {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 0.25rem;\n  }\n\n  .review-item span {\n    text-align: left;\n  }\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAA4B;;EAEhCA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAAgB;;EAkBxBA,KAAK,EAAC;AAAgB;;EAoBtBA,KAAK,EAAC;AAAgB;;;EAIOA,KAAK,EAAC;;;EAM7BA,KAAK,EAAC;AAAiB;;EACrBA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAc;;EAElBA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAW;;EAIjBA,KAAK,EAAC;AAAW;;EAIjBA,KAAK,EAAC;AAAW;;EAIjBA,KAAK,EAAC;AAAW;;EAMrBA,KAAK,EAAC;AAAiB;;EAU3BA,KAAK,EAAC;AAAc;;EAElBA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAY;;EAUlBA,KAAK,EAAC;AAAY;;EAiBlBA,KAAK,EAAC;AAAY;;EAUlBA,KAAK,EAAC;AAAY;;;EAcCA,KAAK,EAAC;;;EAM7BA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAY;;;EAmBlBA,KAAK,EAAC;AAAY;;EAWlBA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAa;;EACfA,KAAK,EAAC;AAAc;;EAUpBA,KAAK,EAAC;AAAc;;;EAaQA,KAAK,EAAC;;;EAWxCA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAa;;EACfA,KAAK,EAAC;AAAc;;EASpBA,KAAK,EAAC;AAAc;;EAY1BA,KAAK,EAAC;AAAY;;;EAaGA,KAAK,EAAC;;;EAO7BA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAAU;;EAEdA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAU;;EAIhBA,KAAK,EAAC;AAAgB;;EAS5BA,KAAK,EAAC;AAAc;;EAElBA,KAAK,EAAC;AAAiB;;;EAQnBA,KAAK,EAAC;AAAc;;EAGpBA,KAAK,EAAC;AAAc;;;;;EAIpBA,KAAK,EAAC;AAAe;;;;EAcJA,KAAK,EAAC;;;EAM7BA,KAAK,EAAC;AAAiB;;EAErBA,KAAK,EAAC;AAAgB;;EAEpBA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAAa;;EAInBA,KAAK,EAAC;AAAa;;EAInBA,KAAK,EAAC;AAAa;;EAQvBA,KAAK,EAAC;AAAgB;;EAEpBA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAAa;;EAInBA,KAAK,EAAC;AAAa;;EAInBA,KAAK,EAAC;AAAa;;EAInBA,KAAK,EAAC;AAAa;;EAQvBA,KAAK,EAAC;AAAgB;;EAEpBA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAAa;;EAInBA,KAAK,EAAC;AAAa;;EAEhBA,KAAK,EAAC;AAAQ;;EAOvBA,KAAK,EAAC;AAAe;;EACjBA,KAAK,EAAC;AAAiB;;EAa7BA,KAAK,EAAC;AAAc;;;;uBAhY/BC,mBAAA,CAwaM,OAxaNC,UAwaM,GAvaJC,mBAAA,YAAe,EACfC,mBAAA,CAgBM,OAhBNC,UAgBM,GAfJD,mBAAA,CAcM,OAdNE,UAcM,G,4BAbJF,mBAAA,CAQM;IARDJ,KAAK,EAAC;EAAa,IACtBI,mBAAA,CAGK;IAHDJ,KAAK,EAAC;EAAY,IACpBI,mBAAA,CAAkC;IAA/BJ,KAAK,EAAC;EAAoB,I,iBAAK,8BAEpC,E,GACAI,mBAAA,CAEI;IAFDJ,KAAK,EAAC;EAAkB,GAAC,wDAE5B,E,sBAEFI,mBAAA,CAGS;IAHDJ,KAAK,EAAC,UAAU;IAAEO,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAC,MAAA,IAAAD,QAAA,CAAAC,MAAA,IAAAF,IAAA,CAAM;kCACrCL,mBAAA,CAAiC;IAA9BJ,KAAK,EAAC;EAAmB,4B,iBAAK,QAEnC,E,QAIJG,mBAAA,oBAAuB,EACvBC,mBAAA,CAiBM,OAjBNQ,UAiBM,GAhBJR,mBAAA,CAGM;IAHDJ,KAAK,EAAAa,eAAA,EAAC,MAAM;MAAAC,MAAA,EAAmBC,KAAA,CAAAC,WAAW;MAAAC,SAAA,EAAkBF,KAAA,CAAAC,WAAW;IAAA;kCAC1EZ,mBAAA,CAAgC;IAA3BJ,KAAK,EAAC;EAAa,GAAC,GAAC,qBAC1BI,mBAAA,CAA6C;IAAvCJ,KAAK,EAAC;EAAY,GAAC,eAAa,oB,mBAExCI,mBAAA,CAGM;IAHDJ,KAAK,EAAAa,eAAA,EAAC,MAAM;MAAAC,MAAA,EAAmBC,KAAA,CAAAC,WAAW;MAAAC,SAAA,EAAkBF,KAAA,CAAAC,WAAW;IAAA;kCAC1EZ,mBAAA,CAAgC;IAA3BJ,KAAK,EAAC;EAAa,GAAC,GAAC,qBAC1BI,mBAAA,CAAiD;IAA3CJ,KAAK,EAAC;EAAY,GAAC,mBAAiB,oB,mBAE5CI,mBAAA,CAGM;IAHDJ,KAAK,EAAAa,eAAA,EAAC,MAAM;MAAAC,MAAA,EAAmBC,KAAA,CAAAC,WAAW;MAAAC,SAAA,EAAkBF,KAAA,CAAAC,WAAW;IAAA;kCAC1EZ,mBAAA,CAAgC;IAA3BJ,KAAK,EAAC;EAAa,GAAC,GAAC,qBAC1BI,mBAAA,CAAuC;IAAjCJ,KAAK,EAAC;EAAY,GAAC,SAAO,oB,mBAElCI,mBAAA,CAGM;IAHDJ,KAAK,EAAAa,eAAA,EAAC,MAAM;MAAAC,MAAA,EAAmBC,KAAA,CAAAC,WAAW;IAAA;kCAC7CZ,mBAAA,CAAgC;IAA3BJ,KAAK,EAAC;EAAa,GAAC,GAAC,qBAC1BI,mBAAA,CAA+C;IAAzCJ,KAAK,EAAC;EAAY,GAAC,iBAAe,oB,qBAI5CG,mBAAA,oBAAuB,EACvBC,mBAAA,CA8XM,OA9XNc,UA8XM,GA7XJd,mBAAA,CA4XO;IA5XAe,QAAM,EAAAX,MAAA,SAAAA,MAAA,OAAAY,cAAA,KAAAX,IAAA,KAAUC,QAAA,CAAAW,YAAA,IAAAX,QAAA,CAAAW,YAAA,IAAAZ,IAAA,CAAY;MAEjCN,mBAAA,kCAAqC,EAC1BY,KAAA,CAAAC,WAAW,U,cAAtBf,mBAAA,CA0FM,OA1FNqB,UA0FM,G,4BAzFJlB,mBAAA,CAGM;IAHDJ,KAAK,EAAC;EAAa,IACtBI,mBAAA,CAA6B,YAAzB,sBAAoB,GACxBA,mBAAA,CAA6D,WAA1D,wDAAsD,E,sBAG3DA,mBAAA,CA8BM,OA9BNmB,UA8BM,GA7BJnB,mBAAA,CA4BM,OA5BNoB,UA4BM,GA3BJpB,mBAAA,CAoBM,OApBNqB,UAoBM,GAnBJrB,mBAAA,CAA4B,YAAAsB,gBAAA,CAArBhB,QAAA,CAAAiB,WAAW,oBAClBvB,mBAAA,CAiBM,OAjBNwB,WAiBM,GAhBJxB,mBAAA,CAGM,OAHNyB,WAGM,G,4BAFJzB,mBAAA,CAAqB,eAAd,QAAM,sBACbA,mBAAA,CAA+D,cAAAsB,gBAAA,CAAtDhB,QAAA,CAAAoB,UAAU,EAAEC,OAAO,EAAEC,KAAK,mC,GAErC5B,mBAAA,CAGM,OAHN6B,WAGM,G,4BAFJ7B,mBAAA,CAAqB,eAAd,QAAM,sBACbA,mBAAA,CAAsE,cAAAsB,gBAAA,CAA7DhB,QAAA,CAAAoB,UAAU,EAAEC,OAAO,EAAEG,YAAY,mC,GAE5C9B,mBAAA,CAGM,OAHN+B,WAGM,G,4BAFJ/B,mBAAA,CAAuB,eAAhB,UAAQ,sBACfA,mBAAA,CAAmC,cAAAsB,gBAAA,CAA1BhB,QAAA,CAAA0B,cAAc,mB,GAEzBhC,mBAAA,CAGM,OAHNiC,WAGM,G,4BAFJjC,mBAAA,CAA6B,eAAtB,gBAAc,sBACrBA,mBAAA,CAAiE,cAAAsB,gBAAA,CAAxDhB,QAAA,CAAA4B,UAAU,CAAC5B,QAAA,CAAAoB,UAAU,EAAEC,OAAO,EAAEQ,aAAa,kB,OAI5DnC,mBAAA,CAKM,OALNoC,WAKM,GAJJpC,mBAAA,CAGS;IAHDqC,IAAI,EAAC,QAAQ;IAACzC,KAAK,EAAC,oBAAoB;IAAEO,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAgC,aAAA,IAAAhC,QAAA,CAAAgC,aAAA,IAAAjC,IAAA,CAAa;kCACpEL,mBAAA,CAA2B;IAAxBJ,KAAK,EAAC;EAAa,4B,iBAAK,kBAE7B,E,UAKNG,mBAAA,mCAAsC,EACtCC,mBAAA,CAkDM,OAlDNuC,WAkDM,G,4BAjDJvC,mBAAA,CAAsC,YAAlC,+BAA6B,sBACjCA,mBAAA,CA+CM,OA/CNwC,WA+CM,GA9CJxC,mBAAA,CASM,OATNyC,WASM,G,4BARJzC,mBAAA,CAA0D;IAAnD0C,GAAG,EAAC;EAAwB,GAAC,gBAAc,sB,gBAClD1C,mBAAA,CAME;IALA2C,EAAE,EAAC,wBAAwB;+DAClBhC,KAAA,CAAAiC,QAAQ,CAACC,sBAAsB,GAAAC,MAAA;IACxCT,IAAI,EAAC,MAAM;IACXU,QAAQ,EAAR,EAAQ;IACRC,WAAW,EAAC;iDAHHrC,KAAA,CAAAiC,QAAQ,CAACC,sBAAsB,E,KAM5C7C,mBAAA,CAgBM,OAhBNiD,WAgBM,G,4BAfJjD,mBAAA,CAAkE;IAA3D0C,GAAG,EAAC;EAAgC,GAAC,gBAAc,sB,gBAC1D1C,mBAAA,CAaS;IAZP2C,EAAE,EAAC,gCAAgC;+DAC1BhC,KAAA,CAAAiC,QAAQ,CAACM,8BAA8B,GAAAJ,MAAA;IAChDC,QAAQ,EAAR;gjBADSpC,KAAA,CAAAiC,QAAQ,CAACM,8BAA8B,E,KAapDlD,mBAAA,CASM,OATNmD,WASM,G,4BARJnD,mBAAA,CAA4D;IAArD0C,GAAG,EAAC;EAAyB,GAAC,iBAAe,sB,gBACpD1C,mBAAA,CAME;IALA2C,EAAE,EAAC,yBAAyB;+DACnBhC,KAAA,CAAAiC,QAAQ,CAACQ,uBAAuB,GAAAN,MAAA;IACzCT,IAAI,EAAC,KAAK;IACVU,QAAQ,EAAR,EAAQ;IACRC,WAAW,EAAC;iDAHHrC,KAAA,CAAAiC,QAAQ,CAACQ,uBAAuB,E,KAM7CpD,mBAAA,CAQM,OARNqD,WAQM,G,4BAPJrD,mBAAA,CAA8D;IAAvD0C,GAAG,EAAC;EAA2B,GAAC,iBAAe,sB,gBACtD1C,mBAAA,CAKY;IAJV2C,EAAE,EAAC,2BAA2B;+DACrBhC,KAAA,CAAAiC,QAAQ,CAACU,yBAAyB,GAAAR,MAAA;IAC3CS,IAAI,EAAC,GAAG;IACRP,WAAW,EAAC;iDAFHrC,KAAA,CAAAiC,QAAQ,CAACU,yBAAyB,E,gDASrDvD,mBAAA,iCAAoC,EACzBY,KAAA,CAAAC,WAAW,U,cAAtBf,mBAAA,CA4GM,OA5GN2D,WA4GM,G,4BA3GJxD,mBAAA,CAGM;IAHDJ,KAAK,EAAC;EAAa,IACtBI,mBAAA,CAAuC,YAAnC,gCAA8B,GAClCA,mBAAA,CAAgE,WAA7D,2DAAyD,E,sBAG9DA,mBAAA,CAqGM,OArGNyD,WAqGM,GApGJzD,mBAAA,CAiBM,OAjBN0D,WAiBM,G,4BAhBJ1D,mBAAA,CAAwD;IAAjD0C,GAAG,EAAC;EAAkB,GAAC,oBAAkB,sB,gBAChD1C,mBAAA,CAcS;IAbP2C,EAAE,EAAC,kBAAkB;+DACZhC,KAAA,CAAAiC,QAAQ,CAACe,mBAAmB,GAAAb,MAAA;IACrCC,QAAQ,EAAR,EAAQ;IACPa,QAAM,EAAAxD,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAuD,eAAA,IAAAvD,QAAA,CAAAuD,eAAA,IAAAxD,IAAA,CAAe;kCAExBL,mBAAA,CAAwC;IAAhC8D,KAAK,EAAC;EAAE,GAAC,gBAAc,uB,kBAC/BjE,mBAAA,CAMSkE,SAAA,QAAAC,WAAA,CALYrD,KAAA,CAAAsD,iBAAiB,EAA7BC,QAAQ;yBADjBrE,mBAAA,CAMS;MAJNsE,GAAG,EAAED,QAAQ,CAACvB,EAAE;MAChBmB,KAAK,EAAEI,QAAQ,CAACvB;wBAEduB,QAAQ,CAACE,aAAa,wBAAAC,WAAA;2FAVlB1D,KAAA,CAAAiC,QAAQ,CAACe,mBAAmB,E,KAezC3D,mBAAA,CASM,OATNsE,WASM,G,4BARJtE,mBAAA,CAAsD;IAA/C0C,GAAG,EAAC;EAAiB,GAAC,mBAAiB,sB,gBAC9C1C,mBAAA,CAMY;IALV2C,EAAE,EAAC,iBAAiB;+DACXhC,KAAA,CAAAiC,QAAQ,CAAC2B,eAAe,GAAAzB,MAAA;IACjCS,IAAI,EAAC,GAAG;IACRR,QAAQ,EAAR,EAAQ;IACRC,WAAW,EAAC;iDAHHrC,KAAA,CAAAiC,QAAQ,CAAC2B,eAAe,E,KAOrCvE,mBAAA,CAwBM,OAxBNwE,WAwBM,G,4BAvBJxE,mBAAA,CAA8D;IAAvD0C,GAAG,EAAC;EAAe,GAAC,6BAA2B,sBACtD1C,mBAAA,CAqBM,OArBNyE,WAqBM,GApBJzE,mBAAA,CASQ,SATR0E,WASQ,G,gBARN1E,mBAAA,CAKE;IAJAqC,IAAI,EAAC,OAAO;+DACH1B,KAAA,CAAAiC,QAAQ,CAAC+B,iBAAiB,GAAA7B,MAAA;IAClCgB,KAAK,EAAE,KAAK;IACbf,QAAQ,EAAR;kDAFSpC,KAAA,CAAAiC,QAAQ,CAAC+B,iBAAiB,E,+BAIrC3E,mBAAA,CAAkC;IAA5BJ,KAAK,EAAC;EAAc,6B,6CAAQ,oBAEpC,G,GACAI,mBAAA,CASQ,SATR4E,WASQ,G,gBARN5E,mBAAA,CAKE;IAJAqC,IAAI,EAAC,OAAO;iEACH1B,KAAA,CAAAiC,QAAQ,CAAC+B,iBAAiB,GAAA7B,MAAA;IAClCgB,KAAK,EAAE,IAAI;IACZf,QAAQ,EAAR;kDAFSpC,KAAA,CAAAiC,QAAQ,CAAC+B,iBAAiB,E,+BAIrC3E,mBAAA,CAAkC;IAA5BJ,KAAK,EAAC;EAAc,6B,6CAAQ,qBAEpC,G,OAIOe,KAAA,CAAAiC,QAAQ,CAAC+B,iBAAiB,I,cAArC9E,mBAAA,CASM,OATNgF,WASM,G,4BARJ7E,mBAAA,CAAkE;IAA3D0C,GAAG,EAAC;EAAuB,GAAC,yBAAuB,sB,gBAC1D1C,mBAAA,CAMY;IALV2C,EAAE,EAAC,uBAAuB;iEACjBhC,KAAA,CAAAiC,QAAQ,CAACkC,qBAAqB,GAAAhC,MAAA;IACvCS,IAAI,EAAC,GAAG;IACRR,QAAQ,EAAR,EAAQ;IACRC,WAAW,EAAC;iDAHHrC,KAAA,CAAAiC,QAAQ,CAACkC,qBAAqB,E,0CAO3C9E,mBAAA,CAsBM,OAtBN+E,WAsBM,G,4BArBJ/E,mBAAA,CAAiE;IAA1D0C,GAAG,EAAC;EAAoB,GAAC,2BAAyB,sBACzD1C,mBAAA,CAmBM,OAnBNgF,WAmBM,GAlBJhF,mBAAA,CAQQ,SARRiF,WAQQ,G,gBAPNjF,mBAAA,CAIE;IAHAqC,IAAI,EAAC,OAAO;iEACH1B,KAAA,CAAAiC,QAAQ,CAACsC,mBAAmB,GAAApC,MAAA;IACpCgB,KAAK,EAAE;kDADCnD,KAAA,CAAAiC,QAAQ,CAACsC,mBAAmB,E,+BAGvClF,mBAAA,CAAkC;IAA5BJ,KAAK,EAAC;EAAc,6B,6CAAQ,oBAEpC,G,GACAI,mBAAA,CAQQ,SARRmF,WAQQ,G,gBAPNnF,mBAAA,CAIE;IAHAqC,IAAI,EAAC,OAAO;iEACH1B,KAAA,CAAAiC,QAAQ,CAACsC,mBAAmB,GAAApC,MAAA;IACpCgB,KAAK,EAAE;kDADCnD,KAAA,CAAAiC,QAAQ,CAACsC,mBAAmB,E,+BAGvClF,mBAAA,CAAkC;IAA5BJ,KAAK,EAAC;EAAc,6B,6CAAQ,kBAEpC,G,OAIJI,mBAAA,CAQM,OARNoF,WAQM,G,4BAPJpF,mBAAA,CAAsD;IAA/C0C,GAAG,EAAC;EAAkB,GAAC,kBAAgB,sB,gBAC9C1C,mBAAA,CAKY;IAJV2C,EAAE,EAAC,kBAAkB;iEACZhC,KAAA,CAAAiC,QAAQ,CAACyC,gBAAgB,GAAAvC,MAAA;IAClCS,IAAI,EAAC,GAAG;IACRP,WAAW,EAAC;iDAFHrC,KAAA,CAAAiC,QAAQ,CAACyC,gBAAgB,E,8CAQ1CtF,mBAAA,4BAA+B,EACpBY,KAAA,CAAAC,WAAW,U,cAAtBf,mBAAA,CAoDM,OApDNyF,WAoDM,G,4BAnDJtF,mBAAA,CAGM;IAHDJ,KAAK,EAAC;EAAa,IACtBI,mBAAA,CAA4B,YAAxB,qBAAmB,GACvBA,mBAAA,CAA2C,WAAxC,sCAAoC,E,sBAGzCD,mBAAA,iBAAoB,EACpBC,mBAAA,CAcM,OAdNuF,WAcM,GAbJvF,mBAAA,CAYM,OAZNwF,WAYM,G,4BAXJxF,mBAAA,CAAsB,YAAlB,eAAa,sBACjBA,mBAAA,CASM,OATNyF,WASM,GARJzF,mBAAA,CAGM,OAHN0F,WAGM,G,4BAFJ1F,mBAAA,CAAmC,cAA7B,wBAAsB,sBAC5BA,mBAAA,CAA2C,cAArC,GAAC,GAAAsB,gBAAA,CAAGhB,QAAA,CAAAqF,cAAc,CAAChF,KAAA,CAAAiF,OAAO,kB,GAElC5F,mBAAA,CAGM,OAHN6F,WAGM,G,4BAFJ7F,mBAAA,CAAyB,cAAnB,cAAY,sBAClBA,mBAAA,CAA4C,cAAtC,GAAC,GAAAsB,gBAAA,CAAGhB,QAAA,CAAAqF,cAAc,CAAChF,KAAA,CAAAmF,QAAQ,kB,SAMzC/F,mBAAA,qBAAwB,EACxBC,mBAAA,CA2BM,OA3BN+F,WA2BM,G,4BA1BJ/F,mBAAA,CAA8B,YAA1B,uBAAqB,sBACzBA,mBAAA,CAwBM,OAxBNgG,WAwBM,I,kBAvBJnG,mBAAA,CAsBMkE,SAAA,QAAAC,WAAA,CArBarD,KAAA,CAAAsF,cAAc,EAAxBC,MAAM;yBADfrG,mBAAA,CAsBM;MApBHsE,GAAG,EAAE+B,MAAM,CAACvD,EAAE;MACf/C,KAAK,EAAAa,eAAA,EAAC,gBAAgB;QAAA0F,QAAA,EACFxF,KAAA,CAAAiC,QAAQ,CAACwD,iBAAiB,KAAKF,MAAM,CAACvD;MAAE;MAC3DxC,OAAK,EAAA2C,MAAA,IAAExC,QAAA,CAAA+F,mBAAmB,CAACH,MAAM,CAACvD,EAAE;QAErC3C,mBAAA,CAEM,OAFNsG,WAEM,GADJtG,mBAAA,CAAmD;MAA/CJ,KAAK,EAAAa,eAAA,CAAEH,QAAA,CAAAiG,cAAc,CAACL,MAAM,CAACM,WAAW;+BAE9CxG,mBAAA,CAGM,OAHNyG,WAGM,GAFJzG,mBAAA,CAAiC,YAAAsB,gBAAA,CAA1B4E,MAAM,CAACQ,WAAW,kBAChBR,MAAM,CAACS,WAAW,I,cAA3B9G,mBAAA,CAAyD,KAAA+G,WAAA,EAAAtF,gBAAA,CAAzB4E,MAAM,CAACS,WAAW,oB,qCAEpD3G,mBAAA,CAOM,OAPN6G,WAOM,G,gBANJ7G,mBAAA,CAKE;MAJAqC,IAAI,EAAC,OAAO;MACXyB,KAAK,EAAEoC,MAAM,CAACvD,EAAE;mEACRhC,KAAA,CAAAiC,QAAQ,CAACwD,iBAAiB,GAAAtD,MAAA;MACnCC,QAAQ,EAAR;0DADSpC,KAAA,CAAAiC,QAAQ,CAACwD,iBAAiB,E;6EAS/CrG,mBAAA,+BAAkC,EACvBY,KAAA,CAAAC,WAAW,U,cAAtBf,mBAAA,CA6EM,OA7ENiH,WA6EM,G,4BA5EJ9G,mBAAA,CAGM;IAHDJ,KAAK,EAAC;EAAa,IACtBI,mBAAA,CAA4B,YAAxB,qBAAmB,GACvBA,mBAAA,CAAsD,WAAnD,iDAA+C,E,sBAGpDA,mBAAA,CAyDM,OAzDN+G,WAyDM,GAxDJhH,mBAAA,iCAAoC,EACpCC,mBAAA,CAgBM,OAhBNgH,WAgBM,G,4BAfJhH,mBAAA,CAA6B,YAAzB,sBAAoB,sBACxBA,mBAAA,CAaM,OAbNiH,WAaM,GAZJjH,mBAAA,CAGM,OAHNkH,WAGM,G,4BAFJlH,mBAAA,CAAyB,eAAlB,YAAU,sBACjBA,mBAAA,CAAgC,cAAAsB,gBAAA,CAAvBhB,QAAA,CAAAiB,WAAW,mB,GAEtBvB,mBAAA,CAGM,OAHNmH,WAGM,G,4BAFJnH,mBAAA,CAAiC,eAA1B,oBAAkB,sBACzBA,mBAAA,CAAkG,cAAAsB,gBAAA,CAAzFX,KAAA,CAAAiC,QAAQ,CAACC,sBAAsB,IAAG,IAAE,GAAAvB,gBAAA,CAAGX,KAAA,CAAAiC,QAAQ,CAACM,8BAA8B,IAAG,GAAC,gB,GAE7FlD,mBAAA,CAGM,OAHNoH,WAGM,G,4BAFJpH,mBAAA,CAA+B,eAAxB,kBAAgB,sBACvBA,mBAAA,CAAmD,cAAAsB,gBAAA,CAA1CX,KAAA,CAAAiC,QAAQ,CAACQ,uBAAuB,iB,OAK/CrD,mBAAA,oBAAuB,EACvBC,mBAAA,CAoBM,OApBNqH,WAoBM,G,4BAnBJrH,mBAAA,CAA0B,YAAtB,mBAAiB,sBACrBA,mBAAA,CAiBM,OAjBNsH,WAiBM,GAhBJtH,mBAAA,CAGM,OAHNuH,WAGM,G,4BAFJvH,mBAAA,CAAuB,eAAhB,UAAQ,sBACfA,mBAAA,CAA2C,cAAAsB,gBAAA,CAAlChB,QAAA,CAAAkH,sBAAsB,mB,GAEjCxH,mBAAA,CAGM,OAHNyH,WAGM,G,4BAFJzH,mBAAA,CAAuB,eAAhB,UAAQ,sBACfA,mBAAA,CAA2C,cAAAsB,gBAAA,CAAlCX,KAAA,CAAAiC,QAAQ,CAAC2B,eAAe,iB,GAEnCvE,mBAAA,CAGM,OAHN0H,WAGM,G,4BAFJ1H,mBAAA,CAA6B,eAAtB,gBAAc,sBACrBA,mBAAA,CAA4D,cAAAsB,gBAAA,CAAnDX,KAAA,CAAAiC,QAAQ,CAAC+B,iBAAiB,gC,GAErC3E,mBAAA,CAGM,OAHN2H,WAGM,G,4BAFJ3H,mBAAA,CAA4B,eAArB,eAAa,sBACpBA,mBAAA,CAAiF,cAAAsB,gBAAA,CAAxEX,KAAA,CAAAiC,QAAQ,CAACsC,mBAAmB,mD,OAK3CnF,mBAAA,oBAAuB,EACvBC,mBAAA,CAYM,OAZN4H,WAYM,G,4BAXJ5H,mBAAA,CAA4B,YAAxB,qBAAmB,sBACvBA,mBAAA,CASM,OATN6H,WASM,GARJ7H,mBAAA,CAGM,OAHN8H,WAGM,G,4BAFJ9H,mBAAA,CAA8B,eAAvB,iBAAe,sBACtBA,mBAAA,CAAyC,cAAAsB,gBAAA,CAAhChB,QAAA,CAAAyH,oBAAoB,mB,GAE/B/H,mBAAA,CAGM,OAHNgI,WAGM,G,4BAFJhI,mBAAA,CAA4B,eAArB,eAAa,sBACpBA,mBAAA,CAA2D,QAA3DiI,WAA2D,EAAtC,GAAC,GAAA3G,gBAAA,CAAGhB,QAAA,CAAAqF,cAAc,CAAChF,KAAA,CAAAmF,QAAQ,kB,SAMxD/F,mBAAA,0BAA6B,EAC7BC,mBAAA,CAUM,OAVNkI,WAUM,GATJlI,mBAAA,CAQQ,SARRmI,WAQQ,G,gBAPNnI,mBAAA,CAIE;IAHAqC,IAAI,EAAC,UAAU;iEACN1B,KAAA,CAAAiC,QAAQ,CAACwF,cAAc,GAAAtF,MAAA;IAChCC,QAAQ,EAAR;qDADSpC,KAAA,CAAAiC,QAAQ,CAACwF,cAAc,E,+BAGlCpI,mBAAA,CAAqC;IAA/BJ,KAAK,EAAC;EAAiB,6B,6CAAQ,kBACtB,IAAAI,mBAAA,CAA+D;IAA5DqI,IAAI,EAAC,GAAG;IAAElI,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAY,cAAA,KAAAX,IAAA,KAAUC,QAAA,CAAAgI,SAAA,IAAAhI,QAAA,CAAAgI,SAAA,IAAAjI,IAAA,CAAS;KAAE,sBAAoB,G,6CAAI,mEAChF,G,4CAIJN,mBAAA,kBAAqB,EACrBC,mBAAA,CAqCM,OArCNuI,WAqCM,GAnCI5H,KAAA,CAAAC,WAAW,Q,cADnBf,mBAAA,CAQS;;IANPwC,IAAI,EAAC,QAAQ;IACbzC,KAAK,EAAC,eAAe;IACpBO,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEC,QAAA,CAAAkI,YAAA,IAAAlI,QAAA,CAAAkI,YAAA,IAAAnI,IAAA,CAAY;kCAEpBL,mBAAA,CAAmC;IAAhCJ,KAAK,EAAC;EAAqB,4B,iBAAK,YAErC,E,yCAGQe,KAAA,CAAAC,WAAW,Q,cADnBf,mBAAA,CASS;;IAPPwC,IAAI,EAAC,QAAQ;IACbzC,KAAK,EAAC,aAAa;IAClBO,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEC,QAAA,CAAAmI,QAAA,IAAAnI,QAAA,CAAAmI,QAAA,IAAApI,IAAA,CAAQ;IACfqI,QAAQ,GAAGpI,QAAA,CAAAqI,oBAAoB;mDACjC,QAEC,GAAA3I,mBAAA,CAAoC;IAAjCJ,KAAK,EAAC;EAAsB,2B,qEAIzBe,KAAA,CAAAC,WAAW,U,cADnBf,mBAAA,CAcS;;IAZPwC,IAAI,EAAC,QAAQ;IACbzC,KAAK,EAAC,YAAY;IACjB8I,QAAQ,EAAE/H,KAAA,CAAAiI,UAAU,KAAKjI,KAAA,CAAAiC,QAAQ,CAACwF;MAEnBzH,KAAA,CAAAiI,UAAU,I,cAA1B/I,mBAAA,CAGWkE,SAAA;IAAAI,GAAA;EAAA,I,4BAFTnE,mBAAA,CAAsC;IAAnCJ,KAAK,EAAC;EAAwB,6B,6CAAK,iBAExC,G,8CACAC,mBAAA,CAGWkE,SAAA;IAAAI,GAAA;EAAA,I,4BAFTnE,mBAAA,CAAkC;IAA/BJ,KAAK,EAAC;EAAoB,6B,6CAAK,kBAEpC,G", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}