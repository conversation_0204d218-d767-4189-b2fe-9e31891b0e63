{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.set.difference.v2.js\";\nimport \"core-js/modules/es.set.intersection.v2.js\";\nimport \"core-js/modules/es.set.is-disjoint-from.v2.js\";\nimport \"core-js/modules/es.set.is-subset-of.v2.js\";\nimport \"core-js/modules/es.set.is-superset-of.v2.js\";\nimport \"core-js/modules/es.set.symmetric-difference.v2.js\";\nimport \"core-js/modules/es.set.union.v2.js\";\nimport axios from 'axios';\nclass NotificationService {\n  constructor() {\n    this.eventSource = null;\n    this.listeners = new Map();\n    this.isConnected = false;\n    this.reconnectAttempts = 0;\n    this.maxReconnectAttempts = 5;\n    this.reconnectDelay = 1000; // Start with 1 second\n    this.maxReconnectDelay = 30000; // Max 30 seconds\n    this.baseURL = process.env.VUE_APP_API_URL || 'http://localhost:3000/api';\n  }\n\n  /**\n   * Connect to SSE stream (singleton pattern)\n   */\n  connect(userType = 'admin') {\n    // If already connected or connecting, just return\n    if (this.eventSource && (this.isConnected || this.eventSource.readyState === EventSource.CONNECTING)) {\n      console.log('Already connected or connecting to notification stream');\n      return Promise.resolve();\n    }\n\n    // If there's an existing connection in a bad state, clean it up first\n    if (this.eventSource) {\n      console.log('Cleaning up existing connection before reconnecting');\n      this.eventSource.close();\n      this.eventSource = null;\n      this.isConnected = false;\n    }\n    return new Promise((resolve, reject) => {\n      try {\n        const token = userType === 'admin' ? localStorage.getItem('adminToken') : localStorage.getItem('clientToken');\n        if (!token) {\n          console.error('No authentication token found');\n          reject(new Error('No authentication token found'));\n          return;\n        }\n\n        // EventSource doesn't support custom headers, so we pass the token as a query parameter\n        const url = `${this.baseURL}/notifications/stream?token=${encodeURIComponent(token)}`;\n        console.log('🔗 Attempting SSE connection to:', url.replace(/token=[^&]+/, 'token=***'));\n        this.eventSource = new EventSource(url);\n        this.eventSource.onopen = () => {\n          console.log('✅ Connected to notification stream');\n          this.isConnected = true;\n          this.reconnectAttempts = 0;\n          this.reconnectDelay = 1000;\n          this.emit('connected');\n          resolve();\n        };\n        this.eventSource.onmessage = event => {\n          try {\n            const notification = JSON.parse(event.data);\n            this.handleNotification(notification);\n          } catch (error) {\n            console.error('Failed to parse notification:', error);\n          }\n        };\n        this.eventSource.onerror = error => {\n          console.error('❌ SSE connection error:', error);\n          console.error('EventSource readyState:', this.eventSource?.readyState);\n          this.isConnected = false;\n          this.emit('error', error);\n\n          // Only reject if this is the initial connection attempt\n          if (this.reconnectAttempts === 0) {\n            reject(error);\n          }\n\n          // Attempt to reconnect\n          this.scheduleReconnect();\n        };\n      } catch (error) {\n        console.error('Failed to establish SSE connection:', error);\n        reject(error);\n        this.scheduleReconnect();\n      }\n    });\n  }\n\n  /**\n   * Disconnect from SSE stream\n   */\n  disconnect() {\n    if (this.eventSource) {\n      this.eventSource.close();\n      this.eventSource = null;\n      this.isConnected = false;\n      console.log('Disconnected from notification stream');\n      this.emit('disconnected');\n    }\n  }\n\n  /**\n   * Schedule reconnection attempt\n   */\n  scheduleReconnect() {\n    if (this.reconnectAttempts >= this.maxReconnectAttempts) {\n      console.error('Max reconnection attempts reached');\n      this.emit('max_reconnect_attempts');\n      return;\n    }\n    this.reconnectAttempts++;\n    const delay = Math.min(this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1), this.maxReconnectDelay);\n    console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);\n    setTimeout(() => {\n      if (!this.isConnected) {\n        this.connect();\n      }\n    }, delay);\n  }\n\n  /**\n   * Handle incoming notification\n   */\n  handleNotification(notification) {\n    console.log('📢 Received notification:', notification);\n\n    // Emit to specific type listeners\n    this.emit(notification.type, notification);\n\n    // Emit to general notification listeners\n    this.emit('notification', notification);\n\n    // Show browser notification if permission granted\n    this.showBrowserNotification(notification);\n  }\n\n  /**\n   * Show browser notification\n   */\n  showBrowserNotification(notification) {\n    if ('Notification' in window && Notification.permission === 'granted') {\n      const options = {\n        body: notification.message,\n        icon: '/favicon.ico',\n        badge: '/favicon.ico',\n        tag: `notification-${notification.id}`,\n        requireInteraction: notification.priority === 'high' || notification.priority === 'urgent'\n      };\n      const browserNotification = new Notification(notification.title, options);\n      browserNotification.onclick = () => {\n        window.focus();\n        this.emit('notification_click', notification);\n        browserNotification.close();\n      };\n\n      // Auto close after 5 seconds for normal priority\n      if (notification.priority !== 'high' && notification.priority !== 'urgent') {\n        setTimeout(() => {\n          browserNotification.close();\n        }, 5000);\n      }\n    }\n  }\n\n  /**\n   * Request browser notification permission\n   */\n  async requestNotificationPermission() {\n    if ('Notification' in window) {\n      const permission = await Notification.requestPermission();\n      return permission === 'granted';\n    }\n    return false;\n  }\n\n  /**\n   * Subscribe to notification events\n   */\n  on(event, callback) {\n    if (!this.listeners.has(event)) {\n      this.listeners.set(event, new Set());\n    }\n    this.listeners.get(event).add(callback);\n  }\n\n  /**\n   * Unsubscribe from notification events\n   */\n  off(event, callback) {\n    if (this.listeners.has(event)) {\n      this.listeners.get(event).delete(callback);\n    }\n  }\n\n  /**\n   * Emit event to listeners\n   */\n  emit(event, data = null) {\n    if (this.listeners.has(event)) {\n      this.listeners.get(event).forEach(callback => {\n        try {\n          callback(data);\n        } catch (error) {\n          console.error(`Error in notification listener for ${event}:`, error);\n        }\n      });\n    }\n  }\n\n  /**\n   * Get user notifications\n   */\n  async getNotifications(page = 1, limit = 20, unreadOnly = false) {\n    try {\n      const token = localStorage.getItem('adminToken') || localStorage.getItem('clientToken');\n      const response = await axios.get(`${this.baseURL}/notifications`, {\n        params: {\n          page,\n          limit,\n          unread_only: unreadOnly\n        },\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Failed to get notifications:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get unread notification count\n   */\n  async getUnreadCount() {\n    try {\n      const token = localStorage.getItem('adminToken') || localStorage.getItem('clientToken');\n      const response = await axios.get(`${this.baseURL}/notifications/unread-count`, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      return response.data.data.count;\n    } catch (error) {\n      console.error('Failed to get unread count:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Mark notification as read\n   */\n  async markAsRead(notificationId) {\n    try {\n      const token = localStorage.getItem('adminToken') || localStorage.getItem('clientToken');\n      const response = await axios.put(`${this.baseURL}/notifications/${notificationId}/read`, {}, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Failed to mark notification as read:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Mark all notifications as read\n   */\n  async markAllAsRead() {\n    try {\n      const token = localStorage.getItem('adminToken') || localStorage.getItem('clientToken');\n      const response = await axios.put(`${this.baseURL}/notifications/mark-all-read`, {}, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Failed to mark all notifications as read:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Send test notification (admin only)\n   */\n  async sendTestNotification(data) {\n    try {\n      const token = localStorage.getItem('adminToken');\n      const response = await axios.post(`${this.baseURL}/notifications/test`, data, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Failed to send test notification:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get notification statistics (admin only)\n   */\n  async getStatistics() {\n    try {\n      const token = localStorage.getItem('adminToken');\n      const response = await axios.get(`${this.baseURL}/notifications/statistics`, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Failed to get notification statistics:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Clean up old notifications (admin only)\n   */\n  async cleanupOldNotifications(days = 90) {\n    try {\n      const token = localStorage.getItem('adminToken');\n      const response = await axios.delete(`${this.baseURL}/notifications/cleanup`, {\n        params: {\n          days\n        },\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Failed to cleanup old notifications:', error);\n      throw error;\n    }\n  }\n}\n\n// Create singleton instance\nconst notificationService = new NotificationService();\nexport default notificationService;", "map": {"version": 3, "names": ["axios", "NotificationService", "constructor", "eventSource", "listeners", "Map", "isConnected", "reconnectAttempts", "maxReconnectAttempts", "reconnectDelay", "maxReconnectDelay", "baseURL", "process", "env", "VUE_APP_API_URL", "connect", "userType", "readyState", "EventSource", "CONNECTING", "console", "log", "Promise", "resolve", "close", "reject", "token", "localStorage", "getItem", "error", "Error", "url", "encodeURIComponent", "replace", "onopen", "emit", "onmessage", "event", "notification", "JSON", "parse", "data", "handleNotification", "onerror", "scheduleReconnect", "disconnect", "delay", "Math", "min", "pow", "setTimeout", "type", "showBrowserNotification", "window", "Notification", "permission", "options", "body", "message", "icon", "badge", "tag", "id", "requireInteraction", "priority", "browserNotification", "title", "onclick", "focus", "requestNotificationPermission", "requestPermission", "on", "callback", "has", "set", "Set", "get", "add", "off", "delete", "for<PERSON>ach", "getNotifications", "page", "limit", "unreadOnly", "response", "params", "unread_only", "headers", "getUnreadCount", "count", "mark<PERSON><PERSON><PERSON>", "notificationId", "put", "markAllAsRead", "sendTestNotification", "post", "getStatistics", "cleanupOldNotifications", "days", "notificationService"], "sources": ["D:/rhai_front_and_back/BOSFDR/src/services/notificationService.js"], "sourcesContent": ["import axios from 'axios';\n\nclass NotificationService {\n  constructor() {\n    this.eventSource = null;\n    this.listeners = new Map();\n    this.isConnected = false;\n    this.reconnectAttempts = 0;\n    this.maxReconnectAttempts = 5;\n    this.reconnectDelay = 1000; // Start with 1 second\n    this.maxReconnectDelay = 30000; // Max 30 seconds\n    this.baseURL = process.env.VUE_APP_API_URL || 'http://localhost:3000/api';\n  }\n\n  /**\n   * Connect to SSE stream (singleton pattern)\n   */\n  connect(userType = 'admin') {\n    // If already connected or connecting, just return\n    if (this.eventSource && (this.isConnected || this.eventSource.readyState === EventSource.CONNECTING)) {\n      console.log('Already connected or connecting to notification stream');\n      return Promise.resolve();\n    }\n\n    // If there's an existing connection in a bad state, clean it up first\n    if (this.eventSource) {\n      console.log('Cleaning up existing connection before reconnecting');\n      this.eventSource.close();\n      this.eventSource = null;\n      this.isConnected = false;\n    }\n\n    return new Promise((resolve, reject) => {\n      try {\n        const token = userType === 'admin'\n          ? localStorage.getItem('adminToken')\n          : localStorage.getItem('clientToken');\n\n        if (!token) {\n          console.error('No authentication token found');\n          reject(new Error('No authentication token found'));\n          return;\n        }\n\n        // EventSource doesn't support custom headers, so we pass the token as a query parameter\n        const url = `${this.baseURL}/notifications/stream?token=${encodeURIComponent(token)}`;\n        console.log('🔗 Attempting SSE connection to:', url.replace(/token=[^&]+/, 'token=***'));\n\n        this.eventSource = new EventSource(url);\n\n        this.eventSource.onopen = () => {\n          console.log('✅ Connected to notification stream');\n          this.isConnected = true;\n          this.reconnectAttempts = 0;\n          this.reconnectDelay = 1000;\n          this.emit('connected');\n          resolve();\n        };\n\n        this.eventSource.onmessage = (event) => {\n          try {\n            const notification = JSON.parse(event.data);\n            this.handleNotification(notification);\n          } catch (error) {\n            console.error('Failed to parse notification:', error);\n          }\n        };\n\n        this.eventSource.onerror = (error) => {\n          console.error('❌ SSE connection error:', error);\n          console.error('EventSource readyState:', this.eventSource?.readyState);\n          this.isConnected = false;\n          this.emit('error', error);\n\n          // Only reject if this is the initial connection attempt\n          if (this.reconnectAttempts === 0) {\n            reject(error);\n          }\n\n          // Attempt to reconnect\n          this.scheduleReconnect();\n        };\n\n      } catch (error) {\n        console.error('Failed to establish SSE connection:', error);\n        reject(error);\n        this.scheduleReconnect();\n      }\n    });\n  }\n\n  /**\n   * Disconnect from SSE stream\n   */\n  disconnect() {\n    if (this.eventSource) {\n      this.eventSource.close();\n      this.eventSource = null;\n      this.isConnected = false;\n      console.log('Disconnected from notification stream');\n      this.emit('disconnected');\n    }\n  }\n\n  /**\n   * Schedule reconnection attempt\n   */\n  scheduleReconnect() {\n    if (this.reconnectAttempts >= this.maxReconnectAttempts) {\n      console.error('Max reconnection attempts reached');\n      this.emit('max_reconnect_attempts');\n      return;\n    }\n\n    this.reconnectAttempts++;\n    const delay = Math.min(this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1), this.maxReconnectDelay);\n    \n    console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);\n    \n    setTimeout(() => {\n      if (!this.isConnected) {\n        this.connect();\n      }\n    }, delay);\n  }\n\n  /**\n   * Handle incoming notification\n   */\n  handleNotification(notification) {\n    console.log('📢 Received notification:', notification);\n    \n    // Emit to specific type listeners\n    this.emit(notification.type, notification);\n    \n    // Emit to general notification listeners\n    this.emit('notification', notification);\n    \n    // Show browser notification if permission granted\n    this.showBrowserNotification(notification);\n  }\n\n  /**\n   * Show browser notification\n   */\n  showBrowserNotification(notification) {\n    if ('Notification' in window && Notification.permission === 'granted') {\n      const options = {\n        body: notification.message,\n        icon: '/favicon.ico',\n        badge: '/favicon.ico',\n        tag: `notification-${notification.id}`,\n        requireInteraction: notification.priority === 'high' || notification.priority === 'urgent'\n      };\n\n      const browserNotification = new Notification(notification.title, options);\n      \n      browserNotification.onclick = () => {\n        window.focus();\n        this.emit('notification_click', notification);\n        browserNotification.close();\n      };\n\n      // Auto close after 5 seconds for normal priority\n      if (notification.priority !== 'high' && notification.priority !== 'urgent') {\n        setTimeout(() => {\n          browserNotification.close();\n        }, 5000);\n      }\n    }\n  }\n\n  /**\n   * Request browser notification permission\n   */\n  async requestNotificationPermission() {\n    if ('Notification' in window) {\n      const permission = await Notification.requestPermission();\n      return permission === 'granted';\n    }\n    return false;\n  }\n\n  /**\n   * Subscribe to notification events\n   */\n  on(event, callback) {\n    if (!this.listeners.has(event)) {\n      this.listeners.set(event, new Set());\n    }\n    this.listeners.get(event).add(callback);\n  }\n\n  /**\n   * Unsubscribe from notification events\n   */\n  off(event, callback) {\n    if (this.listeners.has(event)) {\n      this.listeners.get(event).delete(callback);\n    }\n  }\n\n  /**\n   * Emit event to listeners\n   */\n  emit(event, data = null) {\n    if (this.listeners.has(event)) {\n      this.listeners.get(event).forEach(callback => {\n        try {\n          callback(data);\n        } catch (error) {\n          console.error(`Error in notification listener for ${event}:`, error);\n        }\n      });\n    }\n  }\n\n  /**\n   * Get user notifications\n   */\n  async getNotifications(page = 1, limit = 20, unreadOnly = false) {\n    try {\n      const token = localStorage.getItem('adminToken') || localStorage.getItem('clientToken');\n      const response = await axios.get(`${this.baseURL}/notifications`, {\n        params: { page, limit, unread_only: unreadOnly },\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Failed to get notifications:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get unread notification count\n   */\n  async getUnreadCount() {\n    try {\n      const token = localStorage.getItem('adminToken') || localStorage.getItem('clientToken');\n      const response = await axios.get(`${this.baseURL}/notifications/unread-count`, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      return response.data.data.count;\n    } catch (error) {\n      console.error('Failed to get unread count:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Mark notification as read\n   */\n  async markAsRead(notificationId) {\n    try {\n      const token = localStorage.getItem('adminToken') || localStorage.getItem('clientToken');\n      const response = await axios.put(`${this.baseURL}/notifications/${notificationId}/read`, {}, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Failed to mark notification as read:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Mark all notifications as read\n   */\n  async markAllAsRead() {\n    try {\n      const token = localStorage.getItem('adminToken') || localStorage.getItem('clientToken');\n      const response = await axios.put(`${this.baseURL}/notifications/mark-all-read`, {}, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Failed to mark all notifications as read:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Send test notification (admin only)\n   */\n  async sendTestNotification(data) {\n    try {\n      const token = localStorage.getItem('adminToken');\n      const response = await axios.post(`${this.baseURL}/notifications/test`, data, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Failed to send test notification:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get notification statistics (admin only)\n   */\n  async getStatistics() {\n    try {\n      const token = localStorage.getItem('adminToken');\n      const response = await axios.get(`${this.baseURL}/notifications/statistics`, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Failed to get notification statistics:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Clean up old notifications (admin only)\n   */\n  async cleanupOldNotifications(days = 90) {\n    try {\n      const token = localStorage.getItem('adminToken');\n      const response = await axios.delete(`${this.baseURL}/notifications/cleanup`, {\n        params: { days },\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Failed to cleanup old notifications:', error);\n      throw error;\n    }\n  }\n}\n\n// Create singleton instance\nconst notificationService = new NotificationService();\n\nexport default notificationService;\n"], "mappings": ";;;;;;;;;AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,mBAAmB,CAAC;EACxBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,WAAW,GAAG,IAAI;IACvB,IAAI,CAACC,SAAS,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC1B,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,iBAAiB,GAAG,CAAC;IAC1B,IAAI,CAACC,oBAAoB,GAAG,CAAC;IAC7B,IAAI,CAACC,cAAc,GAAG,IAAI,CAAC,CAAC;IAC5B,IAAI,CAACC,iBAAiB,GAAG,KAAK,CAAC,CAAC;IAChC,IAAI,CAACC,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,eAAe,IAAI,2BAA2B;EAC3E;;EAEA;AACF;AACA;EACEC,OAAOA,CAACC,QAAQ,GAAG,OAAO,EAAE;IAC1B;IACA,IAAI,IAAI,CAACb,WAAW,KAAK,IAAI,CAACG,WAAW,IAAI,IAAI,CAACH,WAAW,CAACc,UAAU,KAAKC,WAAW,CAACC,UAAU,CAAC,EAAE;MACpGC,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;MACrE,OAAOC,OAAO,CAACC,OAAO,CAAC,CAAC;IAC1B;;IAEA;IACA,IAAI,IAAI,CAACpB,WAAW,EAAE;MACpBiB,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;MAClE,IAAI,CAAClB,WAAW,CAACqB,KAAK,CAAC,CAAC;MACxB,IAAI,CAACrB,WAAW,GAAG,IAAI;MACvB,IAAI,CAACG,WAAW,GAAG,KAAK;IAC1B;IAEA,OAAO,IAAIgB,OAAO,CAAC,CAACC,OAAO,EAAEE,MAAM,KAAK;MACtC,IAAI;QACF,MAAMC,KAAK,GAAGV,QAAQ,KAAK,OAAO,GAC9BW,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,GAClCD,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;QAEvC,IAAI,CAACF,KAAK,EAAE;UACVN,OAAO,CAACS,KAAK,CAAC,+BAA+B,CAAC;UAC9CJ,MAAM,CAAC,IAAIK,KAAK,CAAC,+BAA+B,CAAC,CAAC;UAClD;QACF;;QAEA;QACA,MAAMC,GAAG,GAAG,GAAG,IAAI,CAACpB,OAAO,+BAA+BqB,kBAAkB,CAACN,KAAK,CAAC,EAAE;QACrFN,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEU,GAAG,CAACE,OAAO,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;QAExF,IAAI,CAAC9B,WAAW,GAAG,IAAIe,WAAW,CAACa,GAAG,CAAC;QAEvC,IAAI,CAAC5B,WAAW,CAAC+B,MAAM,GAAG,MAAM;UAC9Bd,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;UACjD,IAAI,CAACf,WAAW,GAAG,IAAI;UACvB,IAAI,CAACC,iBAAiB,GAAG,CAAC;UAC1B,IAAI,CAACE,cAAc,GAAG,IAAI;UAC1B,IAAI,CAAC0B,IAAI,CAAC,WAAW,CAAC;UACtBZ,OAAO,CAAC,CAAC;QACX,CAAC;QAED,IAAI,CAACpB,WAAW,CAACiC,SAAS,GAAIC,KAAK,IAAK;UACtC,IAAI;YACF,MAAMC,YAAY,GAAGC,IAAI,CAACC,KAAK,CAACH,KAAK,CAACI,IAAI,CAAC;YAC3C,IAAI,CAACC,kBAAkB,CAACJ,YAAY,CAAC;UACvC,CAAC,CAAC,OAAOT,KAAK,EAAE;YACdT,OAAO,CAACS,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;UACvD;QACF,CAAC;QAED,IAAI,CAAC1B,WAAW,CAACwC,OAAO,GAAId,KAAK,IAAK;UACpCT,OAAO,CAACS,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;UAC/CT,OAAO,CAACS,KAAK,CAAC,yBAAyB,EAAE,IAAI,CAAC1B,WAAW,EAAEc,UAAU,CAAC;UACtE,IAAI,CAACX,WAAW,GAAG,KAAK;UACxB,IAAI,CAAC6B,IAAI,CAAC,OAAO,EAAEN,KAAK,CAAC;;UAEzB;UACA,IAAI,IAAI,CAACtB,iBAAiB,KAAK,CAAC,EAAE;YAChCkB,MAAM,CAACI,KAAK,CAAC;UACf;;UAEA;UACA,IAAI,CAACe,iBAAiB,CAAC,CAAC;QAC1B,CAAC;MAEH,CAAC,CAAC,OAAOf,KAAK,EAAE;QACdT,OAAO,CAACS,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;QAC3DJ,MAAM,CAACI,KAAK,CAAC;QACb,IAAI,CAACe,iBAAiB,CAAC,CAAC;MAC1B;IACF,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;EACEC,UAAUA,CAAA,EAAG;IACX,IAAI,IAAI,CAAC1C,WAAW,EAAE;MACpB,IAAI,CAACA,WAAW,CAACqB,KAAK,CAAC,CAAC;MACxB,IAAI,CAACrB,WAAW,GAAG,IAAI;MACvB,IAAI,CAACG,WAAW,GAAG,KAAK;MACxBc,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;MACpD,IAAI,CAACc,IAAI,CAAC,cAAc,CAAC;IAC3B;EACF;;EAEA;AACF;AACA;EACES,iBAAiBA,CAAA,EAAG;IAClB,IAAI,IAAI,CAACrC,iBAAiB,IAAI,IAAI,CAACC,oBAAoB,EAAE;MACvDY,OAAO,CAACS,KAAK,CAAC,mCAAmC,CAAC;MAClD,IAAI,CAACM,IAAI,CAAC,wBAAwB,CAAC;MACnC;IACF;IAEA,IAAI,CAAC5B,iBAAiB,EAAE;IACxB,MAAMuC,KAAK,GAAGC,IAAI,CAACC,GAAG,CAAC,IAAI,CAACvC,cAAc,GAAGsC,IAAI,CAACE,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC1C,iBAAiB,GAAG,CAAC,CAAC,EAAE,IAAI,CAACG,iBAAiB,CAAC;IAE7GU,OAAO,CAACC,GAAG,CAAC,8BAA8ByB,KAAK,eAAe,IAAI,CAACvC,iBAAiB,IAAI,IAAI,CAACC,oBAAoB,GAAG,CAAC;IAErH0C,UAAU,CAAC,MAAM;MACf,IAAI,CAAC,IAAI,CAAC5C,WAAW,EAAE;QACrB,IAAI,CAACS,OAAO,CAAC,CAAC;MAChB;IACF,CAAC,EAAE+B,KAAK,CAAC;EACX;;EAEA;AACF;AACA;EACEJ,kBAAkBA,CAACJ,YAAY,EAAE;IAC/BlB,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEiB,YAAY,CAAC;;IAEtD;IACA,IAAI,CAACH,IAAI,CAACG,YAAY,CAACa,IAAI,EAAEb,YAAY,CAAC;;IAE1C;IACA,IAAI,CAACH,IAAI,CAAC,cAAc,EAAEG,YAAY,CAAC;;IAEvC;IACA,IAAI,CAACc,uBAAuB,CAACd,YAAY,CAAC;EAC5C;;EAEA;AACF;AACA;EACEc,uBAAuBA,CAACd,YAAY,EAAE;IACpC,IAAI,cAAc,IAAIe,MAAM,IAAIC,YAAY,CAACC,UAAU,KAAK,SAAS,EAAE;MACrE,MAAMC,OAAO,GAAG;QACdC,IAAI,EAAEnB,YAAY,CAACoB,OAAO;QAC1BC,IAAI,EAAE,cAAc;QACpBC,KAAK,EAAE,cAAc;QACrBC,GAAG,EAAE,gBAAgBvB,YAAY,CAACwB,EAAE,EAAE;QACtCC,kBAAkB,EAAEzB,YAAY,CAAC0B,QAAQ,KAAK,MAAM,IAAI1B,YAAY,CAAC0B,QAAQ,KAAK;MACpF,CAAC;MAED,MAAMC,mBAAmB,GAAG,IAAIX,YAAY,CAAChB,YAAY,CAAC4B,KAAK,EAAEV,OAAO,CAAC;MAEzES,mBAAmB,CAACE,OAAO,GAAG,MAAM;QAClCd,MAAM,CAACe,KAAK,CAAC,CAAC;QACd,IAAI,CAACjC,IAAI,CAAC,oBAAoB,EAAEG,YAAY,CAAC;QAC7C2B,mBAAmB,CAACzC,KAAK,CAAC,CAAC;MAC7B,CAAC;;MAED;MACA,IAAIc,YAAY,CAAC0B,QAAQ,KAAK,MAAM,IAAI1B,YAAY,CAAC0B,QAAQ,KAAK,QAAQ,EAAE;QAC1Ed,UAAU,CAAC,MAAM;UACfe,mBAAmB,CAACzC,KAAK,CAAC,CAAC;QAC7B,CAAC,EAAE,IAAI,CAAC;MACV;IACF;EACF;;EAEA;AACF;AACA;EACE,MAAM6C,6BAA6BA,CAAA,EAAG;IACpC,IAAI,cAAc,IAAIhB,MAAM,EAAE;MAC5B,MAAME,UAAU,GAAG,MAAMD,YAAY,CAACgB,iBAAiB,CAAC,CAAC;MACzD,OAAOf,UAAU,KAAK,SAAS;IACjC;IACA,OAAO,KAAK;EACd;;EAEA;AACF;AACA;EACEgB,EAAEA,CAAClC,KAAK,EAAEmC,QAAQ,EAAE;IAClB,IAAI,CAAC,IAAI,CAACpE,SAAS,CAACqE,GAAG,CAACpC,KAAK,CAAC,EAAE;MAC9B,IAAI,CAACjC,SAAS,CAACsE,GAAG,CAACrC,KAAK,EAAE,IAAIsC,GAAG,CAAC,CAAC,CAAC;IACtC;IACA,IAAI,CAACvE,SAAS,CAACwE,GAAG,CAACvC,KAAK,CAAC,CAACwC,GAAG,CAACL,QAAQ,CAAC;EACzC;;EAEA;AACF;AACA;EACEM,GAAGA,CAACzC,KAAK,EAAEmC,QAAQ,EAAE;IACnB,IAAI,IAAI,CAACpE,SAAS,CAACqE,GAAG,CAACpC,KAAK,CAAC,EAAE;MAC7B,IAAI,CAACjC,SAAS,CAACwE,GAAG,CAACvC,KAAK,CAAC,CAAC0C,MAAM,CAACP,QAAQ,CAAC;IAC5C;EACF;;EAEA;AACF;AACA;EACErC,IAAIA,CAACE,KAAK,EAAEI,IAAI,GAAG,IAAI,EAAE;IACvB,IAAI,IAAI,CAACrC,SAAS,CAACqE,GAAG,CAACpC,KAAK,CAAC,EAAE;MAC7B,IAAI,CAACjC,SAAS,CAACwE,GAAG,CAACvC,KAAK,CAAC,CAAC2C,OAAO,CAACR,QAAQ,IAAI;QAC5C,IAAI;UACFA,QAAQ,CAAC/B,IAAI,CAAC;QAChB,CAAC,CAAC,OAAOZ,KAAK,EAAE;UACdT,OAAO,CAACS,KAAK,CAAC,sCAAsCQ,KAAK,GAAG,EAAER,KAAK,CAAC;QACtE;MACF,CAAC,CAAC;IACJ;EACF;;EAEA;AACF;AACA;EACE,MAAMoD,gBAAgBA,CAACC,IAAI,GAAG,CAAC,EAAEC,KAAK,GAAG,EAAE,EAAEC,UAAU,GAAG,KAAK,EAAE;IAC/D,IAAI;MACF,MAAM1D,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,IAAID,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;MACvF,MAAMyD,QAAQ,GAAG,MAAMrF,KAAK,CAAC4E,GAAG,CAAC,GAAG,IAAI,CAACjE,OAAO,gBAAgB,EAAE;QAChE2E,MAAM,EAAE;UAAEJ,IAAI;UAAEC,KAAK;UAAEI,WAAW,EAAEH;QAAW,CAAC;QAChDI,OAAO,EAAE;UACP,eAAe,EAAE,UAAU9D,KAAK;QAClC;MACF,CAAC,CAAC;MACF,OAAO2D,QAAQ,CAAC5C,IAAI;IACtB,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACdT,OAAO,CAACS,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAM4D,cAAcA,CAAA,EAAG;IACrB,IAAI;MACF,MAAM/D,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,IAAID,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;MACvF,MAAMyD,QAAQ,GAAG,MAAMrF,KAAK,CAAC4E,GAAG,CAAC,GAAG,IAAI,CAACjE,OAAO,6BAA6B,EAAE;QAC7E6E,OAAO,EAAE;UACP,eAAe,EAAE,UAAU9D,KAAK;QAClC;MACF,CAAC,CAAC;MACF,OAAO2D,QAAQ,CAAC5C,IAAI,CAACA,IAAI,CAACiD,KAAK;IACjC,CAAC,CAAC,OAAO7D,KAAK,EAAE;MACdT,OAAO,CAACS,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAM8D,UAAUA,CAACC,cAAc,EAAE;IAC/B,IAAI;MACF,MAAMlE,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,IAAID,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;MACvF,MAAMyD,QAAQ,GAAG,MAAMrF,KAAK,CAAC6F,GAAG,CAAC,GAAG,IAAI,CAAClF,OAAO,kBAAkBiF,cAAc,OAAO,EAAE,CAAC,CAAC,EAAE;QAC3FJ,OAAO,EAAE;UACP,eAAe,EAAE,UAAU9D,KAAK;QAClC;MACF,CAAC,CAAC;MACF,OAAO2D,QAAQ,CAAC5C,IAAI;IACtB,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACdT,OAAO,CAACS,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5D,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMiE,aAAaA,CAAA,EAAG;IACpB,IAAI;MACF,MAAMpE,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,IAAID,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;MACvF,MAAMyD,QAAQ,GAAG,MAAMrF,KAAK,CAAC6F,GAAG,CAAC,GAAG,IAAI,CAAClF,OAAO,8BAA8B,EAAE,CAAC,CAAC,EAAE;QAClF6E,OAAO,EAAE;UACP,eAAe,EAAE,UAAU9D,KAAK;QAClC;MACF,CAAC,CAAC;MACF,OAAO2D,QAAQ,CAAC5C,IAAI;IACtB,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACdT,OAAO,CAACS,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;MACjE,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMkE,oBAAoBA,CAACtD,IAAI,EAAE;IAC/B,IAAI;MACF,MAAMf,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;MAChD,MAAMyD,QAAQ,GAAG,MAAMrF,KAAK,CAACgG,IAAI,CAAC,GAAG,IAAI,CAACrF,OAAO,qBAAqB,EAAE8B,IAAI,EAAE;QAC5E+C,OAAO,EAAE;UACP,eAAe,EAAE,UAAU9D,KAAK;QAClC;MACF,CAAC,CAAC;MACF,OAAO2D,QAAQ,CAAC5C,IAAI;IACtB,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACdT,OAAO,CAACS,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMoE,aAAaA,CAAA,EAAG;IACpB,IAAI;MACF,MAAMvE,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;MAChD,MAAMyD,QAAQ,GAAG,MAAMrF,KAAK,CAAC4E,GAAG,CAAC,GAAG,IAAI,CAACjE,OAAO,2BAA2B,EAAE;QAC3E6E,OAAO,EAAE;UACP,eAAe,EAAE,UAAU9D,KAAK;QAClC;MACF,CAAC,CAAC;MACF,OAAO2D,QAAQ,CAAC5C,IAAI;IACtB,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACdT,OAAO,CAACS,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;MAC9D,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMqE,uBAAuBA,CAACC,IAAI,GAAG,EAAE,EAAE;IACvC,IAAI;MACF,MAAMzE,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;MAChD,MAAMyD,QAAQ,GAAG,MAAMrF,KAAK,CAAC+E,MAAM,CAAC,GAAG,IAAI,CAACpE,OAAO,wBAAwB,EAAE;QAC3E2E,MAAM,EAAE;UAAEa;QAAK,CAAC;QAChBX,OAAO,EAAE;UACP,eAAe,EAAE,UAAU9D,KAAK;QAClC;MACF,CAAC,CAAC;MACF,OAAO2D,QAAQ,CAAC5C,IAAI;IACtB,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACdT,OAAO,CAACS,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5D,MAAMA,KAAK;IACb;EACF;AACF;;AAEA;AACA,MAAMuE,mBAAmB,GAAG,IAAInG,mBAAmB,CAAC,CAAC;AAErD,eAAemG,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}