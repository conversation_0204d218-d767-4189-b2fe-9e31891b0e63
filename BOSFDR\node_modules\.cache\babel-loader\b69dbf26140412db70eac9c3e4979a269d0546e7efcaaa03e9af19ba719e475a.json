{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, resolveComponent as _resolveComponent, createVNode as _createVNode, createTextVNode as _createTextVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, normalizeClass as _normalizeClass } from \"vue\";\nconst _hoisted_1 = {\n  class: \"header-content\"\n};\nconst _hoisted_2 = {\n  class: \"header-left\"\n};\nconst _hoisted_3 = {\n  class: \"page-title\"\n};\nconst _hoisted_4 = {\n  class: \"header-actions\"\n};\nconst _hoisted_5 = {\n  class: \"user-info\"\n};\nconst _hoisted_6 = {\n  class: \"user-name\"\n};\nconst _hoisted_7 = {\n  key: 0,\n  class: \"dropdown-menu\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_ClientNotifications = _resolveComponent(\"ClientNotifications\");\n  return _openBlock(), _createElementBlock(\"header\", {\n    class: _normalizeClass([\"dashboard-header\", {\n      'sidebar-collapsed': _ctx.sidebarCollapsed\n    }])\n  }, [_createElementVNode(\"div\", _hoisted_1, [_createCommentVNode(\" Left Section \"), _createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"button\", {\n    class: \"sidebar-toggle\",\n    onClick: _cache[0] || (_cache[0] = (...args) => _ctx.handleSidebarToggle && _ctx.handleSidebarToggle(...args))\n  }, _cache[6] || (_cache[6] = [_createElementVNode(\"i\", {\n    class: \"fas fa-bars\"\n  }, null, -1 /* HOISTED */)])), _createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"h1\", null, _toDisplayString(_ctx.getPageTitle()), 1 /* TEXT */)])]), _createCommentVNode(\" Header Actions \"), _createElementVNode(\"div\", _hoisted_4, [_createCommentVNode(\" Notifications \"), _createVNode(_component_ClientNotifications, {\n    onNewNotification: _ctx.handleNewNotification,\n    onNotificationClick: _ctx.handleNotificationClick,\n    onError: _ctx.handleNotificationError\n  }, null, 8 /* PROPS */, [\"onNewNotification\", \"onNotificationClick\", \"onError\"]), _createCommentVNode(\" User Profile \"), _createElementVNode(\"div\", {\n    class: _normalizeClass([\"user-dropdown\", {\n      active: _ctx.showUserDropdown\n    }])\n  }, [_createElementVNode(\"button\", {\n    class: \"user-btn\",\n    onClick: _cache[1] || (_cache[1] = (...args) => _ctx.handleUserDropdownToggle && _ctx.handleUserDropdownToggle(...args))\n  }, [_cache[8] || (_cache[8] = _createElementVNode(\"div\", {\n    class: \"user-avatar\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-user-circle\"\n  })], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"span\", _hoisted_6, _toDisplayString(_ctx.userName), 1 /* TEXT */), _cache[7] || (_cache[7] = _createElementVNode(\"span\", {\n    class: \"user-role\"\n  }, \"Client\", -1 /* HOISTED */))]), _cache[9] || (_cache[9] = _createElementVNode(\"i\", {\n    class: \"fas fa-chevron-down dropdown-arrow\"\n  }, null, -1 /* HOISTED */))]), _ctx.showUserDropdown ? (_openBlock(), _createElementBlock(\"div\", _hoisted_7, [_createElementVNode(\"a\", {\n    href: \"#\",\n    class: \"dropdown-item\",\n    onClick: _cache[2] || (_cache[2] = $event => _ctx.handleMenuAction('profile'))\n  }, _cache[10] || (_cache[10] = [_createElementVNode(\"i\", {\n    class: \"fas fa-user me-2\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\" My Profile \")])), _createElementVNode(\"a\", {\n    href: \"#\",\n    class: \"dropdown-item\",\n    onClick: _cache[3] || (_cache[3] = $event => _ctx.handleMenuAction('settings'))\n  }, _cache[11] || (_cache[11] = [_createElementVNode(\"i\", {\n    class: \"fas fa-cog me-2\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\" Settings \")])), _createElementVNode(\"a\", {\n    href: \"#\",\n    class: \"dropdown-item\",\n    onClick: _cache[4] || (_cache[4] = $event => _ctx.handleMenuAction('account'))\n  }, _cache[12] || (_cache[12] = [_createElementVNode(\"i\", {\n    class: \"fas fa-id-card me-2\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\" Account Info \")])), _cache[14] || (_cache[14] = _createElementVNode(\"div\", {\n    class: \"dropdown-divider\"\n  }, null, -1 /* HOISTED */)), _createElementVNode(\"a\", {\n    href: \"#\",\n    class: \"dropdown-item\",\n    onClick: _cache[5] || (_cache[5] = (...args) => _ctx.handleLogout && _ctx.handleLogout(...args))\n  }, _cache[13] || (_cache[13] = [_createElementVNode(\"i\", {\n    class: \"fas fa-sign-out-alt me-2\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\" Logout \")]))])) : _createCommentVNode(\"v-if\", true)], 2 /* CLASS */)])])], 2 /* CLASS */);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_normalizeClass", "_ctx", "sidebarCollapsed", "_createElementVNode", "_hoisted_1", "_createCommentVNode", "_hoisted_2", "onClick", "_cache", "args", "handleSidebarToggle", "_hoisted_3", "_toDisplayString", "getPageTitle", "_hoisted_4", "_createVNode", "_component_ClientNotifications", "onNewNotification", "handleNewNotification", "onNotificationClick", "handleNotificationClick", "onError", "handleNotificationError", "active", "showUserDropdown", "handleUserDropdownToggle", "_hoisted_5", "_hoisted_6", "userName", "_hoisted_7", "href", "$event", "handleMenuAction", "handleLogout"], "sources": ["D:\\rhai_front_and_back\\BOSFDR\\src\\components\\client\\ClientHeader.vue"], "sourcesContent": ["<template>\n  <header class=\"dashboard-header\" :class=\"{ 'sidebar-collapsed': sidebarCollapsed }\">\n    <div class=\"header-content\">\n      <!-- Left Section -->\n      <div class=\"header-left\">\n        <button class=\"sidebar-toggle\" @click=\"handleSidebarToggle\">\n          <i class=\"fas fa-bars\"></i>\n        </button>\n        <div class=\"page-title\">\n          <h1>{{ getPageTitle() }}</h1>\n        </div>\n      </div>\n\n      <!-- Header Actions -->\n      <div class=\"header-actions\">\n        <!-- Notifications -->\n        <ClientNotifications\n          @new-notification=\"handleNewNotification\"\n          @notification-click=\"handleNotificationClick\"\n          @error=\"handleNotificationError\"\n        />\n\n        <!-- User Profile -->\n        <div class=\"user-dropdown\" :class=\"{ active: showUserDropdown }\">\n          <button class=\"user-btn\" @click=\"handleUserDropdownToggle\">\n            <div class=\"user-avatar\">\n              <i class=\"fas fa-user-circle\"></i>\n            </div>\n            <div class=\"user-info\">\n              <span class=\"user-name\">{{ userName }}</span>\n              <span class=\"user-role\">Client</span>\n            </div>\n            <i class=\"fas fa-chevron-down dropdown-arrow\"></i>\n          </button>\n          \n          <div v-if=\"showUserDropdown\" class=\"dropdown-menu\">\n            <a href=\"#\" class=\"dropdown-item\" @click=\"handleMenuAction('profile')\">\n              <i class=\"fas fa-user me-2\"></i>\n              My Profile\n            </a>\n            <a href=\"#\" class=\"dropdown-item\" @click=\"handleMenuAction('settings')\">\n              <i class=\"fas fa-cog me-2\"></i>\n              Settings\n            </a>\n            <a href=\"#\" class=\"dropdown-item\" @click=\"handleMenuAction('account')\">\n              <i class=\"fas fa-id-card me-2\"></i>\n              Account Info\n            </a>\n            <div class=\"dropdown-divider\"></div>\n            <a href=\"#\" class=\"dropdown-item\" @click=\"handleLogout\">\n              <i class=\"fas fa-sign-out-alt me-2\"></i>\n              Logout\n            </a>\n          </div>\n        </div>\n      </div>\n    </div>\n  </header>\n</template>\n\n<script src=\"./js/clientHeader.js\"></script>\n\n<style scoped src=\"./css/clientHeader.css\"></style>\n"], "mappings": ";;EAESA,KAAK,EAAC;AAAgB;;EAEpBA,KAAK,EAAC;AAAa;;EAIjBA,KAAK,EAAC;AAAY;;EAMpBA,KAAK,EAAC;AAAgB;;EAchBA,KAAK,EAAC;AAAW;;EACdA,KAAK,EAAC;AAAW;;;EAMEA,KAAK,EAAC;;;;uBAlC3CC,mBAAA,CAwDS;IAxDDD,KAAK,EAAAE,eAAA,EAAC,kBAAkB;MAAA,qBAAgCC,IAAA,CAAAC;IAAgB;MAC9EC,mBAAA,CAsDM,OAtDNC,UAsDM,GArDJC,mBAAA,kBAAqB,EACrBF,mBAAA,CAOM,OAPNG,UAOM,GANJH,mBAAA,CAES;IAFDL,KAAK,EAAC,gBAAgB;IAAES,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAER,IAAA,CAAAS,mBAAA,IAAAT,IAAA,CAAAS,mBAAA,IAAAD,IAAA,CAAmB;gCACxDN,mBAAA,CAA2B;IAAxBL,KAAK,EAAC;EAAa,2B,IAExBK,mBAAA,CAEM,OAFNQ,UAEM,GADJR,mBAAA,CAA6B,YAAAS,gBAAA,CAAtBX,IAAA,CAAAY,YAAY,mB,KAIvBR,mBAAA,oBAAuB,EACvBF,mBAAA,CAyCM,OAzCNW,UAyCM,GAxCJT,mBAAA,mBAAsB,EACtBU,YAAA,CAIEC,8BAAA;IAHCC,iBAAgB,EAAEhB,IAAA,CAAAiB,qBAAqB;IACvCC,mBAAkB,EAAElB,IAAA,CAAAmB,uBAAuB;IAC3CC,OAAK,EAAEpB,IAAA,CAAAqB;oFAGVjB,mBAAA,kBAAqB,EACrBF,mBAAA,CA+BM;IA/BDL,KAAK,EAAAE,eAAA,EAAC,eAAe;MAAAuB,MAAA,EAAmBtB,IAAA,CAAAuB;IAAgB;MAC3DrB,mBAAA,CASS;IATDL,KAAK,EAAC,UAAU;IAAES,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAER,IAAA,CAAAwB,wBAAA,IAAAxB,IAAA,CAAAwB,wBAAA,IAAAhB,IAAA,CAAwB;gCACvDN,mBAAA,CAEM;IAFDL,KAAK,EAAC;EAAa,IACtBK,mBAAA,CAAkC;IAA/BL,KAAK,EAAC;EAAoB,G,sBAE/BK,mBAAA,CAGM,OAHNuB,UAGM,GAFJvB,mBAAA,CAA6C,QAA7CwB,UAA6C,EAAAf,gBAAA,CAAlBX,IAAA,CAAA2B,QAAQ,kB,0BACnCzB,mBAAA,CAAqC;IAA/BL,KAAK,EAAC;EAAW,GAAC,QAAM,qB,6BAEhCK,mBAAA,CAAkD;IAA/CL,KAAK,EAAC;EAAoC,4B,GAGpCG,IAAA,CAAAuB,gBAAgB,I,cAA3BzB,mBAAA,CAkBM,OAlBN8B,UAkBM,GAjBJ1B,mBAAA,CAGI;IAHD2B,IAAI,EAAC,GAAG;IAAChC,KAAK,EAAC,eAAe;IAAES,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAuB,MAAA,IAAE9B,IAAA,CAAA+B,gBAAgB;kCACxD7B,mBAAA,CAAgC;IAA7BL,KAAK,EAAC;EAAkB,4B,iBAAK,cAElC,E,IACAK,mBAAA,CAGI;IAHD2B,IAAI,EAAC,GAAG;IAAChC,KAAK,EAAC,eAAe;IAAES,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAuB,MAAA,IAAE9B,IAAA,CAAA+B,gBAAgB;kCACxD7B,mBAAA,CAA+B;IAA5BL,KAAK,EAAC;EAAiB,4B,iBAAK,YAEjC,E,IACAK,mBAAA,CAGI;IAHD2B,IAAI,EAAC,GAAG;IAAChC,KAAK,EAAC,eAAe;IAAES,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAuB,MAAA,IAAE9B,IAAA,CAAA+B,gBAAgB;kCACxD7B,mBAAA,CAAmC;IAAhCL,KAAK,EAAC;EAAqB,4B,iBAAK,gBAErC,E,gCACAK,mBAAA,CAAoC;IAA/BL,KAAK,EAAC;EAAkB,6BAC7BK,mBAAA,CAGI;IAHD2B,IAAI,EAAC,GAAG;IAAChC,KAAK,EAAC,eAAe;IAAES,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAER,IAAA,CAAAgC,YAAA,IAAAhC,IAAA,CAAAgC,YAAA,IAAAxB,IAAA,CAAY;kCACpDN,mBAAA,CAAwC;IAArCL,KAAK,EAAC;EAA0B,4B,iBAAK,UAE1C,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}