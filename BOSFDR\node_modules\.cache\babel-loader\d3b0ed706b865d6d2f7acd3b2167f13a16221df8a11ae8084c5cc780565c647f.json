{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, createCommentVNode as _createCommentVNode, normalizeClass as _normalizeClass, createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, toDisplayString as _toDisplayString, openBlock as _openBlock, createElementBlock as _createElementBlock, renderList as _renderList, Fragment as _Fragment, normalizeStyle as _normalizeStyle } from \"vue\";\nconst _hoisted_1 = {\n  class: \"admin-dashboard\"\n};\nconst _hoisted_2 = {\n  class: \"dashboard-container\"\n};\nconst _hoisted_3 = {\n  class: \"container-fluid p-4\"\n};\nconst _hoisted_4 = {\n  class: \"row mb-4\"\n};\nconst _hoisted_5 = {\n  class: \"col-12\"\n};\nconst _hoisted_6 = {\n  class: \"d-flex justify-content-between align-items-center flex-wrap\"\n};\nconst _hoisted_7 = {\n  class: \"d-flex gap-2\"\n};\nconst _hoisted_8 = [\"disabled\"];\nconst _hoisted_9 = {\n  class: \"dropdown\"\n};\nconst _hoisted_10 = {\n  class: \"dropdown-menu\"\n};\nconst _hoisted_11 = {\n  class: \"row mb-4\"\n};\nconst _hoisted_12 = {\n  class: \"col-xl-3 col-md-6 mb-4\"\n};\nconst _hoisted_13 = {\n  class: \"card-body\"\n};\nconst _hoisted_14 = {\n  class: \"row no-gutters align-items-center\"\n};\nconst _hoisted_15 = {\n  class: \"col mr-2\"\n};\nconst _hoisted_16 = {\n  class: \"h5 mb-0 font-weight-bold text-gray-800\"\n};\nconst _hoisted_17 = {\n  class: \"col-xl-3 col-md-6 mb-4\"\n};\nconst _hoisted_18 = {\n  class: \"card-body\"\n};\nconst _hoisted_19 = {\n  class: \"row no-gutters align-items-center\"\n};\nconst _hoisted_20 = {\n  class: \"col mr-2\"\n};\nconst _hoisted_21 = {\n  class: \"h5 mb-0 font-weight-bold text-gray-800\"\n};\nconst _hoisted_22 = {\n  class: \"text-xs text-muted mt-1\"\n};\nconst _hoisted_23 = {\n  key: 0,\n  class: \"badge badge-danger ms-1\"\n};\nconst _hoisted_24 = {\n  key: 1\n};\nconst _hoisted_25 = {\n  class: \"col-xl-3 col-md-6 mb-4\"\n};\nconst _hoisted_26 = {\n  class: \"card-body\"\n};\nconst _hoisted_27 = {\n  class: \"row no-gutters align-items-center\"\n};\nconst _hoisted_28 = {\n  class: \"col mr-2\"\n};\nconst _hoisted_29 = {\n  class: \"h5 mb-0 font-weight-bold text-gray-800\"\n};\nconst _hoisted_30 = {\n  class: \"col-xl-3 col-md-6 mb-4\"\n};\nconst _hoisted_31 = {\n  class: \"card-body\"\n};\nconst _hoisted_32 = {\n  class: \"row no-gutters align-items-center\"\n};\nconst _hoisted_33 = {\n  class: \"col mr-2\"\n};\nconst _hoisted_34 = {\n  class: \"h5 mb-0 font-weight-bold text-gray-800\"\n};\nconst _hoisted_35 = {\n  class: \"text-xs text-muted mt-1\"\n};\nconst _hoisted_36 = {\n  class: \"row mb-4\"\n};\nconst _hoisted_37 = {\n  class: \"col-12\"\n};\nconst _hoisted_38 = {\n  class: \"card shadow\"\n};\nconst _hoisted_39 = {\n  class: \"card-header py-3 d-flex justify-content-between align-items-center\"\n};\nconst _hoisted_40 = {\n  class: \"d-flex gap-2\"\n};\nconst _hoisted_41 = [\"disabled\"];\nconst _hoisted_42 = {\n  class: \"dropdown\"\n};\nconst _hoisted_43 = {\n  class: \"dropdown-menu\"\n};\nconst _hoisted_44 = {\n  class: \"card-body\"\n};\nconst _hoisted_45 = {\n  class: \"row\"\n};\nconst _hoisted_46 = {\n  class: \"col-lg-4 mb-3\"\n};\nconst _hoisted_47 = {\n  class: \"border rounded p-3 h-100\"\n};\nconst _hoisted_48 = {\n  key: 0,\n  class: \"text-center text-muted py-3\"\n};\nconst _hoisted_49 = {\n  key: 1\n};\nconst _hoisted_50 = {\n  class: \"text-muted\"\n};\nconst _hoisted_51 = {\n  class: \"fw-bold\"\n};\nconst _hoisted_52 = {\n  class: \"text-danger\"\n};\nconst _hoisted_53 = [\"onClick\"];\nconst _hoisted_54 = {\n  key: 0,\n  class: \"text-center mt-2\"\n};\nconst _hoisted_55 = {\n  class: \"col-lg-4 mb-3\"\n};\nconst _hoisted_56 = {\n  class: \"border rounded p-3 h-100\"\n};\nconst _hoisted_57 = {\n  key: 0,\n  class: \"text-center text-muted py-3\"\n};\nconst _hoisted_58 = {\n  key: 1\n};\nconst _hoisted_59 = {\n  class: \"text-muted\"\n};\nconst _hoisted_60 = {\n  class: \"fw-bold\"\n};\nconst _hoisted_61 = {\n  class: \"text-info\"\n};\nconst _hoisted_62 = [\"onClick\"];\nconst _hoisted_63 = {\n  key: 0,\n  class: \"text-center mt-2\"\n};\nconst _hoisted_64 = {\n  class: \"col-lg-4 mb-3\"\n};\nconst _hoisted_65 = {\n  class: \"border rounded p-3 h-100\"\n};\nconst _hoisted_66 = {\n  class: \"mb-2\"\n};\nconst _hoisted_67 = {\n  class: \"d-flex justify-content-between\"\n};\nconst _hoisted_68 = {\n  class: \"badge bg-warning\"\n};\nconst _hoisted_69 = {\n  class: \"progress mb-2\",\n  style: {\n    \"height\": \"4px\"\n  }\n};\nconst _hoisted_70 = {\n  class: \"mb-2\"\n};\nconst _hoisted_71 = {\n  class: \"d-flex justify-content-between\"\n};\nconst _hoisted_72 = {\n  class: \"badge bg-info\"\n};\nconst _hoisted_73 = {\n  class: \"progress mb-2\",\n  style: {\n    \"height\": \"4px\"\n  }\n};\nconst _hoisted_74 = {\n  class: \"mb-2\"\n};\nconst _hoisted_75 = {\n  class: \"d-flex justify-content-between\"\n};\nconst _hoisted_76 = {\n  class: \"badge bg-success\"\n};\nconst _hoisted_77 = {\n  class: \"progress mb-2\",\n  style: {\n    \"height\": \"4px\"\n  }\n};\nconst _hoisted_78 = {\n  class: \"text-center mt-3\"\n};\nconst _hoisted_79 = {\n  class: \"row\"\n};\nconst _hoisted_80 = {\n  class: \"col-lg-8 mb-4\"\n};\nconst _hoisted_81 = {\n  class: \"card shadow\"\n};\nconst _hoisted_82 = {\n  class: \"card-header py-3 d-flex flex-row align-items-center justify-content-between\"\n};\nconst _hoisted_83 = {\n  class: \"m-0 font-weight-bold text-primary\"\n};\nconst _hoisted_84 = {\n  key: 0,\n  class: \"badge bg-success ms-2 pulse\"\n};\nconst _hoisted_85 = {\n  class: \"dropdown no-arrow\"\n};\nconst _hoisted_86 = {\n  class: \"dropdown-menu dropdown-menu-end shadow\"\n};\nconst _hoisted_87 = {\n  class: \"card-body\"\n};\nconst _hoisted_88 = {\n  key: 0,\n  class: \"text-center py-4\"\n};\nconst _hoisted_89 = {\n  key: 1,\n  class: \"text-center text-muted py-4\"\n};\nconst _hoisted_90 = {\n  key: 2,\n  class: \"activity-list\"\n};\nconst _hoisted_91 = {\n  class: \"me-3\"\n};\nconst _hoisted_92 = {\n  class: \"flex-grow-1\"\n};\nconst _hoisted_93 = {\n  class: \"d-flex justify-content-between align-items-start\"\n};\nconst _hoisted_94 = {\n  class: \"mb-1 text-dark\"\n};\nconst _hoisted_95 = {\n  class: \"mb-1 text-muted small\"\n};\nconst _hoisted_96 = {\n  class: \"small text-gray-500\"\n};\nconst _hoisted_97 = {\n  key: 0,\n  class: \"text-center pt-2\"\n};\nconst _hoisted_98 = {\n  class: \"col-lg-4 mb-4\"\n};\nconst _hoisted_99 = {\n  class: \"card shadow\"\n};\nconst _hoisted_100 = {\n  class: \"card-body\"\n};\nconst _hoisted_101 = {\n  class: \"d-grid gap-2\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_AdminHeader = _resolveComponent(\"AdminHeader\");\n  const _component_AdminSidebar = _resolveComponent(\"AdminSidebar\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_AdminHeader, {\n    userName: $data.adminData?.first_name || 'Admin',\n    showUserDropdown: $data.showUserDropdown,\n    sidebarCollapsed: $data.sidebarCollapsed,\n    activeMenu: $options.activeMenu,\n    onSidebarToggle: $options.handleSidebarToggle,\n    onUserDropdownToggle: $options.handleUserDropdownToggle,\n    onMenuAction: $options.handleMenuAction,\n    onLogout: $options.handleLogout\n  }, null, 8 /* PROPS */, [\"userName\", \"showUserDropdown\", \"sidebarCollapsed\", \"activeMenu\", \"onSidebarToggle\", \"onUserDropdownToggle\", \"onMenuAction\", \"onLogout\"]), _createCommentVNode(\" Mobile Overlay \"), _createElementVNode(\"div\", {\n    class: _normalizeClass([\"mobile-overlay\", {\n      active: !$data.sidebarCollapsed && $data.isMobile\n    }]),\n    onClick: _cache[0] || (_cache[0] = (...args) => $options.closeMobileSidebar && $options.closeMobileSidebar(...args))\n  }, null, 2 /* CLASS */), _createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_AdminSidebar, {\n    collapsed: $data.sidebarCollapsed,\n    activeMenu: $options.activeMenu,\n    totalUsers: $data.stats.totalUsers,\n    pendingRequests: $data.stats.activeRequests,\n    totalReports: $data.stats.completedToday,\n    onMenuChange: $options.handleMenuChange,\n    onLogout: $options.handleLogout,\n    onToggleSidebar: $options.handleSidebarToggle\n  }, null, 8 /* PROPS */, [\"collapsed\", \"activeMenu\", \"totalUsers\", \"pendingRequests\", \"totalReports\", \"onMenuChange\", \"onLogout\", \"onToggleSidebar\"]), _createElementVNode(\"main\", {\n    class: _normalizeClass([\"main-content\", {\n      'sidebar-collapsed': $data.sidebarCollapsed\n    }])\n  }, [_createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"div\", _hoisted_6, [_createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"button\", {\n    class: \"btn btn-outline-success btn-sm\",\n    onClick: _cache[1] || (_cache[1] = (...args) => $options.refreshDashboard && $options.refreshDashboard(...args)),\n    disabled: $data.loading\n  }, [_createElementVNode(\"i\", {\n    class: _normalizeClass([\"fas fa-sync-alt me-1\", {\n      'fa-spin': $data.loading\n    }])\n  }, null, 2 /* CLASS */), _cache[25] || (_cache[25] = _createTextVNode(\" Refresh \"))], 8 /* PROPS */, _hoisted_8), _createElementVNode(\"div\", _hoisted_9, [_cache[30] || (_cache[30] = _createElementVNode(\"button\", {\n    class: \"btn btn-success btn-sm dropdown-toggle\",\n    type: \"button\",\n    \"data-bs-toggle\": \"dropdown\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-plus me-1\"\n  }), _createTextVNode(\" Quick Actions \")], -1 /* HOISTED */)), _createElementVNode(\"ul\", _hoisted_10, [_createElementVNode(\"li\", null, [_createElementVNode(\"a\", {\n    class: \"dropdown-item\",\n    href: \"#\",\n    onClick: _cache[2] || (_cache[2] = $event => $options.navigateTo('/admin/users'))\n  }, _cache[26] || (_cache[26] = [_createElementVNode(\"i\", {\n    class: \"fas fa-user-plus me-2\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\"Add User \")]))]), _createElementVNode(\"li\", null, [_createElementVNode(\"a\", {\n    class: \"dropdown-item\",\n    href: \"#\",\n    onClick: _cache[3] || (_cache[3] = $event => $options.navigateTo('/admin/requests'))\n  }, _cache[27] || (_cache[27] = [_createElementVNode(\"i\", {\n    class: \"fas fa-file-alt me-2\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\"New Request \")]))]), _cache[29] || (_cache[29] = _createElementVNode(\"li\", null, [_createElementVNode(\"hr\", {\n    class: \"dropdown-divider\"\n  })], -1 /* HOISTED */)), _createElementVNode(\"li\", null, [_createElementVNode(\"a\", {\n    class: \"dropdown-item\",\n    href: \"#\",\n    onClick: _cache[4] || (_cache[4] = $event => $options.navigateTo('/admin/reports'))\n  }, _cache[28] || (_cache[28] = [_createElementVNode(\"i\", {\n    class: \"fas fa-chart-bar me-2\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\"Generate Report \")]))])])])])])])]), _createCommentVNode(\" Enhanced Request Management Dashboard Stats \"), _createElementVNode(\"div\", _hoisted_11, [_createCommentVNode(\" Total Requests \"), _createElementVNode(\"div\", _hoisted_12, [_createElementVNode(\"div\", {\n    class: \"card border-left-primary shadow h-100 py-2 stat-card\",\n    onClick: _cache[5] || (_cache[5] = $event => $options.navigateTo('/admin/requests'))\n  }, [_createElementVNode(\"div\", _hoisted_13, [_createElementVNode(\"div\", _hoisted_14, [_createElementVNode(\"div\", _hoisted_15, [_cache[31] || (_cache[31] = _createElementVNode(\"div\", {\n    class: \"text-xs font-weight-bold text-primary text-uppercase mb-1\"\n  }, \" Total Requests \", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_16, _toDisplayString($data.stats.totalRequests || 0), 1 /* TEXT */), _cache[32] || (_cache[32] = _createElementVNode(\"div\", {\n    class: \"text-xs text-muted mt-1\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-file-alt text-primary me-1\"\n  }), _createTextVNode(\" All document requests \")], -1 /* HOISTED */))]), _cache[33] || (_cache[33] = _createElementVNode(\"div\", {\n    class: \"col-auto\"\n  }, [_createElementVNode(\"div\", {\n    class: \"icon-circle bg-primary\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-file-alt text-white\"\n  })])], -1 /* HOISTED */))])])])]), _createCommentVNode(\" Pending Requests (High Priority) \"), _createElementVNode(\"div\", _hoisted_17, [_createElementVNode(\"div\", {\n    class: \"card border-left-warning shadow h-100 py-2 stat-card\",\n    onClick: _cache[6] || (_cache[6] = $event => $options.filterRequestsByStatus('pending'))\n  }, [_createElementVNode(\"div\", _hoisted_18, [_createElementVNode(\"div\", _hoisted_19, [_createElementVNode(\"div\", _hoisted_20, [_cache[35] || (_cache[35] = _createElementVNode(\"div\", {\n    class: \"text-xs font-weight-bold text-warning text-uppercase mb-1\"\n  }, \" Pending Requests \", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_21, _toDisplayString($data.stats.pendingRequests || 0), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_22, [_cache[34] || (_cache[34] = _createElementVNode(\"i\", {\n    class: \"fas fa-clock text-warning me-1\"\n  }, null, -1 /* HOISTED */)), $data.stats.urgentRequests > 0 ? (_openBlock(), _createElementBlock(\"span\", _hoisted_23, _toDisplayString($data.stats.urgentRequests) + \" urgent\", 1 /* TEXT */)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_24, \"Awaiting review\"))])]), _cache[36] || (_cache[36] = _createElementVNode(\"div\", {\n    class: \"col-auto\"\n  }, [_createElementVNode(\"div\", {\n    class: \"icon-circle bg-warning\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-clock text-white\"\n  })])], -1 /* HOISTED */))])])])]), _createCommentVNode(\" Processing Requests \"), _createElementVNode(\"div\", _hoisted_25, [_createElementVNode(\"div\", {\n    class: \"card border-left-info shadow h-100 py-2 stat-card\",\n    onClick: _cache[7] || (_cache[7] = $event => $options.filterRequestsByStatus('processing'))\n  }, [_createElementVNode(\"div\", _hoisted_26, [_createElementVNode(\"div\", _hoisted_27, [_createElementVNode(\"div\", _hoisted_28, [_cache[37] || (_cache[37] = _createElementVNode(\"div\", {\n    class: \"text-xs font-weight-bold text-info text-uppercase mb-1\"\n  }, \" Processing \", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_29, _toDisplayString($data.stats.processingRequests || 0), 1 /* TEXT */), _cache[38] || (_cache[38] = _createElementVNode(\"div\", {\n    class: \"text-xs text-muted mt-1\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-cog text-info me-1\"\n  }), _createTextVNode(\" Currently being processed \")], -1 /* HOISTED */))]), _cache[39] || (_cache[39] = _createElementVNode(\"div\", {\n    class: \"col-auto\"\n  }, [_createElementVNode(\"div\", {\n    class: \"icon-circle bg-info\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-cog text-white\"\n  })])], -1 /* HOISTED */))])])])]), _createCommentVNode(\" Completed Today \"), _createElementVNode(\"div\", _hoisted_30, [_createElementVNode(\"div\", {\n    class: \"card border-left-success shadow h-100 py-2 stat-card\",\n    onClick: _cache[8] || (_cache[8] = $event => $options.filterRequestsByDate('today'))\n  }, [_createElementVNode(\"div\", _hoisted_31, [_createElementVNode(\"div\", _hoisted_32, [_createElementVNode(\"div\", _hoisted_33, [_cache[41] || (_cache[41] = _createElementVNode(\"div\", {\n    class: \"text-xs font-weight-bold text-success text-uppercase mb-1\"\n  }, \" Completed Today \", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_34, _toDisplayString($data.stats.todayRequests || 0), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_35, [_cache[40] || (_cache[40] = _createElementVNode(\"i\", {\n    class: \"fas fa-check-circle text-success me-1\"\n  }, null, -1 /* HOISTED */)), _createTextVNode(\" ₱\" + _toDisplayString(($data.stats.todayRevenue || 0).toLocaleString()) + \" revenue \", 1 /* TEXT */)])]), _cache[42] || (_cache[42] = _createElementVNode(\"div\", {\n    class: \"col-auto\"\n  }, [_createElementVNode(\"div\", {\n    class: \"icon-circle bg-success\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-check-circle text-white\"\n  })])], -1 /* HOISTED */))])])])])]), _createCommentVNode(\" Request Management Quick Actions \"), _createElementVNode(\"div\", _hoisted_36, [_createElementVNode(\"div\", _hoisted_37, [_createElementVNode(\"div\", _hoisted_38, [_createElementVNode(\"div\", _hoisted_39, [_cache[50] || (_cache[50] = _createElementVNode(\"h6\", {\n    class: \"m-0 font-weight-bold text-primary\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-tasks me-2\"\n  }), _createTextVNode(\" Request Management Center \")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_40, [_createElementVNode(\"button\", {\n    class: \"btn btn-outline-primary btn-sm\",\n    onClick: _cache[9] || (_cache[9] = (...args) => $options.refreshDashboard && $options.refreshDashboard(...args)),\n    disabled: $data.loading\n  }, [_createElementVNode(\"i\", {\n    class: _normalizeClass([\"fas fa-sync-alt me-1\", {\n      'fa-spin': $data.loading\n    }])\n  }, null, 2 /* CLASS */), _cache[43] || (_cache[43] = _createTextVNode(\" Refresh \"))], 8 /* PROPS */, _hoisted_41), _createElementVNode(\"div\", _hoisted_42, [_cache[49] || (_cache[49] = _createElementVNode(\"button\", {\n    class: \"btn btn-primary btn-sm dropdown-toggle\",\n    type: \"button\",\n    \"data-bs-toggle\": \"dropdown\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-bolt me-1\"\n  }), _createTextVNode(\" Quick Actions \")], -1 /* HOISTED */)), _createElementVNode(\"ul\", _hoisted_43, [_createElementVNode(\"li\", null, [_createElementVNode(\"a\", {\n    class: \"dropdown-item\",\n    href: \"#\",\n    onClick: _cache[10] || (_cache[10] = $event => $options.navigateToRequests('pending'))\n  }, _cache[44] || (_cache[44] = [_createElementVNode(\"i\", {\n    class: \"fas fa-clock me-2 text-warning\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\"Review Pending Requests \")]))]), _createElementVNode(\"li\", null, [_createElementVNode(\"a\", {\n    class: \"dropdown-item\",\n    href: \"#\",\n    onClick: _cache[11] || (_cache[11] = $event => $options.navigateToRequests('urgent'))\n  }, _cache[45] || (_cache[45] = [_createElementVNode(\"i\", {\n    class: \"fas fa-exclamation-triangle me-2 text-danger\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\"Handle Urgent Requests \")]))]), _createElementVNode(\"li\", null, [_createElementVNode(\"a\", {\n    class: \"dropdown-item\",\n    href: \"#\",\n    onClick: _cache[12] || (_cache[12] = $event => $options.navigateToRequests('processing'))\n  }, _cache[46] || (_cache[46] = [_createElementVNode(\"i\", {\n    class: \"fas fa-cog me-2 text-info\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\"Monitor Processing \")]))]), _cache[48] || (_cache[48] = _createElementVNode(\"li\", null, [_createElementVNode(\"hr\", {\n    class: \"dropdown-divider\"\n  })], -1 /* HOISTED */)), _createElementVNode(\"li\", null, [_createElementVNode(\"a\", {\n    class: \"dropdown-item\",\n    href: \"#\",\n    onClick: _cache[13] || (_cache[13] = $event => $options.navigateTo('/admin/reports'))\n  }, _cache[47] || (_cache[47] = [_createElementVNode(\"i\", {\n    class: \"fas fa-chart-bar me-2 text-success\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\"Generate Reports \")]))])])])])]), _createElementVNode(\"div\", _hoisted_44, [_createElementVNode(\"div\", _hoisted_45, [_createCommentVNode(\" Priority Requests \"), _createElementVNode(\"div\", _hoisted_46, [_createElementVNode(\"div\", _hoisted_47, [_cache[53] || (_cache[53] = _createElementVNode(\"h6\", {\n    class: \"text-danger mb-3\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-exclamation-triangle me-2\"\n  }), _createTextVNode(\" Priority Requests \")], -1 /* HOISTED */)), $data.priorityRequests.length === 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_48, _cache[51] || (_cache[51] = [_createElementVNode(\"i\", {\n    class: \"fas fa-check-circle fa-2x mb-2 text-success\"\n  }, null, -1 /* HOISTED */), _createElementVNode(\"p\", {\n    class: \"mb-0\"\n  }, \"No urgent requests\", -1 /* HOISTED */)]))) : (_openBlock(), _createElementBlock(\"div\", _hoisted_49, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.priorityRequests.slice(0, 3), request => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: request.id,\n      class: \"d-flex justify-content-between align-items-center mb-2 p-2 bg-light rounded\"\n    }, [_createElementVNode(\"div\", null, [_createElementVNode(\"small\", _hoisted_50, _toDisplayString(request.request_number), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_51, _toDisplayString(request.document_type), 1 /* TEXT */), _createElementVNode(\"small\", _hoisted_52, _toDisplayString(request.priority) + \" Priority\", 1 /* TEXT */)]), _createElementVNode(\"button\", {\n      class: \"btn btn-sm btn-outline-primary\",\n      onClick: $event => $options.viewRequestDetails(request.id)\n    }, [...(_cache[52] || (_cache[52] = [_createElementVNode(\"i\", {\n      class: \"fas fa-eye\"\n    }, null, -1 /* HOISTED */)]))], 8 /* PROPS */, _hoisted_53)]);\n  }), 128 /* KEYED_FRAGMENT */)), $data.priorityRequests.length > 3 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_54, [_createElementVNode(\"button\", {\n    class: \"btn btn-sm btn-outline-danger\",\n    onClick: _cache[14] || (_cache[14] = $event => $options.navigateToRequests('urgent'))\n  }, \" View \" + _toDisplayString($data.priorityRequests.length - 3) + \" more urgent \", 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true)]))])]), _createCommentVNode(\" Recent Submissions \"), _createElementVNode(\"div\", _hoisted_55, [_createElementVNode(\"div\", _hoisted_56, [_cache[56] || (_cache[56] = _createElementVNode(\"h6\", {\n    class: \"text-info mb-3\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-clock me-2\"\n  }), _createTextVNode(\" Recent Submissions \")], -1 /* HOISTED */)), $data.recentRequests.length === 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_57, _cache[54] || (_cache[54] = [_createElementVNode(\"i\", {\n    class: \"fas fa-inbox fa-2x mb-2 text-gray-300\"\n  }, null, -1 /* HOISTED */), _createElementVNode(\"p\", {\n    class: \"mb-0\"\n  }, \"No recent submissions\", -1 /* HOISTED */)]))) : (_openBlock(), _createElementBlock(\"div\", _hoisted_58, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.recentRequests.slice(0, 3), request => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: request.id,\n      class: \"d-flex justify-content-between align-items-center mb-2 p-2 bg-light rounded\"\n    }, [_createElementVNode(\"div\", null, [_createElementVNode(\"small\", _hoisted_59, _toDisplayString($options.formatTimeAgo(request.requested_at)), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_60, _toDisplayString(request.document_type), 1 /* TEXT */), _createElementVNode(\"small\", _hoisted_61, _toDisplayString(request.client_name), 1 /* TEXT */)]), _createElementVNode(\"button\", {\n      class: \"btn btn-sm btn-outline-primary\",\n      onClick: $event => $options.viewRequestDetails(request.id)\n    }, [...(_cache[55] || (_cache[55] = [_createElementVNode(\"i\", {\n      class: \"fas fa-eye\"\n    }, null, -1 /* HOISTED */)]))], 8 /* PROPS */, _hoisted_62)]);\n  }), 128 /* KEYED_FRAGMENT */)), $data.recentRequests.length > 3 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_63, [_createElementVNode(\"button\", {\n    class: \"btn btn-sm btn-outline-info\",\n    onClick: _cache[15] || (_cache[15] = $event => $options.navigateToRequests('recent'))\n  }, \" View \" + _toDisplayString($data.recentRequests.length - 3) + \" more recent \", 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true)]))])]), _createCommentVNode(\" Processing Overview \"), _createElementVNode(\"div\", _hoisted_64, [_createElementVNode(\"div\", _hoisted_65, [_cache[61] || (_cache[61] = _createElementVNode(\"h6\", {\n    class: \"text-success mb-3\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-cogs me-2\"\n  }), _createTextVNode(\" Processing Overview \")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_66, [_createElementVNode(\"div\", _hoisted_67, [_cache[57] || (_cache[57] = _createElementVNode(\"span\", {\n    class: \"small\"\n  }, \"Pending Review\", -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_68, _toDisplayString($data.stats.pendingRequests || 0), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_69, [_createElementVNode(\"div\", {\n    class: \"progress-bar bg-warning\",\n    style: _normalizeStyle({\n      width: $options.getProgressPercentage('pending') + '%'\n    })\n  }, null, 4 /* STYLE */)])]), _createElementVNode(\"div\", _hoisted_70, [_createElementVNode(\"div\", _hoisted_71, [_cache[58] || (_cache[58] = _createElementVNode(\"span\", {\n    class: \"small\"\n  }, \"Processing\", -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_72, _toDisplayString($data.stats.processingRequests || 0), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_73, [_createElementVNode(\"div\", {\n    class: \"progress-bar bg-info\",\n    style: _normalizeStyle({\n      width: $options.getProgressPercentage('processing') + '%'\n    })\n  }, null, 4 /* STYLE */)])]), _createElementVNode(\"div\", _hoisted_74, [_createElementVNode(\"div\", _hoisted_75, [_cache[59] || (_cache[59] = _createElementVNode(\"span\", {\n    class: \"small\"\n  }, \"Completed\", -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_76, _toDisplayString($data.stats.completedRequests || 0), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_77, [_createElementVNode(\"div\", {\n    class: \"progress-bar bg-success\",\n    style: _normalizeStyle({\n      width: $options.getProgressPercentage('completed') + '%'\n    })\n  }, null, 4 /* STYLE */)])]), _createElementVNode(\"div\", _hoisted_78, [_createElementVNode(\"button\", {\n    class: \"btn btn-sm btn-success\",\n    onClick: _cache[16] || (_cache[16] = $event => $options.navigateTo('/admin/requests'))\n  }, _cache[60] || (_cache[60] = [_createElementVNode(\"i\", {\n    class: \"fas fa-list me-1\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\" View All Requests \")]))])])])])])])])]), _createCommentVNode(\" Recent Activity \"), _createElementVNode(\"div\", _hoisted_79, [_createElementVNode(\"div\", _hoisted_80, [_createElementVNode(\"div\", _hoisted_81, [_createElementVNode(\"div\", _hoisted_82, [_createElementVNode(\"h6\", _hoisted_83, [_cache[63] || (_cache[63] = _createElementVNode(\"i\", {\n    class: \"fas fa-history me-2\"\n  }, null, -1 /* HOISTED */)), _cache[64] || (_cache[64] = _createTextVNode(\" Recent Activity \")), $data.recentActivity.length > 0 ? (_openBlock(), _createElementBlock(\"span\", _hoisted_84, _cache[62] || (_cache[62] = [_createElementVNode(\"i\", {\n    class: \"fas fa-circle\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\" Live \")]))) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_85, [_cache[70] || (_cache[70] = _createElementVNode(\"button\", {\n    class: \"btn btn-link text-gray-400 p-0\",\n    type: \"button\",\n    \"data-bs-toggle\": \"dropdown\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-ellipsis-v\"\n  })], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_86, [_cache[68] || (_cache[68] = _createElementVNode(\"div\", {\n    class: \"dropdown-header\"\n  }, \"Actions:\", -1 /* HOISTED */)), _createElementVNode(\"a\", {\n    class: \"dropdown-item\",\n    href: \"#\",\n    onClick: _cache[17] || (_cache[17] = $event => $options.navigateTo('/admin/activity-logs'))\n  }, _cache[65] || (_cache[65] = [_createElementVNode(\"i\", {\n    class: \"fas fa-list me-2\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\"View All \")])), _createElementVNode(\"a\", {\n    class: \"dropdown-item\",\n    href: \"#\",\n    onClick: _cache[18] || (_cache[18] = (...args) => $options.exportActivity && $options.exportActivity(...args))\n  }, _cache[66] || (_cache[66] = [_createElementVNode(\"i\", {\n    class: \"fas fa-download me-2\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\"Export \")])), _cache[69] || (_cache[69] = _createElementVNode(\"div\", {\n    class: \"dropdown-divider\"\n  }, null, -1 /* HOISTED */)), _createElementVNode(\"a\", {\n    class: \"dropdown-item\",\n    href: \"#\",\n    onClick: _cache[19] || (_cache[19] = (...args) => $options.refreshDashboard && $options.refreshDashboard(...args))\n  }, _cache[67] || (_cache[67] = [_createElementVNode(\"i\", {\n    class: \"fas fa-sync-alt me-2\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\"Refresh \")]))])])]), _createElementVNode(\"div\", _hoisted_87, [$data.loading ? (_openBlock(), _createElementBlock(\"div\", _hoisted_88, _cache[71] || (_cache[71] = [_createElementVNode(\"div\", {\n    class: \"spinner-border text-primary\",\n    role: \"status\"\n  }, [_createElementVNode(\"span\", {\n    class: \"visually-hidden\"\n  }, \"Loading...\")], -1 /* HOISTED */), _createElementVNode(\"p\", {\n    class: \"text-muted mt-2\"\n  }, \"Loading recent activity...\", -1 /* HOISTED */)]))) : $data.recentActivity.length === 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_89, _cache[72] || (_cache[72] = [_createElementVNode(\"i\", {\n    class: \"fas fa-inbox fa-3x mb-3 text-gray-300\"\n  }, null, -1 /* HOISTED */), _createElementVNode(\"h6\", {\n    class: \"text-gray-600\"\n  }, \"No recent activity\", -1 /* HOISTED */), _createElementVNode(\"p\", {\n    class: \"small\"\n  }, \"Activity will appear here as users interact with the system.\", -1 /* HOISTED */)]))) : (_openBlock(), _createElementBlock(\"div\", _hoisted_90, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.recentActivity.slice(0, 5), (activity, index) => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: activity.id,\n      class: _normalizeClass([\"activity-item d-flex align-items-start mb-3 pb-3\", {\n        'border-bottom': index < $data.recentActivity.slice(0, 5).length - 1\n      }])\n    }, [_createElementVNode(\"div\", _hoisted_91, [_createElementVNode(\"div\", {\n      class: _normalizeClass([\"icon-circle\", $options.getActivityIconClass(activity.type)])\n    }, [_createElementVNode(\"i\", {\n      class: _normalizeClass([$options.getActivityIcon(activity.type), \"text-white\"])\n    }, null, 2 /* CLASS */)], 2 /* CLASS */)]), _createElementVNode(\"div\", _hoisted_92, [_createElementVNode(\"div\", _hoisted_93, [_createElementVNode(\"div\", null, [_createElementVNode(\"h6\", _hoisted_94, _toDisplayString(activity.title || activity.description), 1 /* TEXT */), _createElementVNode(\"p\", _hoisted_95, _toDisplayString(activity.details || activity.description), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_96, [_cache[73] || (_cache[73] = _createElementVNode(\"i\", {\n      class: \"fas fa-clock me-1\"\n    }, null, -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($options.formatDate(activity.created_at)), 1 /* TEXT */)])]), _createElementVNode(\"span\", {\n      class: _normalizeClass([\"badge\", $options.getActivityBadgeClass(activity.status)])\n    }, _toDisplayString(activity.status || 'Completed'), 3 /* TEXT, CLASS */)])])], 2 /* CLASS */);\n  }), 128 /* KEYED_FRAGMENT */)), $data.recentActivity.length > 5 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_97, [_createElementVNode(\"button\", {\n    class: \"btn btn-outline-primary btn-sm\",\n    onClick: _cache[20] || (_cache[20] = $event => $options.navigateTo('/admin/activity-logs'))\n  }, [_cache[74] || (_cache[74] = _createElementVNode(\"i\", {\n    class: \"fas fa-plus me-1\"\n  }, null, -1 /* HOISTED */)), _createTextVNode(\" View \" + _toDisplayString($data.recentActivity.length - 5) + \" more activities \", 1 /* TEXT */)])])) : _createCommentVNode(\"v-if\", true)]))])])]), _createCommentVNode(\" Quick Actions \"), _createElementVNode(\"div\", _hoisted_98, [_createElementVNode(\"div\", _hoisted_99, [_cache[79] || (_cache[79] = _createElementVNode(\"div\", {\n    class: \"card-header py-3\"\n  }, [_createElementVNode(\"h6\", {\n    class: \"m-0 font-weight-bold text-primary\"\n  }, \"Quick Actions\")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_100, [_createElementVNode(\"div\", _hoisted_101, [_createElementVNode(\"button\", {\n    class: \"btn btn-primary btn-sm\",\n    onClick: _cache[21] || (_cache[21] = $event => $options.navigateTo('/admin/users'))\n  }, _cache[75] || (_cache[75] = [_createElementVNode(\"i\", {\n    class: \"fas fa-users me-2\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\" Manage Users \")])), _createElementVNode(\"button\", {\n    class: \"btn btn-success btn-sm\",\n    onClick: _cache[22] || (_cache[22] = $event => $options.navigateTo('/admin/requests'))\n  }, _cache[76] || (_cache[76] = [_createElementVNode(\"i\", {\n    class: \"fas fa-file-alt me-2\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\" View Requests \")])), _createElementVNode(\"button\", {\n    class: \"btn btn-info btn-sm\",\n    onClick: _cache[23] || (_cache[23] = $event => $options.navigateTo('/admin/reports'))\n  }, _cache[77] || (_cache[77] = [_createElementVNode(\"i\", {\n    class: \"fas fa-chart-bar me-2\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\" Generate Reports \")])), _createElementVNode(\"button\", {\n    class: \"btn btn-warning btn-sm\",\n    onClick: _cache[24] || (_cache[24] = $event => $options.navigateTo('/admin/settings'))\n  }, _cache[78] || (_cache[78] = [_createElementVNode(\"i\", {\n    class: \"fas fa-cog me-2\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\" System Settings \")]))])])])])])])], 2 /* CLASS */)])]);\n}", "map": {"version": 3, "names": ["class", "style", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_AdminHeader", "userName", "$data", "adminData", "first_name", "showUserDropdown", "sidebarCollapsed", "activeMenu", "$options", "onSidebarToggle", "handleSidebarToggle", "onUserDropdownToggle", "handleUserDropdownToggle", "onMenuAction", "handleMenuAction", "onLogout", "handleLogout", "_createCommentVNode", "_createElementVNode", "_normalizeClass", "active", "isMobile", "onClick", "_cache", "args", "closeMobileSidebar", "_hoisted_2", "_component_AdminSidebar", "collapsed", "totalUsers", "stats", "pendingRequests", "activeRequests", "totalReports", "completedToday", "onMenuChange", "handleMenuChange", "onToggleSidebar", "_hoisted_3", "_hoisted_4", "_hoisted_5", "_hoisted_6", "_hoisted_7", "refreshDashboard", "disabled", "loading", "_hoisted_9", "type", "_hoisted_10", "href", "$event", "navigateTo", "_hoisted_11", "_hoisted_12", "_hoisted_13", "_hoisted_14", "_hoisted_15", "_hoisted_16", "_toDisplayString", "totalRequests", "_hoisted_17", "filterRequestsByStatus", "_hoisted_18", "_hoisted_19", "_hoisted_20", "_hoisted_21", "_hoisted_22", "urgentRequests", "_hoisted_23", "_hoisted_24", "_hoisted_25", "_hoisted_26", "_hoisted_27", "_hoisted_28", "_hoisted_29", "processingRequests", "_hoisted_30", "filterRequestsByDate", "_hoisted_31", "_hoisted_32", "_hoisted_33", "_hoisted_34", "todayRequests", "_hoisted_35", "todayRevenue", "toLocaleString", "_hoisted_36", "_hoisted_37", "_hoisted_38", "_hoisted_39", "_hoisted_40", "_hoisted_42", "_hoisted_43", "navigateToRequests", "_hoisted_44", "_hoisted_45", "_hoisted_46", "_hoisted_47", "priorityRequests", "length", "_hoisted_48", "_hoisted_49", "_Fragment", "_renderList", "slice", "request", "key", "id", "_hoisted_50", "request_number", "_hoisted_51", "document_type", "_hoisted_52", "priority", "viewRequestDetails", "_hoisted_54", "_hoisted_55", "_hoisted_56", "recentRequests", "_hoisted_57", "_hoisted_58", "_hoisted_59", "formatTimeAgo", "requested_at", "_hoisted_60", "_hoisted_61", "client_name", "_hoisted_63", "_hoisted_64", "_hoisted_65", "_hoisted_66", "_hoisted_67", "_hoisted_68", "_hoisted_69", "_normalizeStyle", "width", "getProgressPercentage", "_hoisted_70", "_hoisted_71", "_hoisted_72", "_hoisted_73", "_hoisted_74", "_hoisted_75", "_hoisted_76", "completedRequests", "_hoisted_77", "_hoisted_78", "_hoisted_79", "_hoisted_80", "_hoisted_81", "_hoisted_82", "_hoisted_83", "recentActivity", "_hoisted_84", "_hoisted_85", "_hoisted_86", "exportActivity", "_hoisted_87", "_hoisted_88", "role", "_hoisted_89", "_hoisted_90", "activity", "index", "_hoisted_91", "getActivityIconClass", "getActivityIcon", "_hoisted_92", "_hoisted_93", "_hoisted_94", "title", "description", "_hoisted_95", "details", "_hoisted_96", "formatDate", "created_at", "getActivityBadgeClass", "status", "_hoisted_97", "_hoisted_98", "_hoisted_99", "_hoisted_100", "_hoisted_101"], "sources": ["D:\\rhai_front_and_back\\BOSFDR\\src\\components\\admin\\AdminDashboard.vue"], "sourcesContent": ["<template>\n  <div class=\"admin-dashboard\">\n    <AdminHeader\n      :userName=\"adminData?.first_name || 'Admin'\"\n      :showUserDropdown=\"showUserDropdown\"\n      :sidebarCollapsed=\"sidebarCollapsed\"\n      :activeMenu=\"activeMenu\"\n      @sidebar-toggle=\"handleSidebarToggle\"\n      @user-dropdown-toggle=\"handleUserDropdownToggle\"\n      @menu-action=\"handleMenuAction\"\n      @logout=\"handleLogout\"\n    />\n\n    <!-- Mobile Overlay -->\n    <div\n      class=\"mobile-overlay\"\n      :class=\"{ active: !sidebarCollapsed && isMobile }\"\n      @click=\"closeMobileSidebar\"\n    ></div>\n\n    <div class=\"dashboard-container\">\n      <AdminSidebar\n        :collapsed=\"sidebarCollapsed\"\n        :activeMenu=\"activeMenu\"\n        :totalUsers=\"stats.totalUsers\"\n        :pendingRequests=\"stats.activeRequests\"\n        :totalReports=\"stats.completedToday\"\n        @menu-change=\"handleMenuChange\"\n        @logout=\"handleLogout\"\n        @toggle-sidebar=\"handleSidebarToggle\"\n      />\n      <main class=\"main-content\" :class=\"{ 'sidebar-collapsed': sidebarCollapsed }\">\n        <div class=\"container-fluid p-4\">\n          <div class=\"row mb-4\">\n            <div class=\"col-12\">\n              <div class=\"d-flex justify-content-between align-items-center flex-wrap\">\n\n                <div class=\"d-flex gap-2\">\n                  <button class=\"btn btn-outline-success btn-sm\" @click=\"refreshDashboard\" :disabled=\"loading\">\n                    <i class=\"fas fa-sync-alt me-1\" :class=\"{ 'fa-spin': loading }\"></i>\n                    Refresh\n                  </button>\n                  <div class=\"dropdown\">\n                    <button class=\"btn btn-success btn-sm dropdown-toggle\" type=\"button\" data-bs-toggle=\"dropdown\">\n                      <i class=\"fas fa-plus me-1\"></i>\n                      Quick Actions\n                    </button>\n                    <ul class=\"dropdown-menu\">\n                      <li><a class=\"dropdown-item\" href=\"#\" @click=\"navigateTo('/admin/users')\">\n                        <i class=\"fas fa-user-plus me-2\"></i>Add User\n                      </a></li>\n                      <li><a class=\"dropdown-item\" href=\"#\" @click=\"navigateTo('/admin/requests')\">\n                        <i class=\"fas fa-file-alt me-2\"></i>New Request\n                      </a></li>\n                      <li><hr class=\"dropdown-divider\"></li>\n                      <li><a class=\"dropdown-item\" href=\"#\" @click=\"navigateTo('/admin/reports')\">\n                        <i class=\"fas fa-chart-bar me-2\"></i>Generate Report\n                      </a></li>\n                    </ul>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Enhanced Request Management Dashboard Stats -->\n          <div class=\"row mb-4\">\n            <!-- Total Requests -->\n            <div class=\"col-xl-3 col-md-6 mb-4\">\n              <div class=\"card border-left-primary shadow h-100 py-2 stat-card\" @click=\"navigateTo('/admin/requests')\">\n                <div class=\"card-body\">\n                  <div class=\"row no-gutters align-items-center\">\n                    <div class=\"col mr-2\">\n                      <div class=\"text-xs font-weight-bold text-primary text-uppercase mb-1\">\n                        Total Requests\n                      </div>\n                      <div class=\"h5 mb-0 font-weight-bold text-gray-800\">{{ stats.totalRequests || 0 }}</div>\n                      <div class=\"text-xs text-muted mt-1\">\n                        <i class=\"fas fa-file-alt text-primary me-1\"></i>\n                        All document requests\n                      </div>\n                    </div>\n                    <div class=\"col-auto\">\n                      <div class=\"icon-circle bg-primary\">\n                        <i class=\"fas fa-file-alt text-white\"></i>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <!-- Pending Requests (High Priority) -->\n            <div class=\"col-xl-3 col-md-6 mb-4\">\n              <div class=\"card border-left-warning shadow h-100 py-2 stat-card\" @click=\"filterRequestsByStatus('pending')\">\n                <div class=\"card-body\">\n                  <div class=\"row no-gutters align-items-center\">\n                    <div class=\"col mr-2\">\n                      <div class=\"text-xs font-weight-bold text-warning text-uppercase mb-1\">\n                        Pending Requests\n                      </div>\n                      <div class=\"h5 mb-0 font-weight-bold text-gray-800\">{{ stats.pendingRequests || 0 }}</div>\n                      <div class=\"text-xs text-muted mt-1\">\n                        <i class=\"fas fa-clock text-warning me-1\"></i>\n                        <span class=\"badge badge-danger ms-1\" v-if=\"stats.urgentRequests > 0\">{{ stats.urgentRequests }} urgent</span>\n                        <span v-else>Awaiting review</span>\n                      </div>\n                    </div>\n                    <div class=\"col-auto\">\n                      <div class=\"icon-circle bg-warning\">\n                        <i class=\"fas fa-clock text-white\"></i>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <!-- Processing Requests -->\n            <div class=\"col-xl-3 col-md-6 mb-4\">\n              <div class=\"card border-left-info shadow h-100 py-2 stat-card\" @click=\"filterRequestsByStatus('processing')\">\n                <div class=\"card-body\">\n                  <div class=\"row no-gutters align-items-center\">\n                    <div class=\"col mr-2\">\n                      <div class=\"text-xs font-weight-bold text-info text-uppercase mb-1\">\n                        Processing\n                      </div>\n                      <div class=\"h5 mb-0 font-weight-bold text-gray-800\">{{ stats.processingRequests || 0 }}</div>\n                      <div class=\"text-xs text-muted mt-1\">\n                        <i class=\"fas fa-cog text-info me-1\"></i>\n                        Currently being processed\n                      </div>\n                    </div>\n                    <div class=\"col-auto\">\n                      <div class=\"icon-circle bg-info\">\n                        <i class=\"fas fa-cog text-white\"></i>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <!-- Completed Today -->\n            <div class=\"col-xl-3 col-md-6 mb-4\">\n              <div class=\"card border-left-success shadow h-100 py-2 stat-card\" @click=\"filterRequestsByDate('today')\">\n                <div class=\"card-body\">\n                  <div class=\"row no-gutters align-items-center\">\n                    <div class=\"col mr-2\">\n                      <div class=\"text-xs font-weight-bold text-success text-uppercase mb-1\">\n                        Completed Today\n                      </div>\n                      <div class=\"h5 mb-0 font-weight-bold text-gray-800\">{{ stats.todayRequests || 0 }}</div>\n                      <div class=\"text-xs text-muted mt-1\">\n                        <i class=\"fas fa-check-circle text-success me-1\"></i>\n                        ₱{{ (stats.todayRevenue || 0).toLocaleString() }} revenue\n                      </div>\n                    </div>\n                    <div class=\"col-auto\">\n                      <div class=\"icon-circle bg-success\">\n                        <i class=\"fas fa-check-circle text-white\"></i>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Request Management Quick Actions -->\n          <div class=\"row mb-4\">\n            <div class=\"col-12\">\n              <div class=\"card shadow\">\n                <div class=\"card-header py-3 d-flex justify-content-between align-items-center\">\n                  <h6 class=\"m-0 font-weight-bold text-primary\">\n                    <i class=\"fas fa-tasks me-2\"></i>\n                    Request Management Center\n                  </h6>\n                  <div class=\"d-flex gap-2\">\n                    <button class=\"btn btn-outline-primary btn-sm\" @click=\"refreshDashboard\" :disabled=\"loading\">\n                      <i class=\"fas fa-sync-alt me-1\" :class=\"{ 'fa-spin': loading }\"></i>\n                      Refresh\n                    </button>\n                    <div class=\"dropdown\">\n                      <button class=\"btn btn-primary btn-sm dropdown-toggle\" type=\"button\" data-bs-toggle=\"dropdown\">\n                        <i class=\"fas fa-bolt me-1\"></i>\n                        Quick Actions\n                      </button>\n                      <ul class=\"dropdown-menu\">\n                        <li><a class=\"dropdown-item\" href=\"#\" @click=\"navigateToRequests('pending')\">\n                          <i class=\"fas fa-clock me-2 text-warning\"></i>Review Pending Requests\n                        </a></li>\n                        <li><a class=\"dropdown-item\" href=\"#\" @click=\"navigateToRequests('urgent')\">\n                          <i class=\"fas fa-exclamation-triangle me-2 text-danger\"></i>Handle Urgent Requests\n                        </a></li>\n                        <li><a class=\"dropdown-item\" href=\"#\" @click=\"navigateToRequests('processing')\">\n                          <i class=\"fas fa-cog me-2 text-info\"></i>Monitor Processing\n                        </a></li>\n                        <li><hr class=\"dropdown-divider\"></li>\n                        <li><a class=\"dropdown-item\" href=\"#\" @click=\"navigateTo('/admin/reports')\">\n                          <i class=\"fas fa-chart-bar me-2 text-success\"></i>Generate Reports\n                        </a></li>\n                      </ul>\n                    </div>\n                  </div>\n                </div>\n                <div class=\"card-body\">\n                  <div class=\"row\">\n                    <!-- Priority Requests -->\n                    <div class=\"col-lg-4 mb-3\">\n                      <div class=\"border rounded p-3 h-100\">\n                        <h6 class=\"text-danger mb-3\">\n                          <i class=\"fas fa-exclamation-triangle me-2\"></i>\n                          Priority Requests\n                        </h6>\n                        <div v-if=\"priorityRequests.length === 0\" class=\"text-center text-muted py-3\">\n                          <i class=\"fas fa-check-circle fa-2x mb-2 text-success\"></i>\n                          <p class=\"mb-0\">No urgent requests</p>\n                        </div>\n                        <div v-else>\n                          <div v-for=\"request in priorityRequests.slice(0, 3)\" :key=\"request.id\"\n                               class=\"d-flex justify-content-between align-items-center mb-2 p-2 bg-light rounded\">\n                            <div>\n                              <small class=\"text-muted\">{{ request.request_number }}</small>\n                              <div class=\"fw-bold\">{{ request.document_type }}</div>\n                              <small class=\"text-danger\">{{ request.priority }} Priority</small>\n                            </div>\n                            <button class=\"btn btn-sm btn-outline-primary\" @click=\"viewRequestDetails(request.id)\">\n                              <i class=\"fas fa-eye\"></i>\n                            </button>\n                          </div>\n                          <div v-if=\"priorityRequests.length > 3\" class=\"text-center mt-2\">\n                            <button class=\"btn btn-sm btn-outline-danger\" @click=\"navigateToRequests('urgent')\">\n                              View {{ priorityRequests.length - 3 }} more urgent\n                            </button>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n\n                    <!-- Recent Submissions -->\n                    <div class=\"col-lg-4 mb-3\">\n                      <div class=\"border rounded p-3 h-100\">\n                        <h6 class=\"text-info mb-3\">\n                          <i class=\"fas fa-clock me-2\"></i>\n                          Recent Submissions\n                        </h6>\n                        <div v-if=\"recentRequests.length === 0\" class=\"text-center text-muted py-3\">\n                          <i class=\"fas fa-inbox fa-2x mb-2 text-gray-300\"></i>\n                          <p class=\"mb-0\">No recent submissions</p>\n                        </div>\n                        <div v-else>\n                          <div v-for=\"request in recentRequests.slice(0, 3)\" :key=\"request.id\"\n                               class=\"d-flex justify-content-between align-items-center mb-2 p-2 bg-light rounded\">\n                            <div>\n                              <small class=\"text-muted\">{{ formatTimeAgo(request.requested_at) }}</small>\n                              <div class=\"fw-bold\">{{ request.document_type }}</div>\n                              <small class=\"text-info\">{{ request.client_name }}</small>\n                            </div>\n                            <button class=\"btn btn-sm btn-outline-primary\" @click=\"viewRequestDetails(request.id)\">\n                              <i class=\"fas fa-eye\"></i>\n                            </button>\n                          </div>\n                          <div v-if=\"recentRequests.length > 3\" class=\"text-center mt-2\">\n                            <button class=\"btn btn-sm btn-outline-info\" @click=\"navigateToRequests('recent')\">\n                              View {{ recentRequests.length - 3 }} more recent\n                            </button>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n\n                    <!-- Processing Overview -->\n                    <div class=\"col-lg-4 mb-3\">\n                      <div class=\"border rounded p-3 h-100\">\n                        <h6 class=\"text-success mb-3\">\n                          <i class=\"fas fa-cogs me-2\"></i>\n                          Processing Overview\n                        </h6>\n                        <div class=\"mb-2\">\n                          <div class=\"d-flex justify-content-between\">\n                            <span class=\"small\">Pending Review</span>\n                            <span class=\"badge bg-warning\">{{ stats.pendingRequests || 0 }}</span>\n                          </div>\n                          <div class=\"progress mb-2\" style=\"height: 4px;\">\n                            <div class=\"progress-bar bg-warning\" :style=\"{ width: getProgressPercentage('pending') + '%' }\"></div>\n                          </div>\n                        </div>\n                        <div class=\"mb-2\">\n                          <div class=\"d-flex justify-content-between\">\n                            <span class=\"small\">Processing</span>\n                            <span class=\"badge bg-info\">{{ stats.processingRequests || 0 }}</span>\n                          </div>\n                          <div class=\"progress mb-2\" style=\"height: 4px;\">\n                            <div class=\"progress-bar bg-info\" :style=\"{ width: getProgressPercentage('processing') + '%' }\"></div>\n                          </div>\n                        </div>\n                        <div class=\"mb-2\">\n                          <div class=\"d-flex justify-content-between\">\n                            <span class=\"small\">Completed</span>\n                            <span class=\"badge bg-success\">{{ stats.completedRequests || 0 }}</span>\n                          </div>\n                          <div class=\"progress mb-2\" style=\"height: 4px;\">\n                            <div class=\"progress-bar bg-success\" :style=\"{ width: getProgressPercentage('completed') + '%' }\"></div>\n                          </div>\n                        </div>\n                        <div class=\"text-center mt-3\">\n                          <button class=\"btn btn-sm btn-success\" @click=\"navigateTo('/admin/requests')\">\n                            <i class=\"fas fa-list me-1\"></i>\n                            View All Requests\n                          </button>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Recent Activity -->\n          <div class=\"row\">\n            <div class=\"col-lg-8 mb-4\">\n              <div class=\"card shadow\">\n                <div class=\"card-header py-3 d-flex flex-row align-items-center justify-content-between\">\n                  <h6 class=\"m-0 font-weight-bold text-primary\">\n                    <i class=\"fas fa-history me-2\"></i>\n                    Recent Activity\n                    <span class=\"badge bg-success ms-2 pulse\" v-if=\"recentActivity.length > 0\">\n                      <i class=\"fas fa-circle\"></i>\n                      Live\n                    </span>\n                  </h6>\n                  <div class=\"dropdown no-arrow\">\n                    <button class=\"btn btn-link text-gray-400 p-0\" type=\"button\" data-bs-toggle=\"dropdown\">\n                      <i class=\"fas fa-ellipsis-v\"></i>\n                    </button>\n                    <div class=\"dropdown-menu dropdown-menu-end shadow\">\n                      <div class=\"dropdown-header\">Actions:</div>\n                      <a class=\"dropdown-item\" href=\"#\" @click=\"navigateTo('/admin/activity-logs')\">\n                        <i class=\"fas fa-list me-2\"></i>View All\n                      </a>\n                      <a class=\"dropdown-item\" href=\"#\" @click=\"exportActivity\">\n                        <i class=\"fas fa-download me-2\"></i>Export\n                      </a>\n                      <div class=\"dropdown-divider\"></div>\n                      <a class=\"dropdown-item\" href=\"#\" @click=\"refreshDashboard\">\n                        <i class=\"fas fa-sync-alt me-2\"></i>Refresh\n                      </a>\n                    </div>\n                  </div>\n                </div>\n                <div class=\"card-body\">\n                  <div v-if=\"loading\" class=\"text-center py-4\">\n                    <div class=\"spinner-border text-primary\" role=\"status\">\n                      <span class=\"visually-hidden\">Loading...</span>\n                    </div>\n                    <p class=\"text-muted mt-2\">Loading recent activity...</p>\n                  </div>\n                  <div v-else-if=\"recentActivity.length === 0\" class=\"text-center text-muted py-4\">\n                    <i class=\"fas fa-inbox fa-3x mb-3 text-gray-300\"></i>\n                    <h6 class=\"text-gray-600\">No recent activity</h6>\n                    <p class=\"small\">Activity will appear here as users interact with the system.</p>\n                  </div>\n                  <div v-else class=\"activity-list\">\n                    <div v-for=\"(activity, index) in recentActivity.slice(0, 5)\" :key=\"activity.id\"\n                         class=\"activity-item d-flex align-items-start mb-3 pb-3\"\n                         :class=\"{ 'border-bottom': index < recentActivity.slice(0, 5).length - 1 }\">\n                      <div class=\"me-3\">\n                        <div class=\"icon-circle\" :class=\"getActivityIconClass(activity.type)\">\n                          <i :class=\"getActivityIcon(activity.type)\" class=\"text-white\"></i>\n                        </div>\n                      </div>\n                      <div class=\"flex-grow-1\">\n                        <div class=\"d-flex justify-content-between align-items-start\">\n                          <div>\n                            <h6 class=\"mb-1 text-dark\">{{ activity.title || activity.description }}</h6>\n                            <p class=\"mb-1 text-muted small\">{{ activity.details || activity.description }}</p>\n                            <div class=\"small text-gray-500\">\n                              <i class=\"fas fa-clock me-1\"></i>\n                              {{ formatDate(activity.created_at) }}\n                            </div>\n                          </div>\n                          <span class=\"badge\" :class=\"getActivityBadgeClass(activity.status)\">\n                            {{ activity.status || 'Completed' }}\n                          </span>\n                        </div>\n                      </div>\n                    </div>\n                    <div v-if=\"recentActivity.length > 5\" class=\"text-center pt-2\">\n                      <button class=\"btn btn-outline-primary btn-sm\" @click=\"navigateTo('/admin/activity-logs')\">\n                        <i class=\"fas fa-plus me-1\"></i>\n                        View {{ recentActivity.length - 5 }} more activities\n                      </button>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <!-- Quick Actions -->\n            <div class=\"col-lg-4 mb-4\">\n              <div class=\"card shadow\">\n                <div class=\"card-header py-3\">\n                  <h6 class=\"m-0 font-weight-bold text-primary\">Quick Actions</h6>\n                </div>\n                <div class=\"card-body\">\n                  <div class=\"d-grid gap-2\">\n                    <button class=\"btn btn-primary btn-sm\" @click=\"navigateTo('/admin/users')\">\n                      <i class=\"fas fa-users me-2\"></i>\n                      Manage Users\n                    </button>\n                    <button class=\"btn btn-success btn-sm\" @click=\"navigateTo('/admin/requests')\">\n                      <i class=\"fas fa-file-alt me-2\"></i>\n                      View Requests\n                    </button>\n                    <button class=\"btn btn-info btn-sm\" @click=\"navigateTo('/admin/reports')\">\n                      <i class=\"fas fa-chart-bar me-2\"></i>\n                      Generate Reports\n                    </button>\n                    <button class=\"btn btn-warning btn-sm\" @click=\"navigateTo('/admin/settings')\">\n                      <i class=\"fas fa-cog me-2\"></i>\n                      System Settings\n                    </button>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </main>\n    </div>\n  </div>\n</template>\n\n<script>\nimport adminAuthService from '@/services/adminAuthService';\nimport adminDocumentService from '@/services/adminDocumentService';\nimport notificationService from '@/services/notificationService';\nimport AdminHeader from './AdminHeader.vue';\nimport AdminSidebar from './AdminSidebar.vue';\n\nexport default {\n  name: 'AdminDashboard',\n  components: {\n    AdminHeader,\n    AdminSidebar\n  },\n  data() {\n    return {\n      loading: true,\n      // UI State\n      sidebarCollapsed: false,\n      showUserDropdown: false,\n      isMobile: false,\n      adminData: null,\n      // Dashboard Data\n      stats: {\n        totalRequests: 0,\n        pendingRequests: 0,\n        approvedRequests: 0,\n        completedRequests: 0,\n        processingRequests: 0,\n        urgentRequests: 0,\n        totalRevenue: 0,\n        todayRequests: 0,\n        todayRevenue: 0,\n        totalUsers: 0,\n        activeRequests: 0,\n        completedToday: 0,\n        pendingApproval: 0\n      },\n      recentActivity: [],\n      priorityRequests: [],\n      recentRequests: [],\n      errorMessage: ''\n    };\n  },\n\n  async mounted() {\n    // Check authentication\n    if (!adminAuthService.isLoggedIn()) {\n      this.$router.push('/admin/login');\n      return;\n    }\n\n    // Initialize UI state\n    this.initializeUI();\n\n    // Load dashboard data\n    await this.loadDashboardData();\n\n    // Initialize real-time features\n    this.initializeRealTimeFeatures();\n  },\n\n  beforeUnmount() {\n    if (this.handleResize) {\n      window.removeEventListener('resize', this.handleResize);\n    }\n    if (this.refreshInterval) {\n      clearInterval(this.refreshInterval);\n    }\n\n    // Clean up real-time features\n    this.cleanupRealTimeFeatures();\n  },\n\n  computed: {\n    activeMenu() {\n      const path = this.$route.path;\n      if (path.includes('/admin/users')) return 'users';\n      if (path.includes('/admin/requests')) return 'requests';\n      if (path.includes('/admin/reports')) return 'reports';\n      if (path.includes('/admin/settings')) return 'settings';\n      if (path.includes('/admin/activity-logs')) return 'activity';\n      if (path.includes('/admin/profile')) return 'profile';\n      return 'dashboard';\n    }\n  },\n\n  methods: {\n    // Initialize UI state\n    initializeUI() {\n      this.isMobile = window.innerWidth <= 768;\n\n      // Load saved sidebar state (only on desktop)\n      if (!this.isMobile) {\n        const saved = localStorage.getItem('adminSidebarCollapsed');\n        this.sidebarCollapsed = saved ? JSON.parse(saved) : false;\n      } else {\n        this.sidebarCollapsed = true; // Always collapsed on mobile\n      }\n\n      // Setup resize listener\n      this.handleResize = () => {\n        const wasMobile = this.isMobile;\n        this.isMobile = window.innerWidth <= 768;\n\n        if (this.isMobile && !wasMobile) {\n          this.sidebarCollapsed = true; // Collapse when switching to mobile\n        } else if (!this.isMobile && wasMobile) {\n          // Restore saved state when switching to desktop\n          const saved = localStorage.getItem('adminSidebarCollapsed');\n          this.sidebarCollapsed = saved ? JSON.parse(saved) : false;\n        }\n      };\n      window.addEventListener('resize', this.handleResize);\n    },\n\n    // Sidebar toggle\n    handleSidebarToggle() {\n      this.sidebarCollapsed = !this.sidebarCollapsed;\n      localStorage.setItem('adminSidebarCollapsed', JSON.stringify(this.sidebarCollapsed));\n    },\n\n    // Menu navigation\n    handleMenuChange(menu) {\n      const routes = {\n        'dashboard': '/admin/dashboard',\n        'users': '/admin/users',\n        'requests': '/admin/requests',\n        'reports': '/admin/reports',\n        'settings': '/admin/settings',\n        'activity': '/admin/activity-logs',\n        'profile': '/admin/profile'\n      };\n\n      // Close sidebar on mobile after navigation\n      if (this.isMobile) {\n        this.sidebarCollapsed = true;\n      }\n\n      if (routes[menu]) {\n        this.$router.push(routes[menu]);\n      }\n    },\n\n    // User dropdown toggle\n    handleUserDropdownToggle() {\n      this.showUserDropdown = !this.showUserDropdown;\n    },\n\n    // Menu actions\n    handleMenuAction(action) {\n      if (action === 'profile') {\n        this.$router.push('/admin/profile');\n      } else if (action === 'settings') {\n        this.$router.push('/admin/settings');\n      }\n      this.showUserDropdown = false;\n    },\n\n    // Close mobile sidebar\n    closeMobileSidebar() {\n      if (this.isMobile) {\n        this.sidebarCollapsed = true;\n      }\n    },\n\n    // Logout\n    handleLogout() {\n      adminAuthService.logout();\n      this.$router.push('/admin/login');\n    },\n\n    // Load dashboard data\n    async loadDashboardData() {\n      this.loading = true;\n\n      try {\n        // Load admin profile\n        await this.loadAdminProfile();\n\n        // Load dashboard statistics\n        await this.loadDashboardStats();\n\n        // Load recent activity\n        await this.loadRecentActivity();\n\n      } catch (error) {\n        console.error('Failed to load dashboard data:', error);\n        const errorData = adminAuthService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to load dashboard data';\n\n        // If unauthorized, redirect to login\n        if (errorData.status === 401) {\n          adminAuthService.logout();\n          this.$router.push('/admin/login');\n        }\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    // Load admin profile\n    async loadAdminProfile() {\n      try {\n        const response = await adminAuthService.getProfile();\n        if (response.success) {\n          this.adminData = response.data;\n        }\n      } catch (error) {\n        console.error('Failed to load admin profile:', error);\n        this.adminData = adminAuthService.getAdminData();\n      }\n    },\n\n\n\n    // Load dashboard statistics\n    async loadDashboardStats() {\n      try {\n        const response = await adminDocumentService.getDashboardStats();\n        if (response.success) {\n          // Update stats with enhanced request management data\n          this.stats = {\n            totalRequests: response.data.totalRequests || 0,\n            pendingRequests: response.data.pendingRequests || 0,\n            approvedRequests: response.data.approvedRequests || 0,\n            completedRequests: response.data.completedRequests || 0,\n            processingRequests: response.data.processingRequests || 0,\n            urgentRequests: response.data.urgentRequests || 0,\n            totalRevenue: response.data.totalRevenue || 0,\n            todayRequests: response.data.todayRequests || 0,\n            todayRevenue: response.data.todayRevenue || 0,\n            // Keep legacy fields for backward compatibility\n            totalUsers: response.data.totalUsers || 0,\n            activeRequests: response.data.pendingRequests || 0,\n            completedToday: response.data.todayRequests || 0,\n            pendingApproval: response.data.pendingRequests || 0\n          };\n        }\n\n        // Load priority and recent requests\n        await this.loadPriorityRequests();\n        await this.loadRecentRequests();\n      } catch (error) {\n        console.error('Failed to load dashboard stats:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to load dashboard statistics';\n      }\n    },\n\n    // Load priority requests\n    async loadPriorityRequests() {\n      try {\n        const response = await adminDocumentService.getAllRequests({\n          priority: 'high',\n          limit: 5,\n          status: 'pending'\n        });\n        if (response.success) {\n          this.priorityRequests = response.data.requests || [];\n        }\n      } catch (error) {\n        console.error('Failed to load priority requests:', error);\n        this.priorityRequests = [];\n      }\n    },\n\n    // Load recent requests\n    async loadRecentRequests() {\n      try {\n        const response = await adminDocumentService.getAllRequests({\n          limit: 5,\n          sort: 'requested_at',\n          order: 'desc'\n        });\n        if (response.success) {\n          this.recentRequests = response.data.requests || [];\n        }\n      } catch (error) {\n        console.error('Failed to load recent requests:', error);\n        this.recentRequests = [];\n      }\n    },\n\n    // Load recent activity\n    async loadRecentActivity() {\n      try {\n        const response = await adminDocumentService.getRecentActivity(10);\n        if (response.success) {\n          this.recentActivity = response.data || [];\n        }\n      } catch (error) {\n        console.error('Failed to load recent activity:', error);\n        const errorData = adminDocumentService.parseError(error);\n        console.error('Recent activity error details:', errorData);\n        this.recentActivity = [];\n      }\n    },\n\n    // Navigate to specific route\n    navigateTo(route) {\n      try {\n        console.log('Navigating to:', route);\n        this.$router.push(route);\n      } catch (error) {\n        console.error('Navigation error:', error);\n        this.errorMessage = 'Navigation failed. Please try again.';\n      }\n    },\n\n    // Format date for display\n    formatDate(dateString) {\n      if (!dateString) return '';\n\n      const date = new Date(dateString);\n      const now = new Date();\n      const diffInMinutes = Math.floor((now - date) / (1000 * 60));\n\n      if (diffInMinutes < 1) {\n        return 'Just now';\n      } else if (diffInMinutes < 60) {\n        return `${diffInMinutes} minute${diffInMinutes > 1 ? 's' : ''} ago`;\n      } else if (diffInMinutes < 1440) {\n        const hours = Math.floor(diffInMinutes / 60);\n        return `${hours} hour${hours > 1 ? 's' : ''} ago`;\n      } else {\n        const days = Math.floor(diffInMinutes / 1440);\n        return `${days} day${days > 1 ? 's' : ''} ago`;\n      }\n    },\n\n    // Format time for display\n    formatTime(dateString) {\n      if (!dateString) return '';\n\n      const date = new Date(dateString);\n      return date.toLocaleTimeString('en-US', {\n        hour: '2-digit',\n        minute: '2-digit',\n        hour12: true\n      });\n    },\n\n    // Refresh dashboard data\n    async refreshDashboard() {\n      await this.loadDashboardData();\n    },\n\n    // Export activity logs\n    exportActivity() {\n      // Implement export functionality\n      console.log('Exporting activity logs...');\n      // This would typically generate and download a CSV/Excel file\n    },\n\n    // Get activity icon based on type\n    getActivityIcon(type) {\n      const icons = {\n        'user_registration': 'fas fa-user-plus',\n        'document_request': 'fas fa-file-alt',\n        'document_approved': 'fas fa-check-circle',\n        'document_rejected': 'fas fa-times-circle',\n        'system_update': 'fas fa-cog',\n        'login': 'fas fa-sign-in-alt',\n        'logout': 'fas fa-sign-out-alt',\n        'default': 'fas fa-info-circle'\n      };\n      return icons[type] || icons.default;\n    },\n\n    // Get activity icon circle class based on type\n    getActivityIconClass(type) {\n      const classes = {\n        'user_registration': 'bg-success',\n        'document_request': 'bg-primary',\n        'document_approved': 'bg-success',\n        'document_rejected': 'bg-danger',\n        'system_update': 'bg-warning',\n        'login': 'bg-info',\n        'logout': 'bg-secondary',\n        'default': 'bg-primary'\n      };\n      return classes[type] || classes.default;\n    },\n\n    // Get activity badge class based on status\n    getActivityBadgeClass(status) {\n      const classes = {\n        'completed': 'badge-success',\n        'pending': 'badge-warning',\n        'failed': 'badge-danger',\n        'in_progress': 'badge-info',\n        'default': 'badge-secondary'\n      };\n      return classes[status?.toLowerCase()] || classes.default;\n    },\n\n    // Enhanced Request Management Methods\n\n    // Format time ago for display\n    formatTimeAgo(dateString) {\n      if (!dateString) return '';\n\n      const date = new Date(dateString);\n      const now = new Date();\n      const diffInMinutes = Math.floor((now - date) / (1000 * 60));\n\n      if (diffInMinutes < 1) {\n        return 'Just now';\n      } else if (diffInMinutes < 60) {\n        return `${diffInMinutes}m ago`;\n      } else if (diffInMinutes < 1440) {\n        const hours = Math.floor(diffInMinutes / 60);\n        return `${hours}h ago`;\n      } else {\n        const days = Math.floor(diffInMinutes / 1440);\n        return `${days}d ago`;\n      }\n    },\n\n    // Filter requests by status and navigate\n    filterRequestsByStatus(status) {\n      this.$router.push({\n        path: '/admin/requests',\n        query: { status: status }\n      });\n    },\n\n    // Filter requests by date and navigate\n    filterRequestsByDate(period) {\n      const query = {};\n      const today = new Date();\n\n      if (period === 'today') {\n        query.date_from = today.toISOString().split('T')[0];\n        query.date_to = today.toISOString().split('T')[0];\n      }\n\n      this.$router.push({\n        path: '/admin/requests',\n        query: query\n      });\n    },\n\n    // Navigate to requests with specific filters\n    navigateToRequests(filter) {\n      const query = {};\n\n      switch (filter) {\n        case 'pending':\n          query.status = 'pending';\n          break;\n        case 'urgent':\n          query.priority = 'high';\n          query.status = 'pending';\n          break;\n        case 'processing':\n          query.status = 'processing';\n          break;\n        case 'recent':\n          query.sort = 'requested_at';\n          query.order = 'desc';\n          break;\n      }\n\n      this.$router.push({\n        path: '/admin/requests',\n        query: query\n      });\n    },\n\n    // View request details\n    viewRequestDetails(requestId) {\n      this.$router.push({\n        path: '/admin/requests',\n        query: { view: requestId }\n      });\n    },\n\n    // Get progress percentage for processing overview\n    getProgressPercentage(type) {\n      const total = this.stats.totalRequests || 1; // Avoid division by zero\n\n      switch (type) {\n        case 'pending':\n          return Math.round((this.stats.pendingRequests / total) * 100);\n        case 'processing':\n          return Math.round((this.stats.processingRequests / total) * 100);\n        case 'completed':\n          return Math.round((this.stats.completedRequests / total) * 100);\n        default:\n          return 0;\n      }\n    },\n\n    // Format currency for display\n    formatCurrency(amount) {\n      if (!amount) return '0.00';\n      return parseFloat(amount).toLocaleString('en-US', {\n        minimumFractionDigits: 2,\n        maximumFractionDigits: 2\n      });\n    },\n\n    // Real-time features\n    async initializeRealTimeFeatures() {\n      console.log('Initializing real-time features for AdminDashboard');\n\n      try {\n        // Initialize notification service\n        await notificationService.init('admin');\n\n        // Listen for dashboard-relevant notifications\n        notificationService.on('notification', this.handleRealTimeNotification);\n        notificationService.on('new_request', this.handleNewRequest);\n        notificationService.on('request_status_changed', this.handleStatusChange);\n        notificationService.on('dashboard_update', this.handleDashboardUpdate);\n      } catch (error) {\n        console.error('Failed to initialize real-time features:', error);\n      }\n    },\n\n    cleanupRealTimeFeatures() {\n      console.log('Cleaning up real-time features for AdminDashboard');\n\n      // Remove notification listeners\n      notificationService.off('notification', this.handleRealTimeNotification);\n      notificationService.off('new_request', this.handleNewRequest);\n      notificationService.off('request_status_changed', this.handleStatusChange);\n      notificationService.off('dashboard_update', this.handleDashboardUpdate);\n\n      // Cleanup (simplified)\n      notificationService.cleanup();\n    },\n\n    handleRealTimeNotification(notification) {\n      console.log('Dashboard received real-time notification:', notification);\n\n      // Handle different notification types\n      switch (notification.type) {\n        case 'new_request':\n          this.handleNewRequest(notification.data);\n          break;\n        case 'request_status_changed':\n          this.handleStatusChange(notification.data);\n          break;\n        case 'dashboard_update':\n          this.handleDashboardUpdate(notification.data);\n          break;\n        default:\n          console.log('Unhandled notification type:', notification.type);\n      }\n    },\n\n    handleNewRequest(data) {\n      console.log('New request received on dashboard:', data);\n\n      // Update statistics\n      this.stats.totalRequests++;\n      this.stats.pendingRequests++;\n      this.stats.todayRequests++;\n\n      // Refresh dashboard data to get accurate counts\n      this.loadDashboardStats();\n      this.loadRecentActivity();\n      this.loadRecentRequests();\n    },\n\n    handleStatusChange(data) {\n      console.log('Request status changed on dashboard:', data);\n\n      // Refresh dashboard statistics and recent activity\n      this.loadDashboardStats();\n      this.loadRecentActivity();\n      this.loadPriorityRequests();\n      this.loadRecentRequests();\n    },\n\n    handleDashboardUpdate(data) {\n      console.log('Dashboard update received:', data);\n\n      // Refresh all dashboard data\n      this.loadDashboardData();\n    }\n\n  },\n\n  // Auto-refresh dashboard data every 5 minutes\n  created() {\n    this.refreshInterval = setInterval(() => {\n      this.loadDashboardStats();\n      this.loadRecentActivity();\n      this.loadPriorityRequests();\n      this.loadRecentRequests();\n    }, 5 * 60 * 1000); // 5 minutes\n  }\n};\n</script>\n\n<style scoped>\n@import './css/adminDashboard.css';\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAiB;;EAmBrBA,KAAK,EAAC;AAAqB;;EAYvBA,KAAK,EAAC;AAAqB;;EACzBA,KAAK,EAAC;AAAU;;EACdA,KAAK,EAAC;AAAQ;;EACZA,KAAK,EAAC;AAA6D;;EAEjEA,KAAK,EAAC;AAAc;;;EAKlBA,KAAK,EAAC;AAAU;;EAKfA,KAAK,EAAC;AAAe;;EAmB9BA,KAAK,EAAC;AAAU;;EAEdA,KAAK,EAAC;AAAwB;;EAE1BA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAmC;;EACvCA,KAAK,EAAC;AAAU;;EAIdA,KAAK,EAAC;AAAwC;;EAiBxDA,KAAK,EAAC;AAAwB;;EAE1BA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAmC;;EACvCA,KAAK,EAAC;AAAU;;EAIdA,KAAK,EAAC;AAAwC;;EAC9CA,KAAK,EAAC;AAAyB;;;EAE5BA,KAAK,EAAC;;;;;;EAenBA,KAAK,EAAC;AAAwB;;EAE1BA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAmC;;EACvCA,KAAK,EAAC;AAAU;;EAIdA,KAAK,EAAC;AAAwC;;EAiBxDA,KAAK,EAAC;AAAwB;;EAE1BA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAmC;;EACvCA,KAAK,EAAC;AAAU;;EAIdA,KAAK,EAAC;AAAwC;;EAC9CA,KAAK,EAAC;AAAyB;;EAiB3CA,KAAK,EAAC;AAAU;;EACdA,KAAK,EAAC;AAAQ;;EACZA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAAoE;;EAKxEA,KAAK,EAAC;AAAc;;;EAKlBA,KAAK,EAAC;AAAU;;EAKfA,KAAK,EAAC;AAAe;;EAkB1BA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAK;;EAETA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAA0B;;;EAKOA,KAAK,EAAC;;;;;;EAQnCA,KAAK,EAAC;AAAY;;EACpBA,KAAK,EAAC;AAAS;;EACbA,KAAK,EAAC;AAAa;;;;EAMUA,KAAK,EAAC;;;EAU/CA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAA0B;;;EAKKA,KAAK,EAAC;;;;;;EAQjCA,KAAK,EAAC;AAAY;;EACpBA,KAAK,EAAC;AAAS;;EACbA,KAAK,EAAC;AAAW;;;;EAMUA,KAAK,EAAC;;;EAU7CA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAA0B;;EAK9BA,KAAK,EAAC;AAAM;;EACVA,KAAK,EAAC;AAAgC;;EAEnCA,KAAK,EAAC;AAAkB;;EAE3BA,KAAK,EAAC,eAAe;EAACC,KAAoB,EAApB;IAAA;EAAA;;;EAIxBD,KAAK,EAAC;AAAM;;EACVA,KAAK,EAAC;AAAgC;;EAEnCA,KAAK,EAAC;AAAe;;EAExBA,KAAK,EAAC,eAAe;EAACC,KAAoB,EAApB;IAAA;EAAA;;;EAIxBD,KAAK,EAAC;AAAM;;EACVA,KAAK,EAAC;AAAgC;;EAEnCA,KAAK,EAAC;AAAkB;;EAE3BA,KAAK,EAAC,eAAe;EAACC,KAAoB,EAApB;IAAA;EAAA;;;EAIxBD,KAAK,EAAC;AAAkB;;EAetCA,KAAK,EAAC;AAAK;;EACTA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAA6E;;EAClFA,KAAK,EAAC;AAAmC;;;EAGrCA,KAAK,EAAC;;;EAKTA,KAAK,EAAC;AAAmB;;EAIvBA,KAAK,EAAC;AAAwC;;EAelDA,KAAK,EAAC;AAAW;;;EACAA,KAAK,EAAC;;;;EAMmBA,KAAK,EAAC;;;;EAKvCA,KAAK,EAAC;;;EAITA,KAAK,EAAC;AAAM;;EAKZA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAAkD;;EAErDA,KAAK,EAAC;AAAgB;;EACvBA,KAAK,EAAC;AAAuB;;EAC3BA,KAAK,EAAC;AAAqB;;;EAWFA,KAAK,EAAC;;;EAY/CA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAAa;;EAIjBA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAc;;;;uBAtZzCE,mBAAA,CA+aM,OA/aNC,UA+aM,GA9aJC,YAAA,CASEC,sBAAA;IARCC,QAAQ,EAAEC,KAAA,CAAAC,SAAS,EAAEC,UAAU;IAC/BC,gBAAgB,EAAEH,KAAA,CAAAG,gBAAgB;IAClCC,gBAAgB,EAAEJ,KAAA,CAAAI,gBAAgB;IAClCC,UAAU,EAAEC,QAAA,CAAAD,UAAU;IACtBE,eAAc,EAAED,QAAA,CAAAE,mBAAmB;IACnCC,oBAAoB,EAAEH,QAAA,CAAAI,wBAAwB;IAC9CC,YAAW,EAAEL,QAAA,CAAAM,gBAAgB;IAC7BC,QAAM,EAAEP,QAAA,CAAAQ;sKAGXC,mBAAA,oBAAuB,EACvBC,mBAAA,CAIO;IAHLvB,KAAK,EAAAwB,eAAA,EAAC,gBAAgB;MAAAC,MAAA,GACHlB,KAAA,CAAAI,gBAAgB,IAAIJ,KAAA,CAAAmB;IAAQ;IAC9CC,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEhB,QAAA,CAAAiB,kBAAA,IAAAjB,QAAA,CAAAiB,kBAAA,IAAAD,IAAA,CAAkB;2BAG5BN,mBAAA,CA2ZM,OA3ZNQ,UA2ZM,GA1ZJ3B,YAAA,CASE4B,uBAAA;IARCC,SAAS,EAAE1B,KAAA,CAAAI,gBAAgB;IAC3BC,UAAU,EAAEC,QAAA,CAAAD,UAAU;IACtBsB,UAAU,EAAE3B,KAAA,CAAA4B,KAAK,CAACD,UAAU;IAC5BE,eAAe,EAAE7B,KAAA,CAAA4B,KAAK,CAACE,cAAc;IACrCC,YAAY,EAAE/B,KAAA,CAAA4B,KAAK,CAACI,cAAc;IAClCC,YAAW,EAAE3B,QAAA,CAAA4B,gBAAgB;IAC7BrB,QAAM,EAAEP,QAAA,CAAAQ,YAAY;IACpBqB,eAAc,EAAE7B,QAAA,CAAAE;wJAEnBQ,mBAAA,CA+YO;IA/YDvB,KAAK,EAAAwB,eAAA,EAAC,cAAc;MAAA,qBAAgCjB,KAAA,CAAAI;IAAgB;MACxEY,mBAAA,CA6YM,OA7YNoB,UA6YM,GA5YJpB,mBAAA,CA8BM,OA9BNqB,UA8BM,GA7BJrB,mBAAA,CA4BM,OA5BNsB,UA4BM,GA3BJtB,mBAAA,CA0BM,OA1BNuB,UA0BM,GAxBJvB,mBAAA,CAuBM,OAvBNwB,UAuBM,GAtBJxB,mBAAA,CAGS;IAHDvB,KAAK,EAAC,gCAAgC;IAAE2B,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEhB,QAAA,CAAAmC,gBAAA,IAAAnC,QAAA,CAAAmC,gBAAA,IAAAnB,IAAA,CAAgB;IAAGoB,QAAQ,EAAE1C,KAAA,CAAA2C;MAClF3B,mBAAA,CAAoE;IAAjEvB,KAAK,EAAAwB,eAAA,EAAC,sBAAsB;MAAA,WAAsBjB,KAAA,CAAA2C;IAAO;wEAAQ,WAEtE,G,8BACA3B,mBAAA,CAiBM,OAjBN4B,UAiBM,G,4BAhBJ5B,mBAAA,CAGS;IAHDvB,KAAK,EAAC,wCAAwC;IAACoD,IAAI,EAAC,QAAQ;IAAC,gBAAc,EAAC;MAClF7B,mBAAA,CAAgC;IAA7BvB,KAAK,EAAC;EAAkB,I,iBAAK,iBAElC,E,sBACAuB,mBAAA,CAWK,MAXL8B,WAWK,GAVH9B,mBAAA,CAES,aAFLA,mBAAA,CAEA;IAFGvB,KAAK,EAAC,eAAe;IAACsD,IAAI,EAAC,GAAG;IAAE3B,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAA2B,MAAA,IAAE1C,QAAA,CAAA2C,UAAU;kCACtDjC,mBAAA,CAAqC;IAAlCvB,KAAK,EAAC;EAAuB,4B,iBAAK,WACvC,E,MACAuB,mBAAA,CAES,aAFLA,mBAAA,CAEA;IAFGvB,KAAK,EAAC,eAAe;IAACsD,IAAI,EAAC,GAAG;IAAE3B,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAA2B,MAAA,IAAE1C,QAAA,CAAA2C,UAAU;kCACtDjC,mBAAA,CAAoC;IAAjCvB,KAAK,EAAC;EAAsB,4B,iBAAK,cACtC,E,kCACAuB,mBAAA,CAAsC,aAAlCA,mBAAA,CAA6B;IAAzBvB,KAAK,EAAC;EAAkB,G,sBAChCuB,mBAAA,CAES,aAFLA,mBAAA,CAEA;IAFGvB,KAAK,EAAC,eAAe;IAACsD,IAAI,EAAC,GAAG;IAAE3B,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAA2B,MAAA,IAAE1C,QAAA,CAAA2C,UAAU;kCACtDjC,mBAAA,CAAqC;IAAlCvB,KAAK,EAAC;EAAuB,4B,iBAAK,kBACvC,E,kBAQZsB,mBAAA,iDAAoD,EACpDC,mBAAA,CAqGM,OArGNkC,WAqGM,GApGJnC,mBAAA,oBAAuB,EACvBC,mBAAA,CAsBM,OAtBNmC,WAsBM,GArBJnC,mBAAA,CAoBM;IApBDvB,KAAK,EAAC,sDAAsD;IAAE2B,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAA2B,MAAA,IAAE1C,QAAA,CAAA2C,UAAU;MAClFjC,mBAAA,CAkBM,OAlBNoC,WAkBM,GAjBJpC,mBAAA,CAgBM,OAhBNqC,WAgBM,GAfJrC,mBAAA,CASM,OATNsC,WASM,G,4BARJtC,mBAAA,CAEM;IAFDvB,KAAK,EAAC;EAA2D,GAAC,kBAEvE,sBACAuB,mBAAA,CAAwF,OAAxFuC,WAAwF,EAAAC,gBAAA,CAAjCxD,KAAA,CAAA4B,KAAK,CAAC6B,aAAa,uB,4BAC1EzC,mBAAA,CAGM;IAHDvB,KAAK,EAAC;EAAyB,IAClCuB,mBAAA,CAAiD;IAA9CvB,KAAK,EAAC;EAAmC,I,iBAAK,yBAEnD,E,oDAEFuB,mBAAA,CAIM;IAJDvB,KAAK,EAAC;EAAU,IACnBuB,mBAAA,CAEM;IAFDvB,KAAK,EAAC;EAAwB,IACjCuB,mBAAA,CAA0C;IAAvCvB,KAAK,EAAC;EAA4B,G,gCAQjDsB,mBAAA,sCAAyC,EACzCC,mBAAA,CAuBM,OAvBN0C,WAuBM,GAtBJ1C,mBAAA,CAqBM;IArBDvB,KAAK,EAAC,sDAAsD;IAAE2B,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAA2B,MAAA,IAAE1C,QAAA,CAAAqD,sBAAsB;MAC9F3C,mBAAA,CAmBM,OAnBN4C,WAmBM,GAlBJ5C,mBAAA,CAiBM,OAjBN6C,WAiBM,GAhBJ7C,mBAAA,CAUM,OAVN8C,WAUM,G,4BATJ9C,mBAAA,CAEM;IAFDvB,KAAK,EAAC;EAA2D,GAAC,oBAEvE,sBACAuB,mBAAA,CAA0F,OAA1F+C,WAA0F,EAAAP,gBAAA,CAAnCxD,KAAA,CAAA4B,KAAK,CAACC,eAAe,uBAC5Eb,mBAAA,CAIM,OAJNgD,WAIM,G,4BAHJhD,mBAAA,CAA8C;IAA3CvB,KAAK,EAAC;EAAgC,6BACGO,KAAA,CAAA4B,KAAK,CAACqC,cAAc,Q,cAAhEtE,mBAAA,CAA8G,QAA9GuE,WAA8G,EAAAV,gBAAA,CAArCxD,KAAA,CAAA4B,KAAK,CAACqC,cAAc,IAAG,SAAO,oB,cACvGtE,mBAAA,CAAmC,QAAAwE,WAAA,EAAtB,iBAAe,G,iCAGhCnD,mBAAA,CAIM;IAJDvB,KAAK,EAAC;EAAU,IACnBuB,mBAAA,CAEM;IAFDvB,KAAK,EAAC;EAAwB,IACjCuB,mBAAA,CAAuC;IAApCvB,KAAK,EAAC;EAAyB,G,gCAQ9CsB,mBAAA,yBAA4B,EAC5BC,mBAAA,CAsBM,OAtBNoD,WAsBM,GArBJpD,mBAAA,CAoBM;IApBDvB,KAAK,EAAC,mDAAmD;IAAE2B,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAA2B,MAAA,IAAE1C,QAAA,CAAAqD,sBAAsB;MAC3F3C,mBAAA,CAkBM,OAlBNqD,WAkBM,GAjBJrD,mBAAA,CAgBM,OAhBNsD,WAgBM,GAfJtD,mBAAA,CASM,OATNuD,WASM,G,4BARJvD,mBAAA,CAEM;IAFDvB,KAAK,EAAC;EAAwD,GAAC,cAEpE,sBACAuB,mBAAA,CAA6F,OAA7FwD,WAA6F,EAAAhB,gBAAA,CAAtCxD,KAAA,CAAA4B,KAAK,CAAC6C,kBAAkB,uB,4BAC/EzD,mBAAA,CAGM;IAHDvB,KAAK,EAAC;EAAyB,IAClCuB,mBAAA,CAAyC;IAAtCvB,KAAK,EAAC;EAA2B,I,iBAAK,6BAE3C,E,oDAEFuB,mBAAA,CAIM;IAJDvB,KAAK,EAAC;EAAU,IACnBuB,mBAAA,CAEM;IAFDvB,KAAK,EAAC;EAAqB,IAC9BuB,mBAAA,CAAqC;IAAlCvB,KAAK,EAAC;EAAuB,G,gCAQ5CsB,mBAAA,qBAAwB,EACxBC,mBAAA,CAsBM,OAtBN0D,WAsBM,GArBJ1D,mBAAA,CAoBM;IApBDvB,KAAK,EAAC,sDAAsD;IAAE2B,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAA2B,MAAA,IAAE1C,QAAA,CAAAqE,oBAAoB;MAC5F3D,mBAAA,CAkBM,OAlBN4D,WAkBM,GAjBJ5D,mBAAA,CAgBM,OAhBN6D,WAgBM,GAfJ7D,mBAAA,CASM,OATN8D,WASM,G,4BARJ9D,mBAAA,CAEM;IAFDvB,KAAK,EAAC;EAA2D,GAAC,mBAEvE,sBACAuB,mBAAA,CAAwF,OAAxF+D,WAAwF,EAAAvB,gBAAA,CAAjCxD,KAAA,CAAA4B,KAAK,CAACoD,aAAa,uBAC1EhE,mBAAA,CAGM,OAHNiE,WAGM,G,4BAFJjE,mBAAA,CAAqD;IAAlDvB,KAAK,EAAC;EAAuC,6B,iBAAK,IACpD,GAAA+D,gBAAA,EAAIxD,KAAA,CAAA4B,KAAK,CAACsD,YAAY,OAAOC,cAAc,MAAK,WACnD,gB,iCAEFnE,mBAAA,CAIM;IAJDvB,KAAK,EAAC;EAAU,IACnBuB,mBAAA,CAEM;IAFDvB,KAAK,EAAC;EAAwB,IACjCuB,mBAAA,CAA8C;IAA3CvB,KAAK,EAAC;EAAgC,G,kCASvDsB,mBAAA,sCAAyC,EACzCC,mBAAA,CAoJM,OApJNoE,WAoJM,GAnJJpE,mBAAA,CAkJM,OAlJNqE,WAkJM,GAjJJrE,mBAAA,CAgJM,OAhJNsE,WAgJM,GA/IJtE,mBAAA,CAgCM,OAhCNuE,WAgCM,G,4BA/BJvE,mBAAA,CAGK;IAHDvB,KAAK,EAAC;EAAmC,IAC3CuB,mBAAA,CAAiC;IAA9BvB,KAAK,EAAC;EAAmB,I,iBAAK,6BAEnC,E,sBACAuB,mBAAA,CA0BM,OA1BNwE,WA0BM,GAzBJxE,mBAAA,CAGS;IAHDvB,KAAK,EAAC,gCAAgC;IAAE2B,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEhB,QAAA,CAAAmC,gBAAA,IAAAnC,QAAA,CAAAmC,gBAAA,IAAAnB,IAAA,CAAgB;IAAGoB,QAAQ,EAAE1C,KAAA,CAAA2C;MAClF3B,mBAAA,CAAoE;IAAjEvB,KAAK,EAAAwB,eAAA,EAAC,sBAAsB;MAAA,WAAsBjB,KAAA,CAAA2C;IAAO;wEAAQ,WAEtE,G,+BACA3B,mBAAA,CAoBM,OApBNyE,WAoBM,G,4BAnBJzE,mBAAA,CAGS;IAHDvB,KAAK,EAAC,wCAAwC;IAACoD,IAAI,EAAC,QAAQ;IAAC,gBAAc,EAAC;MAClF7B,mBAAA,CAAgC;IAA7BvB,KAAK,EAAC;EAAkB,I,iBAAK,iBAElC,E,sBACAuB,mBAAA,CAcK,MAdL0E,WAcK,GAbH1E,mBAAA,CAES,aAFLA,mBAAA,CAEA;IAFGvB,KAAK,EAAC,eAAe;IAACsD,IAAI,EAAC,GAAG;IAAE3B,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAA2B,MAAA,IAAE1C,QAAA,CAAAqF,kBAAkB;kCAC9D3E,mBAAA,CAA8C;IAA3CvB,KAAK,EAAC;EAAgC,4B,iBAAK,0BAChD,E,MACAuB,mBAAA,CAES,aAFLA,mBAAA,CAEA;IAFGvB,KAAK,EAAC,eAAe;IAACsD,IAAI,EAAC,GAAG;IAAE3B,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAA2B,MAAA,IAAE1C,QAAA,CAAAqF,kBAAkB;kCAC9D3E,mBAAA,CAA4D;IAAzDvB,KAAK,EAAC;EAA8C,4B,iBAAK,yBAC9D,E,MACAuB,mBAAA,CAES,aAFLA,mBAAA,CAEA;IAFGvB,KAAK,EAAC,eAAe;IAACsD,IAAI,EAAC,GAAG;IAAE3B,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAA2B,MAAA,IAAE1C,QAAA,CAAAqF,kBAAkB;kCAC9D3E,mBAAA,CAAyC;IAAtCvB,KAAK,EAAC;EAA2B,4B,iBAAK,qBAC3C,E,kCACAuB,mBAAA,CAAsC,aAAlCA,mBAAA,CAA6B;IAAzBvB,KAAK,EAAC;EAAkB,G,sBAChCuB,mBAAA,CAES,aAFLA,mBAAA,CAEA;IAFGvB,KAAK,EAAC,eAAe;IAACsD,IAAI,EAAC,GAAG;IAAE3B,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAA2B,MAAA,IAAE1C,QAAA,CAAA2C,UAAU;kCACtDjC,mBAAA,CAAkD;IAA/CvB,KAAK,EAAC;EAAoC,4B,iBAAK,mBACpD,E,cAKRuB,mBAAA,CA6GM,OA7GN4E,WA6GM,GA5GJ5E,mBAAA,CA2GM,OA3GN6E,WA2GM,GA1GJ9E,mBAAA,uBAA0B,EAC1BC,mBAAA,CA6BM,OA7BN8E,WA6BM,GA5BJ9E,mBAAA,CA2BM,OA3BN+E,WA2BM,G,4BA1BJ/E,mBAAA,CAGK;IAHDvB,KAAK,EAAC;EAAkB,IAC1BuB,mBAAA,CAAgD;IAA7CvB,KAAK,EAAC;EAAkC,I,iBAAK,qBAElD,E,sBACWO,KAAA,CAAAgG,gBAAgB,CAACC,MAAM,U,cAAlCtG,mBAAA,CAGM,OAHNuG,WAGM,EAAA7E,MAAA,SAAAA,MAAA,QAFJL,mBAAA,CAA2D;IAAxDvB,KAAK,EAAC;EAA6C,4BACtDuB,mBAAA,CAAsC;IAAnCvB,KAAK,EAAC;EAAM,GAAC,oBAAkB,oB,qBAEpCE,mBAAA,CAiBM,OAAAwG,WAAA,I,kBAhBJxG,mBAAA,CAUMyG,SAAA,QAAAC,WAAA,CAViBrG,KAAA,CAAAgG,gBAAgB,CAACM,KAAK,QAAjCC,OAAO;yBAAnB5G,mBAAA,CAUM;MAVgD6G,GAAG,EAAED,OAAO,CAACE,EAAE;MAChEhH,KAAK,EAAC;QACTuB,mBAAA,CAIM,cAHJA,mBAAA,CAA8D,SAA9D0F,WAA8D,EAAAlD,gBAAA,CAAjC+C,OAAO,CAACI,cAAc,kBACnD3F,mBAAA,CAAsD,OAAtD4F,WAAsD,EAAApD,gBAAA,CAA9B+C,OAAO,CAACM,aAAa,kBAC7C7F,mBAAA,CAAkE,SAAlE8F,WAAkE,EAAAtD,gBAAA,CAApC+C,OAAO,CAACQ,QAAQ,IAAG,WAAS,gB,GAE5D/F,mBAAA,CAES;MAFDvB,KAAK,EAAC,gCAAgC;MAAE2B,OAAK,EAAA4B,MAAA,IAAE1C,QAAA,CAAA0G,kBAAkB,CAACT,OAAO,CAACE,EAAE;yCAClFzF,mBAAA,CAA0B;MAAvBvB,KAAK,EAAC;IAAY,2B;kCAGdO,KAAA,CAAAgG,gBAAgB,CAACC,MAAM,Q,cAAlCtG,mBAAA,CAIM,OAJNsH,WAIM,GAHJjG,mBAAA,CAES;IAFDvB,KAAK,EAAC,+BAA+B;IAAE2B,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAA2B,MAAA,IAAE1C,QAAA,CAAAqF,kBAAkB;KAAY,QAC7E,GAAAnC,gBAAA,CAAGxD,KAAA,CAAAgG,gBAAgB,CAACC,MAAM,QAAO,eACxC,gB,+CAMRlF,mBAAA,wBAA2B,EAC3BC,mBAAA,CA6BM,OA7BNkG,WA6BM,GA5BJlG,mBAAA,CA2BM,OA3BNmG,WA2BM,G,4BA1BJnG,mBAAA,CAGK;IAHDvB,KAAK,EAAC;EAAgB,IACxBuB,mBAAA,CAAiC;IAA9BvB,KAAK,EAAC;EAAmB,I,iBAAK,sBAEnC,E,sBACWO,KAAA,CAAAoH,cAAc,CAACnB,MAAM,U,cAAhCtG,mBAAA,CAGM,OAHN0H,WAGM,EAAAhG,MAAA,SAAAA,MAAA,QAFJL,mBAAA,CAAqD;IAAlDvB,KAAK,EAAC;EAAuC,4BAChDuB,mBAAA,CAAyC;IAAtCvB,KAAK,EAAC;EAAM,GAAC,uBAAqB,oB,qBAEvCE,mBAAA,CAiBM,OAAA2H,WAAA,I,kBAhBJ3H,mBAAA,CAUMyG,SAAA,QAAAC,WAAA,CAViBrG,KAAA,CAAAoH,cAAc,CAACd,KAAK,QAA/BC,OAAO;yBAAnB5G,mBAAA,CAUM;MAV8C6G,GAAG,EAAED,OAAO,CAACE,EAAE;MAC9DhH,KAAK,EAAC;QACTuB,mBAAA,CAIM,cAHJA,mBAAA,CAA2E,SAA3EuG,WAA2E,EAAA/D,gBAAA,CAA9ClD,QAAA,CAAAkH,aAAa,CAACjB,OAAO,CAACkB,YAAY,mBAC/DzG,mBAAA,CAAsD,OAAtD0G,WAAsD,EAAAlE,gBAAA,CAA9B+C,OAAO,CAACM,aAAa,kBAC7C7F,mBAAA,CAA0D,SAA1D2G,WAA0D,EAAAnE,gBAAA,CAA9B+C,OAAO,CAACqB,WAAW,iB,GAEjD5G,mBAAA,CAES;MAFDvB,KAAK,EAAC,gCAAgC;MAAE2B,OAAK,EAAA4B,MAAA,IAAE1C,QAAA,CAAA0G,kBAAkB,CAACT,OAAO,CAACE,EAAE;yCAClFzF,mBAAA,CAA0B;MAAvBvB,KAAK,EAAC;IAAY,2B;kCAGdO,KAAA,CAAAoH,cAAc,CAACnB,MAAM,Q,cAAhCtG,mBAAA,CAIM,OAJNkI,WAIM,GAHJ7G,mBAAA,CAES;IAFDvB,KAAK,EAAC,6BAA6B;IAAE2B,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAA2B,MAAA,IAAE1C,QAAA,CAAAqF,kBAAkB;KAAY,QAC3E,GAAAnC,gBAAA,CAAGxD,KAAA,CAAAoH,cAAc,CAACnB,MAAM,QAAO,eACtC,gB,+CAMRlF,mBAAA,yBAA4B,EAC5BC,mBAAA,CAwCM,OAxCN8G,WAwCM,GAvCJ9G,mBAAA,CAsCM,OAtCN+G,WAsCM,G,4BArCJ/G,mBAAA,CAGK;IAHDvB,KAAK,EAAC;EAAmB,IAC3BuB,mBAAA,CAAgC;IAA7BvB,KAAK,EAAC;EAAkB,I,iBAAK,uBAElC,E,sBACAuB,mBAAA,CAQM,OARNgH,WAQM,GAPJhH,mBAAA,CAGM,OAHNiH,WAGM,G,4BAFJjH,mBAAA,CAAyC;IAAnCvB,KAAK,EAAC;EAAO,GAAC,gBAAc,sBAClCuB,mBAAA,CAAsE,QAAtEkH,WAAsE,EAAA1E,gBAAA,CAApCxD,KAAA,CAAA4B,KAAK,CAACC,eAAe,sB,GAEzDb,mBAAA,CAEM,OAFNmH,WAEM,GADJnH,mBAAA,CAAsG;IAAjGvB,KAAK,EAAC,yBAAyB;IAAEC,KAAK,EAAA0I,eAAA;MAAAC,KAAA,EAAW/H,QAAA,CAAAgI,qBAAqB;IAAA;+BAG/EtH,mBAAA,CAQM,OARNuH,WAQM,GAPJvH,mBAAA,CAGM,OAHNwH,WAGM,G,4BAFJxH,mBAAA,CAAqC;IAA/BvB,KAAK,EAAC;EAAO,GAAC,YAAU,sBAC9BuB,mBAAA,CAAsE,QAAtEyH,WAAsE,EAAAjF,gBAAA,CAAvCxD,KAAA,CAAA4B,KAAK,CAAC6C,kBAAkB,sB,GAEzDzD,mBAAA,CAEM,OAFN0H,WAEM,GADJ1H,mBAAA,CAAsG;IAAjGvB,KAAK,EAAC,sBAAsB;IAAEC,KAAK,EAAA0I,eAAA;MAAAC,KAAA,EAAW/H,QAAA,CAAAgI,qBAAqB;IAAA;+BAG5EtH,mBAAA,CAQM,OARN2H,WAQM,GAPJ3H,mBAAA,CAGM,OAHN4H,WAGM,G,4BAFJ5H,mBAAA,CAAoC;IAA9BvB,KAAK,EAAC;EAAO,GAAC,WAAS,sBAC7BuB,mBAAA,CAAwE,QAAxE6H,WAAwE,EAAArF,gBAAA,CAAtCxD,KAAA,CAAA4B,KAAK,CAACkH,iBAAiB,sB,GAE3D9H,mBAAA,CAEM,OAFN+H,WAEM,GADJ/H,mBAAA,CAAwG;IAAnGvB,KAAK,EAAC,yBAAyB;IAAEC,KAAK,EAAA0I,eAAA;MAAAC,KAAA,EAAW/H,QAAA,CAAAgI,qBAAqB;IAAA;+BAG/EtH,mBAAA,CAKM,OALNgI,WAKM,GAJJhI,mBAAA,CAGS;IAHDvB,KAAK,EAAC,wBAAwB;IAAE2B,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAA2B,MAAA,IAAE1C,QAAA,CAAA2C,UAAU;kCACvDjC,mBAAA,CAAgC;IAA7BvB,KAAK,EAAC;EAAkB,4B,iBAAK,qBAElC,E,oBAUhBsB,mBAAA,qBAAwB,EACxBC,mBAAA,CA2GM,OA3GNiI,WA2GM,GA1GJjI,mBAAA,CA4EM,OA5ENkI,WA4EM,GA3EJlI,mBAAA,CA0EM,OA1ENmI,WA0EM,GAzEJnI,mBAAA,CA2BM,OA3BNoI,WA2BM,GA1BJpI,mBAAA,CAOK,MAPLqI,WAOK,G,4BANHrI,mBAAA,CAAmC;IAAhCvB,KAAK,EAAC;EAAqB,6B,6CAAK,mBAEnC,IAAgDO,KAAA,CAAAsJ,cAAc,CAACrD,MAAM,Q,cAArEtG,mBAAA,CAGO,QAHP4J,WAGO,EAAAlI,MAAA,SAAAA,MAAA,QAFLL,mBAAA,CAA6B;IAA1BvB,KAAK,EAAC;EAAe,4B,iBAAK,QAE/B,E,2CAEFuB,mBAAA,CAiBM,OAjBNwI,WAiBM,G,4BAhBJxI,mBAAA,CAES;IAFDvB,KAAK,EAAC,gCAAgC;IAACoD,IAAI,EAAC,QAAQ;IAAC,gBAAc,EAAC;MAC1E7B,mBAAA,CAAiC;IAA9BvB,KAAK,EAAC;EAAmB,G,sBAE9BuB,mBAAA,CAYM,OAZNyI,WAYM,G,4BAXJzI,mBAAA,CAA2C;IAAtCvB,KAAK,EAAC;EAAiB,GAAC,UAAQ,sBACrCuB,mBAAA,CAEI;IAFDvB,KAAK,EAAC,eAAe;IAACsD,IAAI,EAAC,GAAG;IAAE3B,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAA2B,MAAA,IAAE1C,QAAA,CAAA2C,UAAU;kCAClDjC,mBAAA,CAAgC;IAA7BvB,KAAK,EAAC;EAAkB,4B,iBAAK,WAClC,E,IACAuB,mBAAA,CAEI;IAFDvB,KAAK,EAAC,eAAe;IAACsD,IAAI,EAAC,GAAG;IAAE3B,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEhB,QAAA,CAAAoJ,cAAA,IAAApJ,QAAA,CAAAoJ,cAAA,IAAApI,IAAA,CAAc;kCACtDN,mBAAA,CAAoC;IAAjCvB,KAAK,EAAC;EAAsB,4B,iBAAK,SACtC,E,gCACAuB,mBAAA,CAAoC;IAA/BvB,KAAK,EAAC;EAAkB,6BAC7BuB,mBAAA,CAEI;IAFDvB,KAAK,EAAC,eAAe;IAACsD,IAAI,EAAC,GAAG;IAAE3B,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEhB,QAAA,CAAAmC,gBAAA,IAAAnC,QAAA,CAAAmC,gBAAA,IAAAnB,IAAA,CAAgB;kCACxDN,mBAAA,CAAoC;IAAjCvB,KAAK,EAAC;EAAsB,4B,iBAAK,UACtC,E,UAINuB,mBAAA,CA4CM,OA5CN2I,WA4CM,GA3CO3J,KAAA,CAAA2C,OAAO,I,cAAlBhD,mBAAA,CAKM,OALNiK,WAKM,EAAAvI,MAAA,SAAAA,MAAA,QAJJL,mBAAA,CAEM;IAFDvB,KAAK,EAAC,6BAA6B;IAACoK,IAAI,EAAC;MAC5C7I,mBAAA,CAA+C;IAAzCvB,KAAK,EAAC;EAAiB,GAAC,YAAU,E,qBAE1CuB,mBAAA,CAAyD;IAAtDvB,KAAK,EAAC;EAAiB,GAAC,4BAA0B,oB,MAEvCO,KAAA,CAAAsJ,cAAc,CAACrD,MAAM,U,cAArCtG,mBAAA,CAIM,OAJNmK,WAIM,EAAAzI,MAAA,SAAAA,MAAA,QAHJL,mBAAA,CAAqD;IAAlDvB,KAAK,EAAC;EAAuC,4BAChDuB,mBAAA,CAAiD;IAA7CvB,KAAK,EAAC;EAAe,GAAC,oBAAkB,qBAC5CuB,mBAAA,CAAiF;IAA9EvB,KAAK,EAAC;EAAO,GAAC,8DAA4D,oB,qBAE/EE,mBAAA,CA+BM,OA/BNoK,WA+BM,I,kBA9BJpK,mBAAA,CAuBMyG,SAAA,QAAAC,WAAA,CAvB2BrG,KAAA,CAAAsJ,cAAc,CAAChD,KAAK,SAAxC0D,QAAQ,EAAEC,KAAK;yBAA5BtK,mBAAA,CAuBM;MAvBwD6G,GAAG,EAAEwD,QAAQ,CAACvD,EAAE;MACzEhH,KAAK,EAAAwB,eAAA,EAAC,kDAAkD;QAAA,iBAC7BgJ,KAAK,GAAGjK,KAAA,CAAAsJ,cAAc,CAAChD,KAAK,OAAOL,MAAM;MAAA;QACvEjF,mBAAA,CAIM,OAJNkJ,WAIM,GAHJlJ,mBAAA,CAEM;MAFDvB,KAAK,EAAAwB,eAAA,EAAC,aAAa,EAASX,QAAA,CAAA6J,oBAAoB,CAACH,QAAQ,CAACnH,IAAI;QACjE7B,mBAAA,CAAkE;MAA9DvB,KAAK,EAAAwB,eAAA,EAAEX,QAAA,CAAA8J,eAAe,CAACJ,QAAQ,CAACnH,IAAI,GAAS,YAAY;gDAGjE7B,mBAAA,CAcM,OAdNqJ,WAcM,GAbJrJ,mBAAA,CAYM,OAZNsJ,WAYM,GAXJtJ,mBAAA,CAOM,cANJA,mBAAA,CAA4E,MAA5EuJ,WAA4E,EAAA/G,gBAAA,CAA9CwG,QAAQ,CAACQ,KAAK,IAAIR,QAAQ,CAACS,WAAW,kBACpEzJ,mBAAA,CAAmF,KAAnF0J,WAAmF,EAAAlH,gBAAA,CAA/CwG,QAAQ,CAACW,OAAO,IAAIX,QAAQ,CAACS,WAAW,kBAC5EzJ,mBAAA,CAGM,OAHN4J,WAGM,G,4BAFJ5J,mBAAA,CAAiC;MAA9BvB,KAAK,EAAC;IAAmB,6B,iBAAK,GACjC,GAAA+D,gBAAA,CAAGlD,QAAA,CAAAuK,UAAU,CAACb,QAAQ,CAACc,UAAU,kB,KAGrC9J,mBAAA,CAEO;MAFDvB,KAAK,EAAAwB,eAAA,EAAC,OAAO,EAASX,QAAA,CAAAyK,qBAAqB,CAACf,QAAQ,CAACgB,MAAM;wBAC5DhB,QAAQ,CAACgB,MAAM,uC;kCAKfhL,KAAA,CAAAsJ,cAAc,CAACrD,MAAM,Q,cAAhCtG,mBAAA,CAKM,OALNsL,WAKM,GAJJjK,mBAAA,CAGS;IAHDvB,KAAK,EAAC,gCAAgC;IAAE2B,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAA2B,MAAA,IAAE1C,QAAA,CAAA2C,UAAU;kCAC/DjC,mBAAA,CAAgC;IAA7BvB,KAAK,EAAC;EAAkB,6B,iBAAK,QAC3B,GAAA+D,gBAAA,CAAGxD,KAAA,CAAAsJ,cAAc,CAACrD,MAAM,QAAO,mBACtC,gB,mDAOVlF,mBAAA,mBAAsB,EACtBC,mBAAA,CA0BM,OA1BNkK,WA0BM,GAzBJlK,mBAAA,CAwBM,OAxBNmK,WAwBM,G,4BAvBJnK,mBAAA,CAEM;IAFDvB,KAAK,EAAC;EAAkB,IAC3BuB,mBAAA,CAAgE;IAA5DvB,KAAK,EAAC;EAAmC,GAAC,eAAa,E,sBAE7DuB,mBAAA,CAmBM,OAnBNoK,YAmBM,GAlBJpK,mBAAA,CAiBM,OAjBNqK,YAiBM,GAhBJrK,mBAAA,CAGS;IAHDvB,KAAK,EAAC,wBAAwB;IAAE2B,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAA2B,MAAA,IAAE1C,QAAA,CAAA2C,UAAU;kCACvDjC,mBAAA,CAAiC;IAA9BvB,KAAK,EAAC;EAAmB,4B,iBAAK,gBAEnC,E,IACAuB,mBAAA,CAGS;IAHDvB,KAAK,EAAC,wBAAwB;IAAE2B,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAA2B,MAAA,IAAE1C,QAAA,CAAA2C,UAAU;kCACvDjC,mBAAA,CAAoC;IAAjCvB,KAAK,EAAC;EAAsB,4B,iBAAK,iBAEtC,E,IACAuB,mBAAA,CAGS;IAHDvB,KAAK,EAAC,qBAAqB;IAAE2B,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAA2B,MAAA,IAAE1C,QAAA,CAAA2C,UAAU;kCACpDjC,mBAAA,CAAqC;IAAlCvB,KAAK,EAAC;EAAuB,4B,iBAAK,oBAEvC,E,IACAuB,mBAAA,CAGS;IAHDvB,KAAK,EAAC,wBAAwB;IAAE2B,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAA2B,MAAA,IAAE1C,QAAA,CAAA2C,UAAU;kCACvDjC,mBAAA,CAA+B;IAA5BvB,KAAK,EAAC;EAAiB,4B,iBAAK,mBAEjC,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}