import axios from 'axios';

class NotificationService {
  constructor() {
    this.eventSource = null;
    this.listeners = new Map();
    this.isConnected = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 1000; // Start with 1 second
    this.maxReconnectDelay = 30000; // Max 30 seconds
    this.baseURL = process.env.VUE_APP_API_URL || 'http://localhost:3000/api';
    this.connectionRefs = 0; // Track how many components are using the connection
    this.currentUserType = null; // Track current connection type
    this.isConnecting = false; // Prevent multiple simultaneous connection attempts
  }

  /**
   * Initialize connection (simplified)
   */
  init(userType = 'admin') {
    console.log('🚀 Initializing notification service');
    if (!this.eventSource) {
      this.connect(userType);
    }
    return Promise.resolve();
  }

  /**
   * Cleanup (simplified)
   */
  cleanup() {
    console.log('🧹 Notification service cleanup');
    // Don't disconnect - let connection persist
  }

  /**
   * Connect to SSE stream - Enhanced version with proper endpoint handling
   */
  connect(userType = 'admin') {
    // Don't create multiple connections
    if (this.eventSource) {
      console.log('SSE connection already exists');
      return Promise.resolve();
    }

    console.log('🔗 Creating SSE connection for:', userType);
    this.currentUserType = userType;

    const token = userType === 'admin'
      ? localStorage.getItem('adminToken')
      : localStorage.getItem('clientToken');

    if (!token) {
      console.error('No authentication token found for', userType);
      return Promise.reject(new Error(`No ${userType} token found`));
    }

    // Use unified endpoint for both admin and client
    const url = `${this.baseURL}/notifications/stream?token=${encodeURIComponent(token)}`;
    console.log('🔗 SSE URL:', url.replace(/token=[^&]+/, 'token=***'));

    this.eventSource = new EventSource(url);

    // CRITICAL: Store multiple references to prevent garbage collection
    window.__sseConnection = this.eventSource;
    window.__notificationEventSource = this.eventSource;
    this.__eventSourceRef = this.eventSource;

    this.eventSource.onopen = () => {
      console.log('✅ SSE Connected successfully');
      this.isConnected = true;
      this.emit('connected');
    };

    this.eventSource.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        console.log('📨 SSE Message:', data);
        this.handleNotification(data);
      } catch (error) {
        console.error('SSE parse error:', error);
      }
    };

    this.eventSource.onerror = (error) => {
      console.error('❌ SSE Error:', error);
      console.error('ReadyState:', this.eventSource?.readyState);
      this.isConnected = false;

      // Don't auto-reconnect to prevent loops
      console.log('SSE connection failed - not auto-reconnecting');
    };

    return Promise.resolve();
  }

  /**
   * Disconnect from SSE stream
   */
  disconnect() {
    console.log('🔌 disconnect() called');
    console.log('📊 Connection refs:', this.connectionRefs);
    console.log('📊 Stack trace:', new Error().stack);

    if (this.eventSource) {
      console.log('🔌 Closing EventSource connection');
      this.eventSource.close();
      this.eventSource = null;
      this.isConnected = false;
      this.connectionRefs = 0; // Reset refs when manually disconnecting

      // Clear global reference
      if (window.__notificationEventSource) {
        delete window.__notificationEventSource;
      }

      console.log('Disconnected from notification stream');
      this.emit('disconnected');
    }
  }

  /**
   * Schedule reconnection attempt
   */
  scheduleReconnect() {
    console.log('🚫 Auto-reconnect disabled to prevent connection loops');
    // Disabled to prevent connection issues during debugging
  }

  /**
   * Handle incoming notification
   */
  handleNotification(notification) {
    console.log('📢 Received notification:', notification);
    
    // Emit to specific type listeners
    this.emit(notification.type, notification);
    
    // Emit to general notification listeners
    this.emit('notification', notification);
    
    // Show browser notification if permission granted
    this.showBrowserNotification(notification);
  }

  /**
   * Show browser notification
   */
  showBrowserNotification(notification) {
    if ('Notification' in window && Notification.permission === 'granted') {
      const options = {
        body: notification.message,
        icon: '/favicon.ico',
        badge: '/favicon.ico',
        tag: `notification-${notification.id}`,
        requireInteraction: notification.priority === 'high' || notification.priority === 'urgent'
      };

      const browserNotification = new Notification(notification.title, options);
      
      browserNotification.onclick = () => {
        window.focus();
        this.emit('notification_click', notification);
        browserNotification.close();
      };

      // Auto close after 5 seconds for normal priority
      if (notification.priority !== 'high' && notification.priority !== 'urgent') {
        setTimeout(() => {
          browserNotification.close();
        }, 5000);
      }
    }
  }

  /**
   * Request browser notification permission
   */
  async requestNotificationPermission() {
    if ('Notification' in window) {
      const permission = await Notification.requestPermission();
      return permission === 'granted';
    }
    return false;
  }

  /**
   * Subscribe to notification events
   */
  on(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, new Set());
    }
    this.listeners.get(event).add(callback);
  }

  /**
   * Unsubscribe from notification events
   */
  off(event, callback) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).delete(callback);
    }
  }

  /**
   * Emit event to listeners
   */
  emit(event, data = null) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`Error in notification listener for ${event}:`, error);
        }
      });
    }
  }

  /**
   * Get user notifications
   */
  async getNotifications(page = 1, limit = 20, unreadOnly = false) {
    try {
      const isAdmin = !!localStorage.getItem('adminToken');
      const token = isAdmin
        ? localStorage.getItem('adminToken')
        : localStorage.getItem('clientToken');

      if (!token) {
        throw new Error('No authentication token found');
      }

      // Use unified endpoint for both admin and client
      const response = await axios.get(`${this.baseURL}/notifications`, {
        params: { page, limit, unread_only: unreadOnly },
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      return response.data;
    } catch (error) {
      console.error('Failed to get notifications:', error);
      throw error;
    }
  }

  /**
   * Get unread notification count
   */
  async getUnreadCount() {
    try {
      const isAdmin = !!localStorage.getItem('adminToken');
      const token = isAdmin
        ? localStorage.getItem('adminToken')
        : localStorage.getItem('clientToken');

      if (!token) {
        throw new Error('No authentication token found');
      }

      // Use unified endpoint for both admin and client
      const response = await axios.get(`${this.baseURL}/notifications/unread-count`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      return response.data.data.count;
    } catch (error) {
      console.error('Failed to get unread count:', error);
      throw error;
    }
  }

  /**
   * Mark notification as read
   */
  async markAsRead(notificationId) {
    try {
      const isAdmin = !!localStorage.getItem('adminToken');
      const token = isAdmin
        ? localStorage.getItem('adminToken')
        : localStorage.getItem('clientToken');

      if (!token) {
        throw new Error('No authentication token found');
      }

      // Use unified endpoint for both admin and client
      const response = await axios.put(`${this.baseURL}/notifications/${notificationId}/read`, {}, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      return response.data;
    } catch (error) {
      console.error('Failed to mark notification as read:', error);
      throw error;
    }
  }

  /**
   * Mark all notifications as read
   */
  async markAllAsRead() {
    try {
      const isAdmin = !!localStorage.getItem('adminToken');
      const token = isAdmin
        ? localStorage.getItem('adminToken')
        : localStorage.getItem('clientToken');

      if (!token) {
        throw new Error('No authentication token found');
      }

      // Use unified endpoint for both admin and client
      const response = await axios.put(`${this.baseURL}/notifications/mark-all-read`, {}, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      return response.data;
    } catch (error) {
      console.error('Failed to mark all notifications as read:', error);
      throw error;
    }
  }

  /**
   * Send test notification (admin only)
   */
  async sendTestNotification(data) {
    try {
      const token = localStorage.getItem('adminToken');
      const response = await axios.post(`${this.baseURL}/notifications/test`, data, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      return response.data;
    } catch (error) {
      console.error('Failed to send test notification:', error);
      throw error;
    }
  }

  /**
   * Get notification statistics (admin only)
   */
  async getStatistics() {
    try {
      const token = localStorage.getItem('adminToken');
      const response = await axios.get(`${this.baseURL}/notifications/statistics`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      return response.data;
    } catch (error) {
      console.error('Failed to get notification statistics:', error);
      throw error;
    }
  }

  /**
   * Test SSE connection (for debugging)
   */
  async testConnection() {
    console.log('🧪 Testing SSE connection...');

    try {
      // Clear any existing connection
      if (this.eventSource) {
        console.log('🧪 Clearing existing connection');
        this.eventSource.close();
        this.eventSource = null;
        this.isConnected = false;
        this.isConnecting = false;
      }

      // Reset state
      this.connectionRefs = 1;

      // Test connection
      await this.connect('admin');

      console.log('🧪 Test connection established');

      // Keep connection alive for 10 seconds
      setTimeout(() => {
        console.log('🧪 Test completed, keeping connection');
      }, 10000);

    } catch (error) {
      console.error('🧪 Test connection failed:', error);
    }
  }

  /**
   * Clean up old notifications (admin only)
   */
  async cleanupOldNotifications(days = 90) {
    try {
      const token = localStorage.getItem('adminToken');
      const response = await axios.delete(`${this.baseURL}/notifications/cleanup`, {
        params: { days },
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      return response.data;
    } catch (error) {
      console.error('Failed to cleanup old notifications:', error);
      throw error;
    }
  }
}

// Create singleton instance
const notificationService = new NotificationService();

// Make available globally for debugging
window.__notificationService = notificationService;

// Add global test function
window.testSSE = () => {
  console.log('🧪 Testing SSE connection manually...');
  notificationService.connect('admin');
};

export default notificationService;
