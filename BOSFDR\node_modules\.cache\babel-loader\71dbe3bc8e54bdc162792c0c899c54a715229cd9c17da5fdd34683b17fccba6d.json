{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.find.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport documentRequestService from '@/services/documentRequestService';\nimport clientAuthService from '@/services/clientAuthService';\nexport default {\n  name: 'BarangayClearanceRequest',\n  data() {\n    return {\n      currentStep: 1,\n      submitting: false,\n      purposeCategories: [],\n      paymentMethods: [],\n      baseFee: 50.00,\n      totalFee: 50.00,\n      formData: {\n        document_type_id: 2,\n        // Barangay Clearance\n        purpose_category_id: '',\n        purpose_details: '',\n        emergency_contact_name: '',\n        emergency_contact_relationship: '',\n        emergency_contact_phone: '',\n        emergency_contact_address: '',\n        has_pending_cases: false,\n        pending_cases_details: '',\n        is_registered_voter: null,\n        additional_notes: '',\n        payment_method_id: '',\n        agree_to_terms: false\n      }\n    };\n  },\n  computed: {\n    clientData() {\n      return clientAuthService.getCurrentUser();\n    }\n  },\n  async mounted() {\n    await this.loadFormData();\n  },\n  methods: {\n    async loadFormData() {\n      try {\n        const [purposeResponse, paymentResponse] = await Promise.all([documentRequestService.getPurposeCategories(), documentRequestService.getPaymentMethods()]);\n        this.purposeCategories = purposeResponse.data || [];\n        this.paymentMethods = paymentResponse.data || [];\n      } catch (error) {\n        console.error('Error loading form data:', error);\n        this.$toast?.error('Failed to load form data');\n      }\n    },\n    getFullName() {\n      const profile = this.clientData?.profile;\n      if (!profile) return 'N/A';\n      return `${profile.first_name || ''} ${profile.middle_name || ''} ${profile.last_name || ''}`.trim();\n    },\n    getFullAddress() {\n      const profile = this.clientData?.profile;\n      if (!profile) return 'Not provided';\n      const parts = [profile.house_number, profile.street, profile.barangay, profile.city, profile.province].filter(Boolean);\n      return parts.length > 0 ? parts.join(', ') : 'Not provided';\n    },\n    formatDate(dateString) {\n      if (!dateString) return 'Not provided';\n      return new Date(dateString).toLocaleDateString();\n    },\n    formatCurrency(amount) {\n      return parseFloat(amount).toFixed(2);\n    },\n    canProceedToNextStep() {\n      switch (this.currentStep) {\n        case 1:\n          return this.formData.emergency_contact_name && this.formData.emergency_contact_relationship && this.formData.emergency_contact_phone;\n        case 2:\n          return this.formData.purpose_category_id && this.formData.purpose_details && this.formData.has_pending_cases !== null;\n        case 3:\n          return this.formData.payment_method_id;\n        default:\n          return true;\n      }\n    },\n    nextStep() {\n      if (this.canProceedToNextStep() && this.currentStep < 4) {\n        this.currentStep++;\n      }\n    },\n    previousStep() {\n      if (this.currentStep > 1) {\n        this.currentStep--;\n      }\n    },\n    onPurposeChange() {\n      // Could implement dynamic fee calculation based on purpose\n    },\n    selectPaymentMethod(methodId) {\n      this.formData.payment_method_id = methodId;\n    },\n    getPaymentIcon(methodCode) {\n      const icons = {\n        'CASH': 'fas fa-money-bill',\n        'PAYMONGO_CARD': 'fas fa-credit-card',\n        'PAYMONGO_GCASH': 'fab fa-google-pay',\n        'PAYMONGO_GRABPAY': 'fas fa-mobile-alt',\n        'PAYMONGO_PAYMAYA': 'fas fa-wallet'\n      };\n      return icons[methodCode] || 'fas fa-credit-card';\n    },\n    getPurposeCategoryName() {\n      const category = this.purposeCategories.find(c => c.id === this.formData.purpose_category_id);\n      return category?.category_name || '';\n    },\n    getPaymentMethodName() {\n      const method = this.paymentMethods.find(m => m.id === this.formData.payment_method_id);\n      return method?.method_name || '';\n    },\n    async handleSubmit() {\n      if (!this.formData.agree_to_terms) return;\n      try {\n        this.submitting = true;\n\n        // Prepare request data with proper validation\n        const requestData = {\n          document_type_id: parseInt(this.formData.document_type_id),\n          purpose_category_id: parseInt(this.formData.purpose_category_id),\n          purpose_details: this.formData.purpose_details && this.formData.purpose_details.length >= 10 ? this.formData.purpose_details : 'Barangay Clearance request for official purposes',\n          payment_method_id: parseInt(this.formData.payment_method_id),\n          delivery_method: 'pickup',\n          // Default to pickup\n          priority: 'normal',\n          // Barangay Clearance specific fields\n          emergency_contact_name: this.formData.emergency_contact_name,\n          emergency_contact_relationship: this.formData.emergency_contact_relationship,\n          emergency_contact_phone: this.formData.emergency_contact_phone,\n          emergency_contact_address: this.formData.emergency_contact_address,\n          has_pending_cases: Boolean(this.formData.has_pending_cases),\n          pending_cases_details: this.formData.pending_cases_details || '',\n          is_registered_voter: this.formData.is_registered_voter,\n          additional_notes: this.formData.additional_notes || '',\n          total_fee: this.totalFee\n        };\n        console.log('Submitting request data:', requestData);\n        const response = await documentRequestService.submitRequest(requestData);\n        this.$toast?.success('Request submitted successfully!');\n        this.$router.push({\n          name: 'RequestDetails',\n          params: {\n            id: response.data.id\n          }\n        });\n      } catch (error) {\n        console.error('Error submitting request:', error);\n        console.error('Error details:', {\n          status: error.response?.status,\n          data: error.response?.data,\n          message: error.message\n        });\n        let errorMessage = 'Failed to submit request';\n        if (error.response?.data?.message) {\n          errorMessage = error.response.data.message;\n        } else if (error.response?.data?.errors) {\n          errorMessage = error.response.data.errors.map(e => e.msg).join(', ');\n        } else if (error.message) {\n          errorMessage = error.message;\n        }\n        this.$toast?.error(errorMessage);\n      } finally {\n        this.submitting = false;\n      }\n    },\n    goBack() {\n      this.$router.push({\n        name: 'NewDocumentRequest'\n      });\n    },\n    updateProfile() {\n      // TODO: Navigate to profile update page\n      console.log('Update profile');\n    },\n    showTerms() {\n      // TODO: Show terms and conditions modal\n      console.log('Show terms');\n    }\n  }\n};", "map": {"version": 3, "names": ["documentRequestService", "clientAuthService", "name", "data", "currentStep", "submitting", "purposeCategories", "paymentMethods", "baseFee", "totalFee", "formData", "document_type_id", "purpose_category_id", "purpose_details", "emergency_contact_name", "emergency_contact_relationship", "emergency_contact_phone", "emergency_contact_address", "has_pending_cases", "pending_cases_details", "is_registered_voter", "additional_notes", "payment_method_id", "agree_to_terms", "computed", "clientData", "getCurrentUser", "mounted", "loadFormData", "methods", "purposeResponse", "paymentResponse", "Promise", "all", "getPurposeCategories", "getPaymentMethods", "error", "console", "$toast", "getFullName", "profile", "first_name", "middle_name", "last_name", "trim", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parts", "house_number", "street", "barangay", "city", "province", "filter", "Boolean", "length", "join", "formatDate", "dateString", "Date", "toLocaleDateString", "formatCurrency", "amount", "parseFloat", "toFixed", "canProceedToNextStep", "nextStep", "previousStep", "onPurposeChange", "selectPaymentMethod", "methodId", "getPaymentIcon", "methodCode", "icons", "getPurposeCategoryName", "category", "find", "c", "id", "category_name", "getPaymentMethodName", "method", "m", "method_name", "handleSubmit", "requestData", "parseInt", "delivery_method", "priority", "total_fee", "log", "response", "submitRequest", "success", "$router", "push", "params", "status", "message", "errorMessage", "errors", "map", "e", "msg", "goBack", "updateProfile", "showTerms"], "sources": ["D:\\rhai_front_and_back\\BOSFDR\\src\\components\\client\\BarangayClearanceRequest.vue"], "sourcesContent": ["<template>\n  <div class=\"barangay-clearance-request\">\n    <!-- Header -->\n    <div class=\"page-header\">\n      <div class=\"header-content\">\n        <div class=\"header-main\">\n          <h1 class=\"page-title\">\n            <i class=\"fas fa-certificate\"></i>\n            Barangay Clearance Request\n          </h1>\n          <p class=\"page-description\">\n            Apply for your Barangay Clearance certificate online\n          </p>\n        </div>\n        <button class=\"back-btn\" @click=\"goBack\">\n          <i class=\"fas fa-arrow-left\"></i>\n          Back\n        </button>\n      </div>\n    </div>\n\n    <!-- Progress Steps -->\n    <div class=\"progress-steps\">\n      <div class=\"step\" :class=\"{ active: currentStep >= 1, completed: currentStep > 1 }\">\n        <div class=\"step-number\">1</div>\n        <span class=\"step-label\">Personal Info</span>\n      </div>\n      <div class=\"step\" :class=\"{ active: currentStep >= 2, completed: currentStep > 2 }\">\n        <div class=\"step-number\">2</div>\n        <span class=\"step-label\">Purpose & Details</span>\n      </div>\n      <div class=\"step\" :class=\"{ active: currentStep >= 3, completed: currentStep > 3 }\">\n        <div class=\"step-number\">3</div>\n        <span class=\"step-label\">Payment</span>\n      </div>\n      <div class=\"step\" :class=\"{ active: currentStep >= 4 }\">\n        <div class=\"step-number\">4</div>\n        <span class=\"step-label\">Review & Submit</span>\n      </div>\n    </div>\n\n    <!-- Form Container -->\n    <div class=\"form-container\">\n      <form @submit.prevent=\"handleSubmit\">\n        \n        <!-- Step 1: Personal Information -->\n        <div v-if=\"currentStep === 1\" class=\"form-step\">\n          <div class=\"step-header\">\n            <h2>Personal Information</h2>\n            <p>Your profile information will be used for this request</p>\n          </div>\n\n          <div class=\"profile-preview\">\n            <div class=\"profile-card\">\n              <div class=\"profile-info\">\n                <h3>{{ getFullName() }}</h3>\n                <div class=\"info-grid\">\n                  <div class=\"info-item\">\n                    <label>Email:</label>\n                    <span>{{ clientData?.profile?.email || 'Not provided' }}</span>\n                  </div>\n                  <div class=\"info-item\">\n                    <label>Phone:</label>\n                    <span>{{ clientData?.profile?.phone_number || 'Not provided' }}</span>\n                  </div>\n                  <div class=\"info-item\">\n                    <label>Address:</label>\n                    <span>{{ getFullAddress() }}</span>\n                  </div>\n                  <div class=\"info-item\">\n                    <label>Date of Birth:</label>\n                    <span>{{ formatDate(clientData?.profile?.date_of_birth) }}</span>\n                  </div>\n                </div>\n              </div>\n              <div class=\"profile-actions\">\n                <button type=\"button\" class=\"update-profile-btn\" @click=\"updateProfile\">\n                  <i class=\"fas fa-edit\"></i>\n                  Update Profile\n                </button>\n              </div>\n            </div>\n          </div>\n\n          <!-- Emergency Contact Information -->\n          <div class=\"form-section\">\n            <h3>Emergency Contact Information</h3>\n            <div class=\"form-grid\">\n              <div class=\"form-group\">\n                <label for=\"emergency_contact_name\">Contact Name *</label>\n                <input\n                  id=\"emergency_contact_name\"\n                  v-model=\"formData.emergency_contact_name\"\n                  type=\"text\"\n                  required\n                  placeholder=\"Full name of emergency contact\"\n                />\n              </div>\n              <div class=\"form-group\">\n                <label for=\"emergency_contact_relationship\">Relationship *</label>\n                <select\n                  id=\"emergency_contact_relationship\"\n                  v-model=\"formData.emergency_contact_relationship\"\n                  required\n                >\n                  <option value=\"\">Select relationship</option>\n                  <option value=\"Spouse\">Spouse</option>\n                  <option value=\"Parent\">Parent</option>\n                  <option value=\"Child\">Child</option>\n                  <option value=\"Sibling\">Sibling</option>\n                  <option value=\"Relative\">Relative</option>\n                  <option value=\"Friend\">Friend</option>\n                  <option value=\"Other\">Other</option>\n                </select>\n              </div>\n              <div class=\"form-group\">\n                <label for=\"emergency_contact_phone\">Contact Phone *</label>\n                <input\n                  id=\"emergency_contact_phone\"\n                  v-model=\"formData.emergency_contact_phone\"\n                  type=\"tel\"\n                  required\n                  placeholder=\"09XXXXXXXXX\"\n                />\n              </div>\n              <div class=\"form-group\">\n                <label for=\"emergency_contact_address\">Contact Address</label>\n                <textarea\n                  id=\"emergency_contact_address\"\n                  v-model=\"formData.emergency_contact_address\"\n                  rows=\"2\"\n                  placeholder=\"Complete address of emergency contact\"\n                ></textarea>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Step 2: Purpose and Details -->\n        <div v-if=\"currentStep === 2\" class=\"form-step\">\n          <div class=\"step-header\">\n            <h2>Purpose and Additional Details</h2>\n            <p>Please provide the purpose and any additional information</p>\n          </div>\n\n          <div class=\"form-section\">\n            <div class=\"form-group\">\n              <label for=\"purpose_category\">Purpose Category *</label>\n              <select\n                id=\"purpose_category\"\n                v-model=\"formData.purpose_category_id\"\n                required\n                @change=\"onPurposeChange\"\n              >\n                <option value=\"\">Select purpose</option>\n                <option\n                  v-for=\"category in purposeCategories\"\n                  :key=\"category.id\"\n                  :value=\"category.id\"\n                >\n                  {{ category.category_name }}\n                </option>\n              </select>\n            </div>\n\n            <div class=\"form-group\">\n              <label for=\"purpose_details\">Purpose Details *</label>\n              <textarea\n                id=\"purpose_details\"\n                v-model=\"formData.purpose_details\"\n                rows=\"3\"\n                required\n                placeholder=\"Please provide specific details about the purpose of this clearance\"\n              ></textarea>\n            </div>\n\n            <div class=\"form-group\">\n              <label for=\"pending_cases\">Pending Cases Declaration *</label>\n              <div class=\"radio-group\">\n                <label class=\"radio-option\">\n                  <input\n                    type=\"radio\"\n                    v-model=\"formData.has_pending_cases\"\n                    :value=\"false\"\n                    required\n                  />\n                  <span class=\"radio-custom\"></span>\n                  No pending cases\n                </label>\n                <label class=\"radio-option\">\n                  <input\n                    type=\"radio\"\n                    v-model=\"formData.has_pending_cases\"\n                    :value=\"true\"\n                    required\n                  />\n                  <span class=\"radio-custom\"></span>\n                  Has pending cases\n                </label>\n              </div>\n            </div>\n\n            <div v-if=\"formData.has_pending_cases\" class=\"form-group\">\n              <label for=\"pending_cases_details\">Pending Cases Details *</label>\n              <textarea\n                id=\"pending_cases_details\"\n                v-model=\"formData.pending_cases_details\"\n                rows=\"3\"\n                required\n                placeholder=\"Please provide details about pending cases\"\n              ></textarea>\n            </div>\n\n            <div class=\"form-group\">\n              <label for=\"voter_registration\">Voter Registration Status</label>\n              <div class=\"radio-group\">\n                <label class=\"radio-option\">\n                  <input\n                    type=\"radio\"\n                    v-model=\"formData.is_registered_voter\"\n                    :value=\"true\"\n                  />\n                  <span class=\"radio-custom\"></span>\n                  Registered voter\n                </label>\n                <label class=\"radio-option\">\n                  <input\n                    type=\"radio\"\n                    v-model=\"formData.is_registered_voter\"\n                    :value=\"false\"\n                  />\n                  <span class=\"radio-custom\"></span>\n                  Not registered\n                </label>\n              </div>\n            </div>\n\n            <div class=\"form-group\">\n              <label for=\"additional_notes\">Additional Notes</label>\n              <textarea\n                id=\"additional_notes\"\n                v-model=\"formData.additional_notes\"\n                rows=\"2\"\n                placeholder=\"Any additional information or special requests\"\n              ></textarea>\n            </div>\n          </div>\n        </div>\n\n        <!-- Step 3: Payment Method -->\n        <div v-if=\"currentStep === 3\" class=\"form-step\">\n          <div class=\"step-header\">\n            <h2>Payment Information</h2>\n            <p>Choose your preferred payment method</p>\n          </div>\n\n          <!-- Fee Summary -->\n          <div class=\"fee-summary\">\n            <div class=\"fee-card\">\n              <h3>Fee Breakdown</h3>\n              <div class=\"fee-items\">\n                <div class=\"fee-item\">\n                  <span>Barangay Clearance Fee</span>\n                  <span>₱{{ formatCurrency(baseFee) }}</span>\n                </div>\n                <div class=\"fee-item total\">\n                  <span>Total Amount</span>\n                  <span>₱{{ formatCurrency(totalFee) }}</span>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Payment Methods -->\n          <div class=\"form-section\">\n            <h3>Select Payment Method</h3>\n            <div class=\"payment-methods\">\n              <div\n                v-for=\"method in paymentMethods\"\n                :key=\"method.id\"\n                class=\"payment-option\"\n                :class=\"{ selected: formData.payment_method_id === method.id }\"\n                @click=\"selectPaymentMethod(method.id)\"\n              >\n                <div class=\"payment-icon\">\n                  <i :class=\"getPaymentIcon(method.method_code)\"></i>\n                </div>\n                <div class=\"payment-info\">\n                  <h4>{{ method.method_name }}</h4>\n                  <p v-if=\"method.description\">{{ method.description }}</p>\n                </div>\n                <div class=\"payment-radio\">\n                  <input\n                    type=\"radio\"\n                    :value=\"method.id\"\n                    v-model=\"formData.payment_method_id\"\n                    required\n                  />\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Step 4: Review and Submit -->\n        <div v-if=\"currentStep === 4\" class=\"form-step\">\n          <div class=\"step-header\">\n            <h2>Review Your Request</h2>\n            <p>Please review all information before submitting</p>\n          </div>\n\n          <div class=\"review-sections\">\n            <!-- Personal Information Review -->\n            <div class=\"review-section\">\n              <h3>Personal Information</h3>\n              <div class=\"review-grid\">\n                <div class=\"review-item\">\n                  <label>Full Name:</label>\n                  <span>{{ getFullName() }}</span>\n                </div>\n                <div class=\"review-item\">\n                  <label>Emergency Contact:</label>\n                  <span>{{ formData.emergency_contact_name }} ({{ formData.emergency_contact_relationship }})</span>\n                </div>\n                <div class=\"review-item\">\n                  <label>Emergency Phone:</label>\n                  <span>{{ formData.emergency_contact_phone }}</span>\n                </div>\n              </div>\n            </div>\n\n            <!-- Purpose Review -->\n            <div class=\"review-section\">\n              <h3>Purpose & Details</h3>\n              <div class=\"review-grid\">\n                <div class=\"review-item\">\n                  <label>Purpose:</label>\n                  <span>{{ getPurposeCategoryName() }}</span>\n                </div>\n                <div class=\"review-item\">\n                  <label>Details:</label>\n                  <span>{{ formData.purpose_details }}</span>\n                </div>\n                <div class=\"review-item\">\n                  <label>Pending Cases:</label>\n                  <span>{{ formData.has_pending_cases ? 'Yes' : 'No' }}</span>\n                </div>\n                <div class=\"review-item\">\n                  <label>Voter Status:</label>\n                  <span>{{ formData.is_registered_voter ? 'Registered' : 'Not Registered' }}</span>\n                </div>\n              </div>\n            </div>\n\n            <!-- Payment Review -->\n            <div class=\"review-section\">\n              <h3>Payment Information</h3>\n              <div class=\"review-grid\">\n                <div class=\"review-item\">\n                  <label>Payment Method:</label>\n                  <span>{{ getPaymentMethodName() }}</span>\n                </div>\n                <div class=\"review-item\">\n                  <label>Total Amount:</label>\n                  <span class=\"amount\">₱{{ formatCurrency(totalFee) }}</span>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Terms and Conditions -->\n          <div class=\"terms-section\">\n            <label class=\"checkbox-option\">\n              <input\n                type=\"checkbox\"\n                v-model=\"formData.agree_to_terms\"\n                required\n              />\n              <span class=\"checkbox-custom\"></span>\n              I agree to the <a href=\"#\" @click.prevent=\"showTerms\">terms and conditions</a> and certify that all information provided is true and accurate.\n            </label>\n          </div>\n        </div>\n\n        <!-- Form Actions -->\n        <div class=\"form-actions\">\n          <button\n            v-if=\"currentStep > 1\"\n            type=\"button\"\n            class=\"btn-secondary\"\n            @click=\"previousStep\"\n          >\n            <i class=\"fas fa-chevron-left\"></i>\n            Previous\n          </button>\n          \n          <button\n            v-if=\"currentStep < 4\"\n            type=\"button\"\n            class=\"btn-primary\"\n            @click=\"nextStep\"\n            :disabled=\"!canProceedToNextStep()\"\n          >\n            Next\n            <i class=\"fas fa-chevron-right\"></i>\n          </button>\n          \n          <button\n            v-if=\"currentStep === 4\"\n            type=\"submit\"\n            class=\"btn-submit\"\n            :disabled=\"submitting || !formData.agree_to_terms\"\n          >\n            <template v-if=\"submitting\">\n              <i class=\"fas fa-spinner fa-spin\"></i>\n              Submitting...\n            </template>\n            <template v-else>\n              <i class=\"fas fa-paper-plane\"></i>\n              Submit Request\n            </template>\n          </button>\n        </div>\n      </form>\n    </div>\n  </div>\n</template>\n\n<script>\nimport documentRequestService from '@/services/documentRequestService';\nimport clientAuthService from '@/services/clientAuthService';\n\nexport default {\n  name: 'BarangayClearanceRequest',\n  data() {\n    return {\n      currentStep: 1,\n      submitting: false,\n      purposeCategories: [],\n      paymentMethods: [],\n      baseFee: 50.00,\n      totalFee: 50.00,\n      formData: {\n        document_type_id: 2, // Barangay Clearance\n        purpose_category_id: '',\n        purpose_details: '',\n        emergency_contact_name: '',\n        emergency_contact_relationship: '',\n        emergency_contact_phone: '',\n        emergency_contact_address: '',\n        has_pending_cases: false,\n        pending_cases_details: '',\n        is_registered_voter: null,\n        additional_notes: '',\n        payment_method_id: '',\n        agree_to_terms: false\n      }\n    };\n  },\n  computed: {\n    clientData() {\n      return clientAuthService.getCurrentUser();\n    }\n  },\n  async mounted() {\n    await this.loadFormData();\n  },\n  methods: {\n    async loadFormData() {\n      try {\n        const [purposeResponse, paymentResponse] = await Promise.all([\n          documentRequestService.getPurposeCategories(),\n          documentRequestService.getPaymentMethods()\n        ]);\n        \n        this.purposeCategories = purposeResponse.data || [];\n        this.paymentMethods = paymentResponse.data || [];\n        \n      } catch (error) {\n        console.error('Error loading form data:', error);\n        this.$toast?.error('Failed to load form data');\n      }\n    },\n\n    getFullName() {\n      const profile = this.clientData?.profile;\n      if (!profile) return 'N/A';\n      return `${profile.first_name || ''} ${profile.middle_name || ''} ${profile.last_name || ''}`.trim();\n    },\n\n    getFullAddress() {\n      const profile = this.clientData?.profile;\n      if (!profile) return 'Not provided';\n      \n      const parts = [\n        profile.house_number,\n        profile.street,\n        profile.barangay,\n        profile.city,\n        profile.province\n      ].filter(Boolean);\n      \n      return parts.length > 0 ? parts.join(', ') : 'Not provided';\n    },\n\n    formatDate(dateString) {\n      if (!dateString) return 'Not provided';\n      return new Date(dateString).toLocaleDateString();\n    },\n\n    formatCurrency(amount) {\n      return parseFloat(amount).toFixed(2);\n    },\n\n    canProceedToNextStep() {\n      switch (this.currentStep) {\n        case 1:\n          return this.formData.emergency_contact_name && \n                 this.formData.emergency_contact_relationship && \n                 this.formData.emergency_contact_phone;\n        case 2:\n          return this.formData.purpose_category_id && \n                 this.formData.purpose_details &&\n                 this.formData.has_pending_cases !== null;\n        case 3:\n          return this.formData.payment_method_id;\n        default:\n          return true;\n      }\n    },\n\n    nextStep() {\n      if (this.canProceedToNextStep() && this.currentStep < 4) {\n        this.currentStep++;\n      }\n    },\n\n    previousStep() {\n      if (this.currentStep > 1) {\n        this.currentStep--;\n      }\n    },\n\n    onPurposeChange() {\n      // Could implement dynamic fee calculation based on purpose\n    },\n\n    selectPaymentMethod(methodId) {\n      this.formData.payment_method_id = methodId;\n    },\n\n    getPaymentIcon(methodCode) {\n      const icons = {\n        'CASH': 'fas fa-money-bill',\n        'PAYMONGO_CARD': 'fas fa-credit-card',\n        'PAYMONGO_GCASH': 'fab fa-google-pay',\n        'PAYMONGO_GRABPAY': 'fas fa-mobile-alt',\n        'PAYMONGO_PAYMAYA': 'fas fa-wallet'\n      };\n      return icons[methodCode] || 'fas fa-credit-card';\n    },\n\n    getPurposeCategoryName() {\n      const category = this.purposeCategories.find(c => c.id === this.formData.purpose_category_id);\n      return category?.category_name || '';\n    },\n\n    getPaymentMethodName() {\n      const method = this.paymentMethods.find(m => m.id === this.formData.payment_method_id);\n      return method?.method_name || '';\n    },\n\n    async handleSubmit() {\n      if (!this.formData.agree_to_terms) return;\n\n      try {\n        this.submitting = true;\n\n        // Prepare request data with proper validation\n        const requestData = {\n          document_type_id: parseInt(this.formData.document_type_id),\n          purpose_category_id: parseInt(this.formData.purpose_category_id),\n          purpose_details: this.formData.purpose_details && this.formData.purpose_details.length >= 10\n            ? this.formData.purpose_details\n            : 'Barangay Clearance request for official purposes',\n          payment_method_id: parseInt(this.formData.payment_method_id),\n          delivery_method: 'pickup', // Default to pickup\n          priority: 'normal',\n          // Barangay Clearance specific fields\n          emergency_contact_name: this.formData.emergency_contact_name,\n          emergency_contact_relationship: this.formData.emergency_contact_relationship,\n          emergency_contact_phone: this.formData.emergency_contact_phone,\n          emergency_contact_address: this.formData.emergency_contact_address,\n          has_pending_cases: Boolean(this.formData.has_pending_cases),\n          pending_cases_details: this.formData.pending_cases_details || '',\n          is_registered_voter: this.formData.is_registered_voter,\n          additional_notes: this.formData.additional_notes || '',\n          total_fee: this.totalFee\n        };\n\n        console.log('Submitting request data:', requestData);\n\n        const response = await documentRequestService.submitRequest(requestData);\n\n        this.$toast?.success('Request submitted successfully!');\n        this.$router.push({\n          name: 'RequestDetails',\n          params: { id: response.data.id }\n        });\n\n      } catch (error) {\n        console.error('Error submitting request:', error);\n        console.error('Error details:', {\n          status: error.response?.status,\n          data: error.response?.data,\n          message: error.message\n        });\n\n        let errorMessage = 'Failed to submit request';\n        if (error.response?.data?.message) {\n          errorMessage = error.response.data.message;\n        } else if (error.response?.data?.errors) {\n          errorMessage = error.response.data.errors.map(e => e.msg).join(', ');\n        } else if (error.message) {\n          errorMessage = error.message;\n        }\n\n        this.$toast?.error(errorMessage);\n      } finally {\n        this.submitting = false;\n      }\n    },\n\n    goBack() {\n      this.$router.push({ name: 'NewDocumentRequest' });\n    },\n\n    updateProfile() {\n      // TODO: Navigate to profile update page\n      console.log('Update profile');\n    },\n\n    showTerms() {\n      // TODO: Show terms and conditions modal\n      console.log('Show terms');\n    }\n  }\n};\n</script>\n\n<style scoped>\n.barangay-clearance-request {\n  padding: 2rem;\n  max-width: 1000px;\n  margin: 0 auto;\n}\n\n.page-header {\n  margin-bottom: 2rem;\n}\n\n.header-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  gap: 2rem;\n}\n\n.page-title {\n  font-size: 1.75rem;\n  font-weight: 700;\n  color: #1a365d;\n  margin-bottom: 0.5rem;\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n}\n\n.page-title i {\n  color: #3182ce;\n}\n\n.page-description {\n  font-size: 1rem;\n  color: #4a5568;\n  margin: 0;\n}\n\n.back-btn {\n  background: #e2e8f0;\n  border: none;\n  padding: 0.75rem 1.5rem;\n  border-radius: 0.5rem;\n  color: #4a5568;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.back-btn:hover {\n  background: #cbd5e0;\n  color: #2d3748;\n}\n\n.progress-steps {\n  display: flex;\n  justify-content: center;\n  margin-bottom: 3rem;\n  position: relative;\n}\n\n.progress-steps::before {\n  content: '';\n  position: absolute;\n  top: 1.5rem;\n  left: 25%;\n  right: 25%;\n  height: 2px;\n  background: #e2e8f0;\n  z-index: 1;\n}\n\n.step {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  flex: 1;\n  position: relative;\n  z-index: 2;\n}\n\n.step-number {\n  width: 3rem;\n  height: 3rem;\n  border-radius: 50%;\n  background: #e2e8f0;\n  color: #a0aec0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: 600;\n  margin-bottom: 0.5rem;\n  transition: all 0.3s;\n}\n\n.step.active .step-number {\n  background: #3182ce;\n  color: white;\n}\n\n.step.completed .step-number {\n  background: #38a169;\n  color: white;\n}\n\n.step-label {\n  font-size: 0.875rem;\n  color: #718096;\n  text-align: center;\n}\n\n.step.active .step-label {\n  color: #3182ce;\n  font-weight: 500;\n}\n\n.form-container {\n  background: white;\n  border-radius: 1rem;\n  padding: 2rem;\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);\n}\n\n.form-step {\n  min-height: 400px;\n}\n\n.step-header {\n  text-align: center;\n  margin-bottom: 2rem;\n}\n\n.step-header h2 {\n  font-size: 1.5rem;\n  font-weight: 600;\n  color: #1a365d;\n  margin-bottom: 0.5rem;\n}\n\n.step-header p {\n  color: #4a5568;\n  margin: 0;\n}\n\n.profile-preview {\n  margin-bottom: 2rem;\n}\n\n.profile-card {\n  background: #f7fafc;\n  border: 1px solid #e2e8f0;\n  border-radius: 0.75rem;\n  padding: 1.5rem;\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n}\n\n.profile-info h3 {\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: #1a365d;\n  margin-bottom: 1rem;\n}\n\n.info-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 1rem;\n}\n\n.info-item {\n  display: flex;\n  flex-direction: column;\n  gap: 0.25rem;\n}\n\n.info-item label {\n  font-size: 0.875rem;\n  font-weight: 500;\n  color: #718096;\n}\n\n.info-item span {\n  color: #2d3748;\n}\n\n.update-profile-btn {\n  background: #3182ce;\n  color: white;\n  border: none;\n  padding: 0.75rem 1.5rem;\n  border-radius: 0.5rem;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  transition: all 0.2s;\n}\n\n.update-profile-btn:hover {\n  background: #2c5aa0;\n}\n\n.form-section {\n  margin-bottom: 2rem;\n}\n\n.form-section h3 {\n  font-size: 1.125rem;\n  font-weight: 600;\n  color: #1a365d;\n  margin-bottom: 1rem;\n}\n\n.form-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 1rem;\n}\n\n.form-group {\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n}\n\n.form-group label {\n  font-weight: 500;\n  color: #2d3748;\n  font-size: 0.875rem;\n}\n\n.form-group input,\n.form-group select,\n.form-group textarea {\n  padding: 0.75rem;\n  border: 1px solid #e2e8f0;\n  border-radius: 0.5rem;\n  font-size: 0.875rem;\n  transition: all 0.2s;\n}\n\n.form-group input:focus,\n.form-group select:focus,\n.form-group textarea:focus {\n  outline: none;\n  border-color: #3182ce;\n  box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);\n}\n\n.radio-group {\n  display: flex;\n  flex-direction: column;\n  gap: 0.75rem;\n}\n\n.radio-option {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  cursor: pointer;\n  padding: 0.5rem;\n  border-radius: 0.5rem;\n  transition: all 0.2s;\n}\n\n.radio-option:hover {\n  background: #f7fafc;\n}\n\n.radio-option input[type=\"radio\"] {\n  display: none;\n}\n\n.radio-custom {\n  width: 1.25rem;\n  height: 1.25rem;\n  border: 2px solid #e2e8f0;\n  border-radius: 50%;\n  position: relative;\n  transition: all 0.2s;\n}\n\n.radio-option input[type=\"radio\"]:checked + .radio-custom {\n  border-color: #3182ce;\n}\n\n.radio-option input[type=\"radio\"]:checked + .radio-custom::after {\n  content: '';\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  width: 0.5rem;\n  height: 0.5rem;\n  background: #3182ce;\n  border-radius: 50%;\n}\n\n.fee-summary {\n  margin-bottom: 2rem;\n}\n\n.fee-card {\n  background: #f7fafc;\n  border: 1px solid #e2e8f0;\n  border-radius: 0.75rem;\n  padding: 1.5rem;\n}\n\n.fee-card h3 {\n  font-size: 1.125rem;\n  font-weight: 600;\n  color: #1a365d;\n  margin-bottom: 1rem;\n}\n\n.fee-items {\n  display: flex;\n  flex-direction: column;\n  gap: 0.75rem;\n}\n\n.fee-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 0.5rem 0;\n}\n\n.fee-item.total {\n  border-top: 1px solid #e2e8f0;\n  padding-top: 1rem;\n  font-weight: 600;\n  font-size: 1.125rem;\n  color: #1a365d;\n}\n\n.payment-methods {\n  display: grid;\n  gap: 1rem;\n}\n\n.payment-option {\n  border: 2px solid #e2e8f0;\n  border-radius: 0.75rem;\n  padding: 1rem;\n  cursor: pointer;\n  transition: all 0.2s;\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n}\n\n.payment-option:hover {\n  border-color: #cbd5e0;\n}\n\n.payment-option.selected {\n  border-color: #3182ce;\n  background: #ebf8ff;\n}\n\n.payment-icon {\n  width: 3rem;\n  height: 3rem;\n  background: #f7fafc;\n  border-radius: 0.5rem;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 1.25rem;\n  color: #4a5568;\n}\n\n.payment-option.selected .payment-icon {\n  background: #3182ce;\n  color: white;\n}\n\n.payment-info {\n  flex: 1;\n}\n\n.payment-info h4 {\n  font-weight: 600;\n  color: #1a365d;\n  margin-bottom: 0.25rem;\n}\n\n.payment-info p {\n  color: #718096;\n  font-size: 0.875rem;\n  margin: 0;\n}\n\n.payment-radio input {\n  width: 1.25rem;\n  height: 1.25rem;\n}\n\n.review-sections {\n  display: flex;\n  flex-direction: column;\n  gap: 2rem;\n  margin-bottom: 2rem;\n}\n\n.review-section {\n  background: #f7fafc;\n  border-radius: 0.75rem;\n  padding: 1.5rem;\n}\n\n.review-section h3 {\n  font-size: 1.125rem;\n  font-weight: 600;\n  color: #1a365d;\n  margin-bottom: 1rem;\n}\n\n.review-grid {\n  display: grid;\n  gap: 1rem;\n}\n\n.review-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  padding: 0.5rem 0;\n  border-bottom: 1px solid #e2e8f0;\n}\n\n.review-item:last-child {\n  border-bottom: none;\n}\n\n.review-item label {\n  font-weight: 500;\n  color: #4a5568;\n  min-width: 120px;\n}\n\n.review-item span {\n  color: #2d3748;\n  text-align: right;\n  flex: 1;\n}\n\n.review-item .amount {\n  font-weight: 600;\n  color: #38a169;\n  font-size: 1.125rem;\n}\n\n.terms-section {\n  margin-bottom: 2rem;\n  padding: 1rem;\n  background: #fffaf0;\n  border: 1px solid #fed7aa;\n  border-radius: 0.5rem;\n}\n\n.checkbox-option {\n  display: flex;\n  align-items: flex-start;\n  gap: 0.75rem;\n  cursor: pointer;\n  line-height: 1.5;\n}\n\n.checkbox-option input[type=\"checkbox\"] {\n  display: none;\n}\n\n.checkbox-custom {\n  width: 1.25rem;\n  height: 1.25rem;\n  border: 2px solid #e2e8f0;\n  border-radius: 0.25rem;\n  position: relative;\n  flex-shrink: 0;\n  margin-top: 0.125rem;\n  transition: all 0.2s;\n}\n\n.checkbox-option input[type=\"checkbox\"]:checked + .checkbox-custom {\n  border-color: #3182ce;\n  background: #3182ce;\n}\n\n.checkbox-option input[type=\"checkbox\"]:checked + .checkbox-custom::after {\n  content: '✓';\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  color: white;\n  font-size: 0.875rem;\n  font-weight: bold;\n}\n\n.checkbox-option a {\n  color: #3182ce;\n  text-decoration: underline;\n}\n\n.form-actions {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  gap: 1rem;\n  padding-top: 2rem;\n  border-top: 1px solid #e2e8f0;\n}\n\n.btn-secondary,\n.btn-primary,\n.btn-submit {\n  padding: 0.75rem 2rem;\n  border-radius: 0.5rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  border: none;\n}\n\n.btn-secondary {\n  background: #e2e8f0;\n  color: #4a5568;\n}\n\n.btn-secondary:hover {\n  background: #cbd5e0;\n}\n\n.btn-primary {\n  background: #3182ce;\n  color: white;\n}\n\n.btn-primary:hover:not(:disabled) {\n  background: #2c5aa0;\n}\n\n.btn-submit {\n  background: #38a169;\n  color: white;\n}\n\n.btn-submit:hover:not(:disabled) {\n  background: #2f855a;\n}\n\n.btn-primary:disabled,\n.btn-submit:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n}\n\n@media (max-width: 768px) {\n  .barangay-clearance-request {\n    padding: 1rem;\n  }\n\n  .header-content {\n    flex-direction: column;\n    align-items: stretch;\n  }\n\n  .progress-steps {\n    flex-wrap: wrap;\n    gap: 1rem;\n  }\n\n  .progress-steps::before {\n    display: none;\n  }\n\n  .form-container {\n    padding: 1.5rem;\n  }\n\n  .form-grid {\n    grid-template-columns: 1fr;\n  }\n\n  .info-grid {\n    grid-template-columns: 1fr;\n  }\n\n  .profile-card {\n    flex-direction: column;\n    gap: 1rem;\n  }\n\n  .form-actions {\n    flex-direction: column;\n    align-items: stretch;\n  }\n\n  .review-item {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 0.25rem;\n  }\n\n  .review-item span {\n    text-align: left;\n  }\n}\n</style>\n"], "mappings": ";;;;AA6aA,OAAOA,sBAAqB,MAAO,mCAAmC;AACtE,OAAOC,iBAAgB,MAAO,8BAA8B;AAE5D,eAAe;EACbC,IAAI,EAAE,0BAA0B;EAChCC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,WAAW,EAAE,CAAC;MACdC,UAAU,EAAE,KAAK;MACjBC,iBAAiB,EAAE,EAAE;MACrBC,cAAc,EAAE,EAAE;MAClBC,OAAO,EAAE,KAAK;MACdC,QAAQ,EAAE,KAAK;MACfC,QAAQ,EAAE;QACRC,gBAAgB,EAAE,CAAC;QAAE;QACrBC,mBAAmB,EAAE,EAAE;QACvBC,eAAe,EAAE,EAAE;QACnBC,sBAAsB,EAAE,EAAE;QAC1BC,8BAA8B,EAAE,EAAE;QAClCC,uBAAuB,EAAE,EAAE;QAC3BC,yBAAyB,EAAE,EAAE;QAC7BC,iBAAiB,EAAE,KAAK;QACxBC,qBAAqB,EAAE,EAAE;QACzBC,mBAAmB,EAAE,IAAI;QACzBC,gBAAgB,EAAE,EAAE;QACpBC,iBAAiB,EAAE,EAAE;QACrBC,cAAc,EAAE;MAClB;IACF,CAAC;EACH,CAAC;EACDC,QAAQ,EAAE;IACRC,UAAUA,CAAA,EAAG;MACX,OAAOxB,iBAAiB,CAACyB,cAAc,CAAC,CAAC;IAC3C;EACF,CAAC;EACD,MAAMC,OAAOA,CAAA,EAAG;IACd,MAAM,IAAI,CAACC,YAAY,CAAC,CAAC;EAC3B,CAAC;EACDC,OAAO,EAAE;IACP,MAAMD,YAAYA,CAAA,EAAG;MACnB,IAAI;QACF,MAAM,CAACE,eAAe,EAAEC,eAAe,IAAI,MAAMC,OAAO,CAACC,GAAG,CAAC,CAC3DjC,sBAAsB,CAACkC,oBAAoB,CAAC,CAAC,EAC7ClC,sBAAsB,CAACmC,iBAAiB,CAAC,EAC1C,CAAC;QAEF,IAAI,CAAC7B,iBAAgB,GAAIwB,eAAe,CAAC3B,IAAG,IAAK,EAAE;QACnD,IAAI,CAACI,cAAa,GAAIwB,eAAe,CAAC5B,IAAG,IAAK,EAAE;MAElD,EAAE,OAAOiC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChD,IAAI,CAACE,MAAM,EAAEF,KAAK,CAAC,0BAA0B,CAAC;MAChD;IACF,CAAC;IAEDG,WAAWA,CAAA,EAAG;MACZ,MAAMC,OAAM,GAAI,IAAI,CAACf,UAAU,EAAEe,OAAO;MACxC,IAAI,CAACA,OAAO,EAAE,OAAO,KAAK;MAC1B,OAAO,GAAGA,OAAO,CAACC,UAAS,IAAK,EAAE,IAAID,OAAO,CAACE,WAAU,IAAK,EAAE,IAAIF,OAAO,CAACG,SAAQ,IAAK,EAAE,EAAE,CAACC,IAAI,CAAC,CAAC;IACrG,CAAC;IAEDC,cAAcA,CAAA,EAAG;MACf,MAAML,OAAM,GAAI,IAAI,CAACf,UAAU,EAAEe,OAAO;MACxC,IAAI,CAACA,OAAO,EAAE,OAAO,cAAc;MAEnC,MAAMM,KAAI,GAAI,CACZN,OAAO,CAACO,YAAY,EACpBP,OAAO,CAACQ,MAAM,EACdR,OAAO,CAACS,QAAQ,EAChBT,OAAO,CAACU,IAAI,EACZV,OAAO,CAACW,QAAO,CAChB,CAACC,MAAM,CAACC,OAAO,CAAC;MAEjB,OAAOP,KAAK,CAACQ,MAAK,GAAI,IAAIR,KAAK,CAACS,IAAI,CAAC,IAAI,IAAI,cAAc;IAC7D,CAAC;IAEDC,UAAUA,CAACC,UAAU,EAAE;MACrB,IAAI,CAACA,UAAU,EAAE,OAAO,cAAc;MACtC,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,CAAC;IAClD,CAAC;IAEDC,cAAcA,CAACC,MAAM,EAAE;MACrB,OAAOC,UAAU,CAACD,MAAM,CAAC,CAACE,OAAO,CAAC,CAAC,CAAC;IACtC,CAAC;IAEDC,oBAAoBA,CAAA,EAAG;MACrB,QAAQ,IAAI,CAAC5D,WAAW;QACtB,KAAK,CAAC;UACJ,OAAO,IAAI,CAACM,QAAQ,CAACI,sBAAqB,IACnC,IAAI,CAACJ,QAAQ,CAACK,8BAA6B,IAC3C,IAAI,CAACL,QAAQ,CAACM,uBAAuB;QAC9C,KAAK,CAAC;UACJ,OAAO,IAAI,CAACN,QAAQ,CAACE,mBAAkB,IAChC,IAAI,CAACF,QAAQ,CAACG,eAAc,IAC5B,IAAI,CAACH,QAAQ,CAACQ,iBAAgB,KAAM,IAAI;QACjD,KAAK,CAAC;UACJ,OAAO,IAAI,CAACR,QAAQ,CAACY,iBAAiB;QACxC;UACE,OAAO,IAAI;MACf;IACF,CAAC;IAED2C,QAAQA,CAAA,EAAG;MACT,IAAI,IAAI,CAACD,oBAAoB,CAAC,KAAK,IAAI,CAAC5D,WAAU,GAAI,CAAC,EAAE;QACvD,IAAI,CAACA,WAAW,EAAE;MACpB;IACF,CAAC;IAED8D,YAAYA,CAAA,EAAG;MACb,IAAI,IAAI,CAAC9D,WAAU,GAAI,CAAC,EAAE;QACxB,IAAI,CAACA,WAAW,EAAE;MACpB;IACF,CAAC;IAED+D,eAAeA,CAAA,EAAG;MAChB;IAAA,CACD;IAEDC,mBAAmBA,CAACC,QAAQ,EAAE;MAC5B,IAAI,CAAC3D,QAAQ,CAACY,iBAAgB,GAAI+C,QAAQ;IAC5C,CAAC;IAEDC,cAAcA,CAACC,UAAU,EAAE;MACzB,MAAMC,KAAI,GAAI;QACZ,MAAM,EAAE,mBAAmB;QAC3B,eAAe,EAAE,oBAAoB;QACrC,gBAAgB,EAAE,mBAAmB;QACrC,kBAAkB,EAAE,mBAAmB;QACvC,kBAAkB,EAAE;MACtB,CAAC;MACD,OAAOA,KAAK,CAACD,UAAU,KAAK,oBAAoB;IAClD,CAAC;IAEDE,sBAAsBA,CAAA,EAAG;MACvB,MAAMC,QAAO,GAAI,IAAI,CAACpE,iBAAiB,CAACqE,IAAI,CAACC,CAAA,IAAKA,CAAC,CAACC,EAAC,KAAM,IAAI,CAACnE,QAAQ,CAACE,mBAAmB,CAAC;MAC7F,OAAO8D,QAAQ,EAAEI,aAAY,IAAK,EAAE;IACtC,CAAC;IAEDC,oBAAoBA,CAAA,EAAG;MACrB,MAAMC,MAAK,GAAI,IAAI,CAACzE,cAAc,CAACoE,IAAI,CAACM,CAAA,IAAKA,CAAC,CAACJ,EAAC,KAAM,IAAI,CAACnE,QAAQ,CAACY,iBAAiB,CAAC;MACtF,OAAO0D,MAAM,EAAEE,WAAU,IAAK,EAAE;IAClC,CAAC;IAED,MAAMC,YAAYA,CAAA,EAAG;MACnB,IAAI,CAAC,IAAI,CAACzE,QAAQ,CAACa,cAAc,EAAE;MAEnC,IAAI;QACF,IAAI,CAAClB,UAAS,GAAI,IAAI;;QAEtB;QACA,MAAM+E,WAAU,GAAI;UAClBzE,gBAAgB,EAAE0E,QAAQ,CAAC,IAAI,CAAC3E,QAAQ,CAACC,gBAAgB,CAAC;UAC1DC,mBAAmB,EAAEyE,QAAQ,CAAC,IAAI,CAAC3E,QAAQ,CAACE,mBAAmB,CAAC;UAChEC,eAAe,EAAE,IAAI,CAACH,QAAQ,CAACG,eAAc,IAAK,IAAI,CAACH,QAAQ,CAACG,eAAe,CAACyC,MAAK,IAAK,EAAC,GACvF,IAAI,CAAC5C,QAAQ,CAACG,eAAc,GAC5B,kDAAkD;UACtDS,iBAAiB,EAAE+D,QAAQ,CAAC,IAAI,CAAC3E,QAAQ,CAACY,iBAAiB,CAAC;UAC5DgE,eAAe,EAAE,QAAQ;UAAE;UAC3BC,QAAQ,EAAE,QAAQ;UAClB;UACAzE,sBAAsB,EAAE,IAAI,CAACJ,QAAQ,CAACI,sBAAsB;UAC5DC,8BAA8B,EAAE,IAAI,CAACL,QAAQ,CAACK,8BAA8B;UAC5EC,uBAAuB,EAAE,IAAI,CAACN,QAAQ,CAACM,uBAAuB;UAC9DC,yBAAyB,EAAE,IAAI,CAACP,QAAQ,CAACO,yBAAyB;UAClEC,iBAAiB,EAAEmC,OAAO,CAAC,IAAI,CAAC3C,QAAQ,CAACQ,iBAAiB,CAAC;UAC3DC,qBAAqB,EAAE,IAAI,CAACT,QAAQ,CAACS,qBAAoB,IAAK,EAAE;UAChEC,mBAAmB,EAAE,IAAI,CAACV,QAAQ,CAACU,mBAAmB;UACtDC,gBAAgB,EAAE,IAAI,CAACX,QAAQ,CAACW,gBAAe,IAAK,EAAE;UACtDmE,SAAS,EAAE,IAAI,CAAC/E;QAClB,CAAC;QAED4B,OAAO,CAACoD,GAAG,CAAC,0BAA0B,EAAEL,WAAW,CAAC;QAEpD,MAAMM,QAAO,GAAI,MAAM1F,sBAAsB,CAAC2F,aAAa,CAACP,WAAW,CAAC;QAExE,IAAI,CAAC9C,MAAM,EAAEsD,OAAO,CAAC,iCAAiC,CAAC;QACvD,IAAI,CAACC,OAAO,CAACC,IAAI,CAAC;UAChB5F,IAAI,EAAE,gBAAgB;UACtB6F,MAAM,EAAE;YAAElB,EAAE,EAAEa,QAAQ,CAACvF,IAAI,CAAC0E;UAAG;QACjC,CAAC,CAAC;MAEJ,EAAE,OAAOzC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjDC,OAAO,CAACD,KAAK,CAAC,gBAAgB,EAAE;UAC9B4D,MAAM,EAAE5D,KAAK,CAACsD,QAAQ,EAAEM,MAAM;UAC9B7F,IAAI,EAAEiC,KAAK,CAACsD,QAAQ,EAAEvF,IAAI;UAC1B8F,OAAO,EAAE7D,KAAK,CAAC6D;QACjB,CAAC,CAAC;QAEF,IAAIC,YAAW,GAAI,0BAA0B;QAC7C,IAAI9D,KAAK,CAACsD,QAAQ,EAAEvF,IAAI,EAAE8F,OAAO,EAAE;UACjCC,YAAW,GAAI9D,KAAK,CAACsD,QAAQ,CAACvF,IAAI,CAAC8F,OAAO;QAC5C,OAAO,IAAI7D,KAAK,CAACsD,QAAQ,EAAEvF,IAAI,EAAEgG,MAAM,EAAE;UACvCD,YAAW,GAAI9D,KAAK,CAACsD,QAAQ,CAACvF,IAAI,CAACgG,MAAM,CAACC,GAAG,CAACC,CAAA,IAAKA,CAAC,CAACC,GAAG,CAAC,CAAC/C,IAAI,CAAC,IAAI,CAAC;QACtE,OAAO,IAAInB,KAAK,CAAC6D,OAAO,EAAE;UACxBC,YAAW,GAAI9D,KAAK,CAAC6D,OAAO;QAC9B;QAEA,IAAI,CAAC3D,MAAM,EAAEF,KAAK,CAAC8D,YAAY,CAAC;MAClC,UAAU;QACR,IAAI,CAAC7F,UAAS,GAAI,KAAK;MACzB;IACF,CAAC;IAEDkG,MAAMA,CAAA,EAAG;MACP,IAAI,CAACV,OAAO,CAACC,IAAI,CAAC;QAAE5F,IAAI,EAAE;MAAqB,CAAC,CAAC;IACnD,CAAC;IAEDsG,aAAaA,CAAA,EAAG;MACd;MACAnE,OAAO,CAACoD,GAAG,CAAC,gBAAgB,CAAC;IAC/B,CAAC;IAEDgB,SAASA,CAAA,EAAG;MACV;MACApE,OAAO,CAACoD,GAAG,CAAC,YAAY,CAAC;IAC3B;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}