{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.set.difference.v2.js\";\nimport \"core-js/modules/es.set.intersection.v2.js\";\nimport \"core-js/modules/es.set.is-disjoint-from.v2.js\";\nimport \"core-js/modules/es.set.is-subset-of.v2.js\";\nimport \"core-js/modules/es.set.is-superset-of.v2.js\";\nimport \"core-js/modules/es.set.symmetric-difference.v2.js\";\nimport \"core-js/modules/es.set.union.v2.js\";\nimport axios from 'axios';\nclass NotificationService {\n  constructor() {\n    this.eventSource = null;\n    this.listeners = new Map();\n    this.isConnected = false;\n    this.reconnectAttempts = 0;\n    this.maxReconnectAttempts = 5;\n    this.reconnectDelay = 1000; // Start with 1 second\n    this.maxReconnectDelay = 30000; // Max 30 seconds\n    this.baseURL = process.env.VUE_APP_API_URL || 'http://localhost:3000/api';\n    this.connectionRefs = 0; // Track how many components are using the connection\n    this.currentUserType = null; // Track current connection type\n    this.isConnecting = false; // Prevent multiple simultaneous connection attempts\n  }\n\n  /**\n   * Initialize connection (simplified)\n   */\n  init(userType = 'admin') {\n    console.log('🚀 Initializing notification service');\n    if (!this.eventSource) {\n      this.connect(userType);\n    }\n    return Promise.resolve();\n  }\n\n  /**\n   * Cleanup (simplified)\n   */\n  cleanup() {\n    console.log('🧹 Notification service cleanup');\n    // Don't disconnect - let connection persist\n  }\n\n  /**\n   * Connect to SSE stream - Enhanced version with proper endpoint handling\n   */\n  connect(userType = 'admin') {\n    // Don't create multiple connections\n    if (this.eventSource) {\n      console.log('SSE connection already exists');\n      return Promise.resolve();\n    }\n    console.log('🔗 Creating SSE connection for:', userType);\n    this.currentUserType = userType;\n    const token = userType === 'admin' ? localStorage.getItem('adminToken') : localStorage.getItem('clientToken');\n    if (!token) {\n      console.error('No authentication token found for', userType);\n      return Promise.reject(new Error(`No ${userType} token found`));\n    }\n\n    // Use unified endpoint for both admin and client\n    const url = `${this.baseURL}/notifications/stream?token=${encodeURIComponent(token)}`;\n    console.log('🔗 SSE URL:', url.replace(/token=[^&]+/, 'token=***'));\n    this.eventSource = new EventSource(url);\n\n    // CRITICAL: Store multiple references to prevent garbage collection\n    window.__sseConnection = this.eventSource;\n    window.__notificationEventSource = this.eventSource;\n    this.__eventSourceRef = this.eventSource;\n    this.eventSource.onopen = () => {\n      console.log('✅ SSE Connected successfully');\n      this.isConnected = true;\n      this.emit('connected');\n    };\n    this.eventSource.onmessage = event => {\n      try {\n        const data = JSON.parse(event.data);\n        console.log('📨 SSE Message:', data);\n        this.handleNotification(data);\n      } catch (error) {\n        console.error('SSE parse error:', error);\n      }\n    };\n    this.eventSource.onerror = error => {\n      console.error('❌ SSE Error:', error);\n      console.error('ReadyState:', this.eventSource?.readyState);\n      this.isConnected = false;\n\n      // Don't auto-reconnect to prevent loops\n      console.log('SSE connection failed - not auto-reconnecting');\n    };\n    return Promise.resolve();\n  }\n\n  /**\n   * Disconnect from SSE stream\n   */\n  disconnect() {\n    console.log('🔌 disconnect() called');\n    console.log('📊 Connection refs:', this.connectionRefs);\n    console.log('📊 Stack trace:', new Error().stack);\n    if (this.eventSource) {\n      console.log('🔌 Closing EventSource connection');\n      this.eventSource.close();\n      this.eventSource = null;\n      this.isConnected = false;\n      this.connectionRefs = 0; // Reset refs when manually disconnecting\n\n      // Clear global reference\n      if (window.__notificationEventSource) {\n        delete window.__notificationEventSource;\n      }\n      console.log('Disconnected from notification stream');\n      this.emit('disconnected');\n    }\n  }\n\n  /**\n   * Schedule reconnection attempt\n   */\n  scheduleReconnect() {\n    console.log('🚫 Auto-reconnect disabled to prevent connection loops');\n    // Disabled to prevent connection issues during debugging\n  }\n\n  /**\n   * Handle incoming notification\n   */\n  handleNotification(notification) {\n    console.log('📢 Received notification:', notification);\n\n    // Emit to specific type listeners\n    this.emit(notification.type, notification);\n\n    // Emit to general notification listeners\n    this.emit('notification', notification);\n\n    // Show browser notification if permission granted\n    this.showBrowserNotification(notification);\n  }\n\n  /**\n   * Show browser notification\n   */\n  showBrowserNotification(notification) {\n    if ('Notification' in window && Notification.permission === 'granted') {\n      const options = {\n        body: notification.message,\n        icon: '/favicon.ico',\n        badge: '/favicon.ico',\n        tag: `notification-${notification.id}`,\n        requireInteraction: notification.priority === 'high' || notification.priority === 'urgent'\n      };\n      const browserNotification = new Notification(notification.title, options);\n      browserNotification.onclick = () => {\n        window.focus();\n        this.emit('notification_click', notification);\n        browserNotification.close();\n      };\n\n      // Auto close after 5 seconds for normal priority\n      if (notification.priority !== 'high' && notification.priority !== 'urgent') {\n        setTimeout(() => {\n          browserNotification.close();\n        }, 5000);\n      }\n    }\n  }\n\n  /**\n   * Request browser notification permission\n   */\n  async requestNotificationPermission() {\n    if ('Notification' in window) {\n      const permission = await Notification.requestPermission();\n      return permission === 'granted';\n    }\n    return false;\n  }\n\n  /**\n   * Subscribe to notification events\n   */\n  on(event, callback) {\n    if (!this.listeners.has(event)) {\n      this.listeners.set(event, new Set());\n    }\n    this.listeners.get(event).add(callback);\n  }\n\n  /**\n   * Unsubscribe from notification events\n   */\n  off(event, callback) {\n    if (this.listeners.has(event)) {\n      this.listeners.get(event).delete(callback);\n    }\n  }\n\n  /**\n   * Emit event to listeners\n   */\n  emit(event, data = null) {\n    if (this.listeners.has(event)) {\n      this.listeners.get(event).forEach(callback => {\n        try {\n          callback(data);\n        } catch (error) {\n          console.error(`Error in notification listener for ${event}:`, error);\n        }\n      });\n    }\n  }\n\n  /**\n   * Get user notifications\n   */\n  async getNotifications(page = 1, limit = 20, unreadOnly = false) {\n    try {\n      const isAdmin = !!localStorage.getItem('adminToken');\n      const token = isAdmin ? localStorage.getItem('adminToken') : localStorage.getItem('clientToken');\n      if (!token) {\n        throw new Error('No authentication token found');\n      }\n\n      // Use unified endpoint for both admin and client\n      const response = await axios.get(`${this.baseURL}/notifications`, {\n        params: {\n          page,\n          limit,\n          unread_only: unreadOnly\n        },\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Failed to get notifications:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get unread notification count\n   */\n  async getUnreadCount() {\n    try {\n      const isAdmin = !!localStorage.getItem('adminToken');\n      const token = isAdmin ? localStorage.getItem('adminToken') : localStorage.getItem('clientToken');\n      if (!token) {\n        throw new Error('No authentication token found');\n      }\n\n      // Use unified endpoint for both admin and client\n      const response = await axios.get(`${this.baseURL}/notifications/unread-count`, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      return response.data.data.count;\n    } catch (error) {\n      console.error('Failed to get unread count:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Mark notification as read\n   */\n  async markAsRead(notificationId) {\n    try {\n      const isAdmin = !!localStorage.getItem('adminToken');\n      const token = isAdmin ? localStorage.getItem('adminToken') : localStorage.getItem('clientToken');\n      if (!token) {\n        throw new Error('No authentication token found');\n      }\n\n      // Use unified endpoint for both admin and client\n      const response = await axios.put(`${this.baseURL}/notifications/${notificationId}/read`, {}, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Failed to mark notification as read:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Mark all notifications as read\n   */\n  async markAllAsRead() {\n    try {\n      const isAdmin = !!localStorage.getItem('adminToken');\n      const token = isAdmin ? localStorage.getItem('adminToken') : localStorage.getItem('clientToken');\n      if (!token) {\n        throw new Error('No authentication token found');\n      }\n\n      // Use unified endpoint for both admin and client\n      const response = await axios.put(`${this.baseURL}/notifications/mark-all-read`, {}, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Failed to mark all notifications as read:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Send test notification (admin only)\n   */\n  async sendTestNotification(data) {\n    try {\n      const token = localStorage.getItem('adminToken');\n      const response = await axios.post(`${this.baseURL}/notifications/test`, data, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Failed to send test notification:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get notification statistics (admin only)\n   */\n  async getStatistics() {\n    try {\n      const token = localStorage.getItem('adminToken');\n      const response = await axios.get(`${this.baseURL}/notifications/statistics`, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Failed to get notification statistics:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Test SSE connection (for debugging)\n   */\n  async testConnection() {\n    console.log('🧪 Testing SSE connection...');\n    try {\n      // Clear any existing connection\n      if (this.eventSource) {\n        console.log('🧪 Clearing existing connection');\n        this.eventSource.close();\n        this.eventSource = null;\n        this.isConnected = false;\n        this.isConnecting = false;\n      }\n\n      // Reset state\n      this.connectionRefs = 1;\n\n      // Test connection\n      await this.connect('admin');\n      console.log('🧪 Test connection established');\n\n      // Keep connection alive for 10 seconds\n      setTimeout(() => {\n        console.log('🧪 Test completed, keeping connection');\n      }, 10000);\n    } catch (error) {\n      console.error('🧪 Test connection failed:', error);\n    }\n  }\n\n  /**\n   * Clean up old notifications (admin only)\n   */\n  async cleanupOldNotifications(days = 90) {\n    try {\n      const token = localStorage.getItem('adminToken');\n      const response = await axios.delete(`${this.baseURL}/notifications/cleanup`, {\n        params: {\n          days\n        },\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Failed to cleanup old notifications:', error);\n      throw error;\n    }\n  }\n}\n\n// Create singleton instance\nconst notificationService = new NotificationService();\n\n// Make available globally for debugging\nwindow.__notificationService = notificationService;\n\n// Add global test function\nwindow.testSSE = () => {\n  console.log('🧪 Testing SSE connection manually...');\n  notificationService.connect('admin');\n};\nexport default notificationService;", "map": {"version": 3, "names": ["axios", "NotificationService", "constructor", "eventSource", "listeners", "Map", "isConnected", "reconnectAttempts", "maxReconnectAttempts", "reconnectDelay", "maxReconnectDelay", "baseURL", "process", "env", "VUE_APP_API_URL", "connectionRefs", "currentUserType", "isConnecting", "init", "userType", "console", "log", "connect", "Promise", "resolve", "cleanup", "token", "localStorage", "getItem", "error", "reject", "Error", "url", "encodeURIComponent", "replace", "EventSource", "window", "__sseConnection", "__notificationEventSource", "__eventSourceRef", "onopen", "emit", "onmessage", "event", "data", "JSON", "parse", "handleNotification", "onerror", "readyState", "disconnect", "stack", "close", "scheduleReconnect", "notification", "type", "showBrowserNotification", "Notification", "permission", "options", "body", "message", "icon", "badge", "tag", "id", "requireInteraction", "priority", "browserNotification", "title", "onclick", "focus", "setTimeout", "requestNotificationPermission", "requestPermission", "on", "callback", "has", "set", "Set", "get", "add", "off", "delete", "for<PERSON>ach", "getNotifications", "page", "limit", "unreadOnly", "isAdmin", "response", "params", "unread_only", "headers", "getUnreadCount", "count", "mark<PERSON><PERSON><PERSON>", "notificationId", "put", "markAllAsRead", "sendTestNotification", "post", "getStatistics", "testConnection", "cleanupOldNotifications", "days", "notificationService", "__notificationService", "testSSE"], "sources": ["D:/rhai_front_and_back/BOSFDR/src/services/notificationService.js"], "sourcesContent": ["import axios from 'axios';\n\nclass NotificationService {\n  constructor() {\n    this.eventSource = null;\n    this.listeners = new Map();\n    this.isConnected = false;\n    this.reconnectAttempts = 0;\n    this.maxReconnectAttempts = 5;\n    this.reconnectDelay = 1000; // Start with 1 second\n    this.maxReconnectDelay = 30000; // Max 30 seconds\n    this.baseURL = process.env.VUE_APP_API_URL || 'http://localhost:3000/api';\n    this.connectionRefs = 0; // Track how many components are using the connection\n    this.currentUserType = null; // Track current connection type\n    this.isConnecting = false; // Prevent multiple simultaneous connection attempts\n  }\n\n  /**\n   * Initialize connection (simplified)\n   */\n  init(userType = 'admin') {\n    console.log('🚀 Initializing notification service');\n    if (!this.eventSource) {\n      this.connect(userType);\n    }\n    return Promise.resolve();\n  }\n\n  /**\n   * Cleanup (simplified)\n   */\n  cleanup() {\n    console.log('🧹 Notification service cleanup');\n    // Don't disconnect - let connection persist\n  }\n\n  /**\n   * Connect to SSE stream - Enhanced version with proper endpoint handling\n   */\n  connect(userType = 'admin') {\n    // Don't create multiple connections\n    if (this.eventSource) {\n      console.log('SSE connection already exists');\n      return Promise.resolve();\n    }\n\n    console.log('🔗 Creating SSE connection for:', userType);\n    this.currentUserType = userType;\n\n    const token = userType === 'admin'\n      ? localStorage.getItem('adminToken')\n      : localStorage.getItem('clientToken');\n\n    if (!token) {\n      console.error('No authentication token found for', userType);\n      return Promise.reject(new Error(`No ${userType} token found`));\n    }\n\n    // Use unified endpoint for both admin and client\n    const url = `${this.baseURL}/notifications/stream?token=${encodeURIComponent(token)}`;\n    console.log('🔗 SSE URL:', url.replace(/token=[^&]+/, 'token=***'));\n\n    this.eventSource = new EventSource(url);\n\n    // CRITICAL: Store multiple references to prevent garbage collection\n    window.__sseConnection = this.eventSource;\n    window.__notificationEventSource = this.eventSource;\n    this.__eventSourceRef = this.eventSource;\n\n    this.eventSource.onopen = () => {\n      console.log('✅ SSE Connected successfully');\n      this.isConnected = true;\n      this.emit('connected');\n    };\n\n    this.eventSource.onmessage = (event) => {\n      try {\n        const data = JSON.parse(event.data);\n        console.log('📨 SSE Message:', data);\n        this.handleNotification(data);\n      } catch (error) {\n        console.error('SSE parse error:', error);\n      }\n    };\n\n    this.eventSource.onerror = (error) => {\n      console.error('❌ SSE Error:', error);\n      console.error('ReadyState:', this.eventSource?.readyState);\n      this.isConnected = false;\n\n      // Don't auto-reconnect to prevent loops\n      console.log('SSE connection failed - not auto-reconnecting');\n    };\n\n    return Promise.resolve();\n  }\n\n  /**\n   * Disconnect from SSE stream\n   */\n  disconnect() {\n    console.log('🔌 disconnect() called');\n    console.log('📊 Connection refs:', this.connectionRefs);\n    console.log('📊 Stack trace:', new Error().stack);\n\n    if (this.eventSource) {\n      console.log('🔌 Closing EventSource connection');\n      this.eventSource.close();\n      this.eventSource = null;\n      this.isConnected = false;\n      this.connectionRefs = 0; // Reset refs when manually disconnecting\n\n      // Clear global reference\n      if (window.__notificationEventSource) {\n        delete window.__notificationEventSource;\n      }\n\n      console.log('Disconnected from notification stream');\n      this.emit('disconnected');\n    }\n  }\n\n  /**\n   * Schedule reconnection attempt\n   */\n  scheduleReconnect() {\n    console.log('🚫 Auto-reconnect disabled to prevent connection loops');\n    // Disabled to prevent connection issues during debugging\n  }\n\n  /**\n   * Handle incoming notification\n   */\n  handleNotification(notification) {\n    console.log('📢 Received notification:', notification);\n    \n    // Emit to specific type listeners\n    this.emit(notification.type, notification);\n    \n    // Emit to general notification listeners\n    this.emit('notification', notification);\n    \n    // Show browser notification if permission granted\n    this.showBrowserNotification(notification);\n  }\n\n  /**\n   * Show browser notification\n   */\n  showBrowserNotification(notification) {\n    if ('Notification' in window && Notification.permission === 'granted') {\n      const options = {\n        body: notification.message,\n        icon: '/favicon.ico',\n        badge: '/favicon.ico',\n        tag: `notification-${notification.id}`,\n        requireInteraction: notification.priority === 'high' || notification.priority === 'urgent'\n      };\n\n      const browserNotification = new Notification(notification.title, options);\n      \n      browserNotification.onclick = () => {\n        window.focus();\n        this.emit('notification_click', notification);\n        browserNotification.close();\n      };\n\n      // Auto close after 5 seconds for normal priority\n      if (notification.priority !== 'high' && notification.priority !== 'urgent') {\n        setTimeout(() => {\n          browserNotification.close();\n        }, 5000);\n      }\n    }\n  }\n\n  /**\n   * Request browser notification permission\n   */\n  async requestNotificationPermission() {\n    if ('Notification' in window) {\n      const permission = await Notification.requestPermission();\n      return permission === 'granted';\n    }\n    return false;\n  }\n\n  /**\n   * Subscribe to notification events\n   */\n  on(event, callback) {\n    if (!this.listeners.has(event)) {\n      this.listeners.set(event, new Set());\n    }\n    this.listeners.get(event).add(callback);\n  }\n\n  /**\n   * Unsubscribe from notification events\n   */\n  off(event, callback) {\n    if (this.listeners.has(event)) {\n      this.listeners.get(event).delete(callback);\n    }\n  }\n\n  /**\n   * Emit event to listeners\n   */\n  emit(event, data = null) {\n    if (this.listeners.has(event)) {\n      this.listeners.get(event).forEach(callback => {\n        try {\n          callback(data);\n        } catch (error) {\n          console.error(`Error in notification listener for ${event}:`, error);\n        }\n      });\n    }\n  }\n\n  /**\n   * Get user notifications\n   */\n  async getNotifications(page = 1, limit = 20, unreadOnly = false) {\n    try {\n      const isAdmin = !!localStorage.getItem('adminToken');\n      const token = isAdmin\n        ? localStorage.getItem('adminToken')\n        : localStorage.getItem('clientToken');\n\n      if (!token) {\n        throw new Error('No authentication token found');\n      }\n\n      // Use unified endpoint for both admin and client\n      const response = await axios.get(`${this.baseURL}/notifications`, {\n        params: { page, limit, unread_only: unreadOnly },\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Failed to get notifications:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get unread notification count\n   */\n  async getUnreadCount() {\n    try {\n      const isAdmin = !!localStorage.getItem('adminToken');\n      const token = isAdmin\n        ? localStorage.getItem('adminToken')\n        : localStorage.getItem('clientToken');\n\n      if (!token) {\n        throw new Error('No authentication token found');\n      }\n\n      // Use unified endpoint for both admin and client\n      const response = await axios.get(`${this.baseURL}/notifications/unread-count`, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      return response.data.data.count;\n    } catch (error) {\n      console.error('Failed to get unread count:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Mark notification as read\n   */\n  async markAsRead(notificationId) {\n    try {\n      const isAdmin = !!localStorage.getItem('adminToken');\n      const token = isAdmin\n        ? localStorage.getItem('adminToken')\n        : localStorage.getItem('clientToken');\n\n      if (!token) {\n        throw new Error('No authentication token found');\n      }\n\n      // Use unified endpoint for both admin and client\n      const response = await axios.put(`${this.baseURL}/notifications/${notificationId}/read`, {}, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Failed to mark notification as read:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Mark all notifications as read\n   */\n  async markAllAsRead() {\n    try {\n      const isAdmin = !!localStorage.getItem('adminToken');\n      const token = isAdmin\n        ? localStorage.getItem('adminToken')\n        : localStorage.getItem('clientToken');\n\n      if (!token) {\n        throw new Error('No authentication token found');\n      }\n\n      // Use unified endpoint for both admin and client\n      const response = await axios.put(`${this.baseURL}/notifications/mark-all-read`, {}, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Failed to mark all notifications as read:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Send test notification (admin only)\n   */\n  async sendTestNotification(data) {\n    try {\n      const token = localStorage.getItem('adminToken');\n      const response = await axios.post(`${this.baseURL}/notifications/test`, data, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Failed to send test notification:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get notification statistics (admin only)\n   */\n  async getStatistics() {\n    try {\n      const token = localStorage.getItem('adminToken');\n      const response = await axios.get(`${this.baseURL}/notifications/statistics`, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Failed to get notification statistics:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Test SSE connection (for debugging)\n   */\n  async testConnection() {\n    console.log('🧪 Testing SSE connection...');\n\n    try {\n      // Clear any existing connection\n      if (this.eventSource) {\n        console.log('🧪 Clearing existing connection');\n        this.eventSource.close();\n        this.eventSource = null;\n        this.isConnected = false;\n        this.isConnecting = false;\n      }\n\n      // Reset state\n      this.connectionRefs = 1;\n\n      // Test connection\n      await this.connect('admin');\n\n      console.log('🧪 Test connection established');\n\n      // Keep connection alive for 10 seconds\n      setTimeout(() => {\n        console.log('🧪 Test completed, keeping connection');\n      }, 10000);\n\n    } catch (error) {\n      console.error('🧪 Test connection failed:', error);\n    }\n  }\n\n  /**\n   * Clean up old notifications (admin only)\n   */\n  async cleanupOldNotifications(days = 90) {\n    try {\n      const token = localStorage.getItem('adminToken');\n      const response = await axios.delete(`${this.baseURL}/notifications/cleanup`, {\n        params: { days },\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Failed to cleanup old notifications:', error);\n      throw error;\n    }\n  }\n}\n\n// Create singleton instance\nconst notificationService = new NotificationService();\n\n// Make available globally for debugging\nwindow.__notificationService = notificationService;\n\n// Add global test function\nwindow.testSSE = () => {\n  console.log('🧪 Testing SSE connection manually...');\n  notificationService.connect('admin');\n};\n\nexport default notificationService;\n"], "mappings": ";;;;;;;;;AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,mBAAmB,CAAC;EACxBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,WAAW,GAAG,IAAI;IACvB,IAAI,CAACC,SAAS,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC1B,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,iBAAiB,GAAG,CAAC;IAC1B,IAAI,CAACC,oBAAoB,GAAG,CAAC;IAC7B,IAAI,CAACC,cAAc,GAAG,IAAI,CAAC,CAAC;IAC5B,IAAI,CAACC,iBAAiB,GAAG,KAAK,CAAC,CAAC;IAChC,IAAI,CAACC,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,eAAe,IAAI,2BAA2B;IACzE,IAAI,CAACC,cAAc,GAAG,CAAC,CAAC,CAAC;IACzB,IAAI,CAACC,eAAe,GAAG,IAAI,CAAC,CAAC;IAC7B,IAAI,CAACC,YAAY,GAAG,KAAK,CAAC,CAAC;EAC7B;;EAEA;AACF;AACA;EACEC,IAAIA,CAACC,QAAQ,GAAG,OAAO,EAAE;IACvBC,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;IACnD,IAAI,CAAC,IAAI,CAAClB,WAAW,EAAE;MACrB,IAAI,CAACmB,OAAO,CAACH,QAAQ,CAAC;IACxB;IACA,OAAOI,OAAO,CAACC,OAAO,CAAC,CAAC;EAC1B;;EAEA;AACF;AACA;EACEC,OAAOA,CAAA,EAAG;IACRL,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;IAC9C;EACF;;EAEA;AACF;AACA;EACEC,OAAOA,CAACH,QAAQ,GAAG,OAAO,EAAE;IAC1B;IACA,IAAI,IAAI,CAAChB,WAAW,EAAE;MACpBiB,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;MAC5C,OAAOE,OAAO,CAACC,OAAO,CAAC,CAAC;IAC1B;IAEAJ,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEF,QAAQ,CAAC;IACxD,IAAI,CAACH,eAAe,GAAGG,QAAQ;IAE/B,MAAMO,KAAK,GAAGP,QAAQ,KAAK,OAAO,GAC9BQ,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,GAClCD,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;IAEvC,IAAI,CAACF,KAAK,EAAE;MACVN,OAAO,CAACS,KAAK,CAAC,mCAAmC,EAAEV,QAAQ,CAAC;MAC5D,OAAOI,OAAO,CAACO,MAAM,CAAC,IAAIC,KAAK,CAAC,MAAMZ,QAAQ,cAAc,CAAC,CAAC;IAChE;;IAEA;IACA,MAAMa,GAAG,GAAG,GAAG,IAAI,CAACrB,OAAO,+BAA+BsB,kBAAkB,CAACP,KAAK,CAAC,EAAE;IACrFN,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEW,GAAG,CAACE,OAAO,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;IAEnE,IAAI,CAAC/B,WAAW,GAAG,IAAIgC,WAAW,CAACH,GAAG,CAAC;;IAEvC;IACAI,MAAM,CAACC,eAAe,GAAG,IAAI,CAAClC,WAAW;IACzCiC,MAAM,CAACE,yBAAyB,GAAG,IAAI,CAACnC,WAAW;IACnD,IAAI,CAACoC,gBAAgB,GAAG,IAAI,CAACpC,WAAW;IAExC,IAAI,CAACA,WAAW,CAACqC,MAAM,GAAG,MAAM;MAC9BpB,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;MAC3C,IAAI,CAACf,WAAW,GAAG,IAAI;MACvB,IAAI,CAACmC,IAAI,CAAC,WAAW,CAAC;IACxB,CAAC;IAED,IAAI,CAACtC,WAAW,CAACuC,SAAS,GAAIC,KAAK,IAAK;MACtC,IAAI;QACF,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACH,KAAK,CAACC,IAAI,CAAC;QACnCxB,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEuB,IAAI,CAAC;QACpC,IAAI,CAACG,kBAAkB,CAACH,IAAI,CAAC;MAC/B,CAAC,CAAC,OAAOf,KAAK,EAAE;QACdT,OAAO,CAACS,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;MAC1C;IACF,CAAC;IAED,IAAI,CAAC1B,WAAW,CAAC6C,OAAO,GAAInB,KAAK,IAAK;MACpCT,OAAO,CAACS,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACpCT,OAAO,CAACS,KAAK,CAAC,aAAa,EAAE,IAAI,CAAC1B,WAAW,EAAE8C,UAAU,CAAC;MAC1D,IAAI,CAAC3C,WAAW,GAAG,KAAK;;MAExB;MACAc,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;IAC9D,CAAC;IAED,OAAOE,OAAO,CAACC,OAAO,CAAC,CAAC;EAC1B;;EAEA;AACF;AACA;EACE0B,UAAUA,CAAA,EAAG;IACX9B,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;IACrCD,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE,IAAI,CAACN,cAAc,CAAC;IACvDK,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE,IAAIU,KAAK,CAAC,CAAC,CAACoB,KAAK,CAAC;IAEjD,IAAI,IAAI,CAAChD,WAAW,EAAE;MACpBiB,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;MAChD,IAAI,CAAClB,WAAW,CAACiD,KAAK,CAAC,CAAC;MACxB,IAAI,CAACjD,WAAW,GAAG,IAAI;MACvB,IAAI,CAACG,WAAW,GAAG,KAAK;MACxB,IAAI,CAACS,cAAc,GAAG,CAAC,CAAC,CAAC;;MAEzB;MACA,IAAIqB,MAAM,CAACE,yBAAyB,EAAE;QACpC,OAAOF,MAAM,CAACE,yBAAyB;MACzC;MAEAlB,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;MACpD,IAAI,CAACoB,IAAI,CAAC,cAAc,CAAC;IAC3B;EACF;;EAEA;AACF;AACA;EACEY,iBAAiBA,CAAA,EAAG;IAClBjC,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;IACrE;EACF;;EAEA;AACF;AACA;EACE0B,kBAAkBA,CAACO,YAAY,EAAE;IAC/BlC,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEiC,YAAY,CAAC;;IAEtD;IACA,IAAI,CAACb,IAAI,CAACa,YAAY,CAACC,IAAI,EAAED,YAAY,CAAC;;IAE1C;IACA,IAAI,CAACb,IAAI,CAAC,cAAc,EAAEa,YAAY,CAAC;;IAEvC;IACA,IAAI,CAACE,uBAAuB,CAACF,YAAY,CAAC;EAC5C;;EAEA;AACF;AACA;EACEE,uBAAuBA,CAACF,YAAY,EAAE;IACpC,IAAI,cAAc,IAAIlB,MAAM,IAAIqB,YAAY,CAACC,UAAU,KAAK,SAAS,EAAE;MACrE,MAAMC,OAAO,GAAG;QACdC,IAAI,EAAEN,YAAY,CAACO,OAAO;QAC1BC,IAAI,EAAE,cAAc;QACpBC,KAAK,EAAE,cAAc;QACrBC,GAAG,EAAE,gBAAgBV,YAAY,CAACW,EAAE,EAAE;QACtCC,kBAAkB,EAAEZ,YAAY,CAACa,QAAQ,KAAK,MAAM,IAAIb,YAAY,CAACa,QAAQ,KAAK;MACpF,CAAC;MAED,MAAMC,mBAAmB,GAAG,IAAIX,YAAY,CAACH,YAAY,CAACe,KAAK,EAAEV,OAAO,CAAC;MAEzES,mBAAmB,CAACE,OAAO,GAAG,MAAM;QAClClC,MAAM,CAACmC,KAAK,CAAC,CAAC;QACd,IAAI,CAAC9B,IAAI,CAAC,oBAAoB,EAAEa,YAAY,CAAC;QAC7Cc,mBAAmB,CAAChB,KAAK,CAAC,CAAC;MAC7B,CAAC;;MAED;MACA,IAAIE,YAAY,CAACa,QAAQ,KAAK,MAAM,IAAIb,YAAY,CAACa,QAAQ,KAAK,QAAQ,EAAE;QAC1EK,UAAU,CAAC,MAAM;UACfJ,mBAAmB,CAAChB,KAAK,CAAC,CAAC;QAC7B,CAAC,EAAE,IAAI,CAAC;MACV;IACF;EACF;;EAEA;AACF;AACA;EACE,MAAMqB,6BAA6BA,CAAA,EAAG;IACpC,IAAI,cAAc,IAAIrC,MAAM,EAAE;MAC5B,MAAMsB,UAAU,GAAG,MAAMD,YAAY,CAACiB,iBAAiB,CAAC,CAAC;MACzD,OAAOhB,UAAU,KAAK,SAAS;IACjC;IACA,OAAO,KAAK;EACd;;EAEA;AACF;AACA;EACEiB,EAAEA,CAAChC,KAAK,EAAEiC,QAAQ,EAAE;IAClB,IAAI,CAAC,IAAI,CAACxE,SAAS,CAACyE,GAAG,CAAClC,KAAK,CAAC,EAAE;MAC9B,IAAI,CAACvC,SAAS,CAAC0E,GAAG,CAACnC,KAAK,EAAE,IAAIoC,GAAG,CAAC,CAAC,CAAC;IACtC;IACA,IAAI,CAAC3E,SAAS,CAAC4E,GAAG,CAACrC,KAAK,CAAC,CAACsC,GAAG,CAACL,QAAQ,CAAC;EACzC;;EAEA;AACF;AACA;EACEM,GAAGA,CAACvC,KAAK,EAAEiC,QAAQ,EAAE;IACnB,IAAI,IAAI,CAACxE,SAAS,CAACyE,GAAG,CAAClC,KAAK,CAAC,EAAE;MAC7B,IAAI,CAACvC,SAAS,CAAC4E,GAAG,CAACrC,KAAK,CAAC,CAACwC,MAAM,CAACP,QAAQ,CAAC;IAC5C;EACF;;EAEA;AACF;AACA;EACEnC,IAAIA,CAACE,KAAK,EAAEC,IAAI,GAAG,IAAI,EAAE;IACvB,IAAI,IAAI,CAACxC,SAAS,CAACyE,GAAG,CAAClC,KAAK,CAAC,EAAE;MAC7B,IAAI,CAACvC,SAAS,CAAC4E,GAAG,CAACrC,KAAK,CAAC,CAACyC,OAAO,CAACR,QAAQ,IAAI;QAC5C,IAAI;UACFA,QAAQ,CAAChC,IAAI,CAAC;QAChB,CAAC,CAAC,OAAOf,KAAK,EAAE;UACdT,OAAO,CAACS,KAAK,CAAC,sCAAsCc,KAAK,GAAG,EAAEd,KAAK,CAAC;QACtE;MACF,CAAC,CAAC;IACJ;EACF;;EAEA;AACF;AACA;EACE,MAAMwD,gBAAgBA,CAACC,IAAI,GAAG,CAAC,EAAEC,KAAK,GAAG,EAAE,EAAEC,UAAU,GAAG,KAAK,EAAE;IAC/D,IAAI;MACF,MAAMC,OAAO,GAAG,CAAC,CAAC9D,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;MACpD,MAAMF,KAAK,GAAG+D,OAAO,GACjB9D,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,GAClCD,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;MAEvC,IAAI,CAACF,KAAK,EAAE;QACV,MAAM,IAAIK,KAAK,CAAC,+BAA+B,CAAC;MAClD;;MAEA;MACA,MAAM2D,QAAQ,GAAG,MAAM1F,KAAK,CAACgF,GAAG,CAAC,GAAG,IAAI,CAACrE,OAAO,gBAAgB,EAAE;QAChEgF,MAAM,EAAE;UAAEL,IAAI;UAAEC,KAAK;UAAEK,WAAW,EAAEJ;QAAW,CAAC;QAChDK,OAAO,EAAE;UACP,eAAe,EAAE,UAAUnE,KAAK;QAClC;MACF,CAAC,CAAC;MACF,OAAOgE,QAAQ,CAAC9C,IAAI;IACtB,CAAC,CAAC,OAAOf,KAAK,EAAE;MACdT,OAAO,CAACS,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMiE,cAAcA,CAAA,EAAG;IACrB,IAAI;MACF,MAAML,OAAO,GAAG,CAAC,CAAC9D,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;MACpD,MAAMF,KAAK,GAAG+D,OAAO,GACjB9D,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,GAClCD,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;MAEvC,IAAI,CAACF,KAAK,EAAE;QACV,MAAM,IAAIK,KAAK,CAAC,+BAA+B,CAAC;MAClD;;MAEA;MACA,MAAM2D,QAAQ,GAAG,MAAM1F,KAAK,CAACgF,GAAG,CAAC,GAAG,IAAI,CAACrE,OAAO,6BAA6B,EAAE;QAC7EkF,OAAO,EAAE;UACP,eAAe,EAAE,UAAUnE,KAAK;QAClC;MACF,CAAC,CAAC;MACF,OAAOgE,QAAQ,CAAC9C,IAAI,CAACA,IAAI,CAACmD,KAAK;IACjC,CAAC,CAAC,OAAOlE,KAAK,EAAE;MACdT,OAAO,CAACS,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMmE,UAAUA,CAACC,cAAc,EAAE;IAC/B,IAAI;MACF,MAAMR,OAAO,GAAG,CAAC,CAAC9D,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;MACpD,MAAMF,KAAK,GAAG+D,OAAO,GACjB9D,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,GAClCD,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;MAEvC,IAAI,CAACF,KAAK,EAAE;QACV,MAAM,IAAIK,KAAK,CAAC,+BAA+B,CAAC;MAClD;;MAEA;MACA,MAAM2D,QAAQ,GAAG,MAAM1F,KAAK,CAACkG,GAAG,CAAC,GAAG,IAAI,CAACvF,OAAO,kBAAkBsF,cAAc,OAAO,EAAE,CAAC,CAAC,EAAE;QAC3FJ,OAAO,EAAE;UACP,eAAe,EAAE,UAAUnE,KAAK;QAClC;MACF,CAAC,CAAC;MACF,OAAOgE,QAAQ,CAAC9C,IAAI;IACtB,CAAC,CAAC,OAAOf,KAAK,EAAE;MACdT,OAAO,CAACS,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5D,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMsE,aAAaA,CAAA,EAAG;IACpB,IAAI;MACF,MAAMV,OAAO,GAAG,CAAC,CAAC9D,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;MACpD,MAAMF,KAAK,GAAG+D,OAAO,GACjB9D,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,GAClCD,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;MAEvC,IAAI,CAACF,KAAK,EAAE;QACV,MAAM,IAAIK,KAAK,CAAC,+BAA+B,CAAC;MAClD;;MAEA;MACA,MAAM2D,QAAQ,GAAG,MAAM1F,KAAK,CAACkG,GAAG,CAAC,GAAG,IAAI,CAACvF,OAAO,8BAA8B,EAAE,CAAC,CAAC,EAAE;QAClFkF,OAAO,EAAE;UACP,eAAe,EAAE,UAAUnE,KAAK;QAClC;MACF,CAAC,CAAC;MACF,OAAOgE,QAAQ,CAAC9C,IAAI;IACtB,CAAC,CAAC,OAAOf,KAAK,EAAE;MACdT,OAAO,CAACS,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;MACjE,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMuE,oBAAoBA,CAACxD,IAAI,EAAE;IAC/B,IAAI;MACF,MAAMlB,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;MAChD,MAAM8D,QAAQ,GAAG,MAAM1F,KAAK,CAACqG,IAAI,CAAC,GAAG,IAAI,CAAC1F,OAAO,qBAAqB,EAAEiC,IAAI,EAAE;QAC5EiD,OAAO,EAAE;UACP,eAAe,EAAE,UAAUnE,KAAK;QAClC;MACF,CAAC,CAAC;MACF,OAAOgE,QAAQ,CAAC9C,IAAI;IACtB,CAAC,CAAC,OAAOf,KAAK,EAAE;MACdT,OAAO,CAACS,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMyE,aAAaA,CAAA,EAAG;IACpB,IAAI;MACF,MAAM5E,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;MAChD,MAAM8D,QAAQ,GAAG,MAAM1F,KAAK,CAACgF,GAAG,CAAC,GAAG,IAAI,CAACrE,OAAO,2BAA2B,EAAE;QAC3EkF,OAAO,EAAE;UACP,eAAe,EAAE,UAAUnE,KAAK;QAClC;MACF,CAAC,CAAC;MACF,OAAOgE,QAAQ,CAAC9C,IAAI;IACtB,CAAC,CAAC,OAAOf,KAAK,EAAE;MACdT,OAAO,CAACS,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;MAC9D,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAM0E,cAAcA,CAAA,EAAG;IACrBnF,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;IAE3C,IAAI;MACF;MACA,IAAI,IAAI,CAAClB,WAAW,EAAE;QACpBiB,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;QAC9C,IAAI,CAAClB,WAAW,CAACiD,KAAK,CAAC,CAAC;QACxB,IAAI,CAACjD,WAAW,GAAG,IAAI;QACvB,IAAI,CAACG,WAAW,GAAG,KAAK;QACxB,IAAI,CAACW,YAAY,GAAG,KAAK;MAC3B;;MAEA;MACA,IAAI,CAACF,cAAc,GAAG,CAAC;;MAEvB;MACA,MAAM,IAAI,CAACO,OAAO,CAAC,OAAO,CAAC;MAE3BF,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;;MAE7C;MACAmD,UAAU,CAAC,MAAM;QACfpD,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;MACtD,CAAC,EAAE,KAAK,CAAC;IAEX,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACdT,OAAO,CAACS,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IACpD;EACF;;EAEA;AACF;AACA;EACE,MAAM2E,uBAAuBA,CAACC,IAAI,GAAG,EAAE,EAAE;IACvC,IAAI;MACF,MAAM/E,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;MAChD,MAAM8D,QAAQ,GAAG,MAAM1F,KAAK,CAACmF,MAAM,CAAC,GAAG,IAAI,CAACxE,OAAO,wBAAwB,EAAE;QAC3EgF,MAAM,EAAE;UAAEc;QAAK,CAAC;QAChBZ,OAAO,EAAE;UACP,eAAe,EAAE,UAAUnE,KAAK;QAClC;MACF,CAAC,CAAC;MACF,OAAOgE,QAAQ,CAAC9C,IAAI;IACtB,CAAC,CAAC,OAAOf,KAAK,EAAE;MACdT,OAAO,CAACS,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5D,MAAMA,KAAK;IACb;EACF;AACF;;AAEA;AACA,MAAM6E,mBAAmB,GAAG,IAAIzG,mBAAmB,CAAC,CAAC;;AAErD;AACAmC,MAAM,CAACuE,qBAAqB,GAAGD,mBAAmB;;AAElD;AACAtE,MAAM,CAACwE,OAAO,GAAG,MAAM;EACrBxF,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;EACpDqF,mBAAmB,CAACpF,OAAO,CAAC,OAAO,CAAC;AACtC,CAAC;AAED,eAAeoF,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}