{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport adminAuthService from '@/services/adminAuthService';\nimport adminDocumentService from '@/services/adminDocumentService';\nimport notificationService from '@/services/notificationService';\nimport AdminHeader from './AdminHeader.vue';\nimport AdminSidebar from './AdminSidebar.vue';\nexport default {\n  name: 'AdminDashboard',\n  components: {\n    AdminHeader,\n    AdminSidebar\n  },\n  data() {\n    return {\n      loading: true,\n      // UI State\n      sidebarCollapsed: false,\n      showUserDropdown: false,\n      isMobile: false,\n      adminData: null,\n      // Dashboard Data\n      stats: {\n        totalRequests: 0,\n        pendingRequests: 0,\n        approvedRequests: 0,\n        completedRequests: 0,\n        processingRequests: 0,\n        urgentRequests: 0,\n        totalRevenue: 0,\n        todayRequests: 0,\n        todayRevenue: 0,\n        totalUsers: 0,\n        activeRequests: 0,\n        completedToday: 0,\n        pendingApproval: 0\n      },\n      recentActivity: [],\n      priorityRequests: [],\n      recentRequests: [],\n      errorMessage: ''\n    };\n  },\n  async mounted() {\n    // Check authentication\n    if (!adminAuthService.isLoggedIn()) {\n      this.$router.push('/admin/login');\n      return;\n    }\n\n    // Initialize UI state\n    this.initializeUI();\n\n    // Load dashboard data\n    await this.loadDashboardData();\n\n    // Initialize real-time features\n    this.initializeRealTimeFeatures();\n  },\n  beforeUnmount() {\n    if (this.handleResize) {\n      window.removeEventListener('resize', this.handleResize);\n    }\n    if (this.refreshInterval) {\n      clearInterval(this.refreshInterval);\n    }\n\n    // Clean up real-time features\n    this.cleanupRealTimeFeatures();\n  },\n  computed: {\n    activeMenu() {\n      const path = this.$route.path;\n      if (path.includes('/admin/users')) return 'users';\n      if (path.includes('/admin/requests')) return 'requests';\n      if (path.includes('/admin/reports')) return 'reports';\n      if (path.includes('/admin/settings')) return 'settings';\n      if (path.includes('/admin/activity-logs')) return 'activity';\n      if (path.includes('/admin/profile')) return 'profile';\n      return 'dashboard';\n    }\n  },\n  methods: {\n    // Initialize UI state\n    initializeUI() {\n      this.isMobile = window.innerWidth <= 768;\n\n      // Load saved sidebar state (only on desktop)\n      if (!this.isMobile) {\n        const saved = localStorage.getItem('adminSidebarCollapsed');\n        this.sidebarCollapsed = saved ? JSON.parse(saved) : false;\n      } else {\n        this.sidebarCollapsed = true; // Always collapsed on mobile\n      }\n\n      // Setup resize listener\n      this.handleResize = () => {\n        const wasMobile = this.isMobile;\n        this.isMobile = window.innerWidth <= 768;\n        if (this.isMobile && !wasMobile) {\n          this.sidebarCollapsed = true; // Collapse when switching to mobile\n        } else if (!this.isMobile && wasMobile) {\n          // Restore saved state when switching to desktop\n          const saved = localStorage.getItem('adminSidebarCollapsed');\n          this.sidebarCollapsed = saved ? JSON.parse(saved) : false;\n        }\n      };\n      window.addEventListener('resize', this.handleResize);\n    },\n    // Sidebar toggle\n    handleSidebarToggle() {\n      this.sidebarCollapsed = !this.sidebarCollapsed;\n      localStorage.setItem('adminSidebarCollapsed', JSON.stringify(this.sidebarCollapsed));\n    },\n    // Menu navigation\n    handleMenuChange(menu) {\n      const routes = {\n        'dashboard': '/admin/dashboard',\n        'users': '/admin/users',\n        'requests': '/admin/requests',\n        'reports': '/admin/reports',\n        'settings': '/admin/settings',\n        'activity': '/admin/activity-logs',\n        'profile': '/admin/profile'\n      };\n\n      // Close sidebar on mobile after navigation\n      if (this.isMobile) {\n        this.sidebarCollapsed = true;\n      }\n      if (routes[menu]) {\n        this.$router.push(routes[menu]);\n      }\n    },\n    // User dropdown toggle\n    handleUserDropdownToggle() {\n      this.showUserDropdown = !this.showUserDropdown;\n    },\n    // Menu actions\n    handleMenuAction(action) {\n      if (action === 'profile') {\n        this.$router.push('/admin/profile');\n      } else if (action === 'settings') {\n        this.$router.push('/admin/settings');\n      }\n      this.showUserDropdown = false;\n    },\n    // Close mobile sidebar\n    closeMobileSidebar() {\n      if (this.isMobile) {\n        this.sidebarCollapsed = true;\n      }\n    },\n    // Logout\n    handleLogout() {\n      adminAuthService.logout();\n      this.$router.push('/admin/login');\n    },\n    // Load dashboard data\n    async loadDashboardData() {\n      this.loading = true;\n      try {\n        // Load admin profile\n        await this.loadAdminProfile();\n\n        // Load dashboard statistics\n        await this.loadDashboardStats();\n\n        // Load recent activity\n        await this.loadRecentActivity();\n      } catch (error) {\n        console.error('Failed to load dashboard data:', error);\n        const errorData = adminAuthService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to load dashboard data';\n\n        // If unauthorized, redirect to login\n        if (errorData.status === 401) {\n          adminAuthService.logout();\n          this.$router.push('/admin/login');\n        }\n      } finally {\n        this.loading = false;\n      }\n    },\n    // Load admin profile\n    async loadAdminProfile() {\n      try {\n        const response = await adminAuthService.getProfile();\n        if (response.success) {\n          this.adminData = response.data;\n        }\n      } catch (error) {\n        console.error('Failed to load admin profile:', error);\n        this.adminData = adminAuthService.getAdminData();\n      }\n    },\n    // Load dashboard statistics\n    async loadDashboardStats() {\n      try {\n        const response = await adminDocumentService.getDashboardStats();\n        if (response.success) {\n          // Update stats with enhanced request management data\n          this.stats = {\n            totalRequests: response.data.totalRequests || 0,\n            pendingRequests: response.data.pendingRequests || 0,\n            approvedRequests: response.data.approvedRequests || 0,\n            completedRequests: response.data.completedRequests || 0,\n            processingRequests: response.data.processingRequests || 0,\n            urgentRequests: response.data.urgentRequests || 0,\n            totalRevenue: response.data.totalRevenue || 0,\n            todayRequests: response.data.todayRequests || 0,\n            todayRevenue: response.data.todayRevenue || 0,\n            // Keep legacy fields for backward compatibility\n            totalUsers: response.data.totalUsers || 0,\n            activeRequests: response.data.pendingRequests || 0,\n            completedToday: response.data.todayRequests || 0,\n            pendingApproval: response.data.pendingRequests || 0\n          };\n        }\n\n        // Load priority and recent requests\n        await this.loadPriorityRequests();\n        await this.loadRecentRequests();\n      } catch (error) {\n        console.error('Failed to load dashboard stats:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to load dashboard statistics';\n      }\n    },\n    // Load priority requests\n    async loadPriorityRequests() {\n      try {\n        const response = await adminDocumentService.getAllRequests({\n          priority: 'high',\n          limit: 5,\n          status: 'pending'\n        });\n        if (response.success) {\n          this.priorityRequests = response.data.requests || [];\n        }\n      } catch (error) {\n        console.error('Failed to load priority requests:', error);\n        this.priorityRequests = [];\n      }\n    },\n    // Load recent requests\n    async loadRecentRequests() {\n      try {\n        const response = await adminDocumentService.getAllRequests({\n          limit: 5,\n          sort: 'requested_at',\n          order: 'desc'\n        });\n        if (response.success) {\n          this.recentRequests = response.data.requests || [];\n        }\n      } catch (error) {\n        console.error('Failed to load recent requests:', error);\n        this.recentRequests = [];\n      }\n    },\n    // Load recent activity\n    async loadRecentActivity() {\n      try {\n        const response = await adminDocumentService.getRecentActivity(10);\n        if (response.success) {\n          this.recentActivity = response.data || [];\n        }\n      } catch (error) {\n        console.error('Failed to load recent activity:', error);\n        const errorData = adminDocumentService.parseError(error);\n        console.error('Recent activity error details:', errorData);\n        this.recentActivity = [];\n      }\n    },\n    // Navigate to specific route\n    navigateTo(route) {\n      try {\n        console.log('Navigating to:', route);\n        this.$router.push(route);\n      } catch (error) {\n        console.error('Navigation error:', error);\n        this.errorMessage = 'Navigation failed. Please try again.';\n      }\n    },\n    // Format date for display\n    formatDate(dateString) {\n      if (!dateString) return '';\n      const date = new Date(dateString);\n      const now = new Date();\n      const diffInMinutes = Math.floor((now - date) / (1000 * 60));\n      if (diffInMinutes < 1) {\n        return 'Just now';\n      } else if (diffInMinutes < 60) {\n        return `${diffInMinutes} minute${diffInMinutes > 1 ? 's' : ''} ago`;\n      } else if (diffInMinutes < 1440) {\n        const hours = Math.floor(diffInMinutes / 60);\n        return `${hours} hour${hours > 1 ? 's' : ''} ago`;\n      } else {\n        const days = Math.floor(diffInMinutes / 1440);\n        return `${days} day${days > 1 ? 's' : ''} ago`;\n      }\n    },\n    // Format time for display\n    formatTime(dateString) {\n      if (!dateString) return '';\n      const date = new Date(dateString);\n      return date.toLocaleTimeString('en-US', {\n        hour: '2-digit',\n        minute: '2-digit',\n        hour12: true\n      });\n    },\n    // Refresh dashboard data\n    async refreshDashboard() {\n      await this.loadDashboardData();\n    },\n    // Export activity logs\n    exportActivity() {\n      // Implement export functionality\n      console.log('Exporting activity logs...');\n      // This would typically generate and download a CSV/Excel file\n    },\n    // Get activity icon based on type\n    getActivityIcon(type) {\n      const icons = {\n        'user_registration': 'fas fa-user-plus',\n        'document_request': 'fas fa-file-alt',\n        'document_approved': 'fas fa-check-circle',\n        'document_rejected': 'fas fa-times-circle',\n        'system_update': 'fas fa-cog',\n        'login': 'fas fa-sign-in-alt',\n        'logout': 'fas fa-sign-out-alt',\n        'default': 'fas fa-info-circle'\n      };\n      return icons[type] || icons.default;\n    },\n    // Get activity icon circle class based on type\n    getActivityIconClass(type) {\n      const classes = {\n        'user_registration': 'bg-success',\n        'document_request': 'bg-primary',\n        'document_approved': 'bg-success',\n        'document_rejected': 'bg-danger',\n        'system_update': 'bg-warning',\n        'login': 'bg-info',\n        'logout': 'bg-secondary',\n        'default': 'bg-primary'\n      };\n      return classes[type] || classes.default;\n    },\n    // Get activity badge class based on status\n    getActivityBadgeClass(status) {\n      const classes = {\n        'completed': 'badge-success',\n        'pending': 'badge-warning',\n        'failed': 'badge-danger',\n        'in_progress': 'badge-info',\n        'default': 'badge-secondary'\n      };\n      return classes[status?.toLowerCase()] || classes.default;\n    },\n    // Enhanced Request Management Methods\n\n    // Format time ago for display\n    formatTimeAgo(dateString) {\n      if (!dateString) return '';\n      const date = new Date(dateString);\n      const now = new Date();\n      const diffInMinutes = Math.floor((now - date) / (1000 * 60));\n      if (diffInMinutes < 1) {\n        return 'Just now';\n      } else if (diffInMinutes < 60) {\n        return `${diffInMinutes}m ago`;\n      } else if (diffInMinutes < 1440) {\n        const hours = Math.floor(diffInMinutes / 60);\n        return `${hours}h ago`;\n      } else {\n        const days = Math.floor(diffInMinutes / 1440);\n        return `${days}d ago`;\n      }\n    },\n    // Filter requests by status and navigate\n    filterRequestsByStatus(status) {\n      this.$router.push({\n        path: '/admin/requests',\n        query: {\n          status: status\n        }\n      });\n    },\n    // Filter requests by date and navigate\n    filterRequestsByDate(period) {\n      const query = {};\n      const today = new Date();\n      if (period === 'today') {\n        query.date_from = today.toISOString().split('T')[0];\n        query.date_to = today.toISOString().split('T')[0];\n      }\n      this.$router.push({\n        path: '/admin/requests',\n        query: query\n      });\n    },\n    // Navigate to requests with specific filters\n    navigateToRequests(filter) {\n      const query = {};\n      switch (filter) {\n        case 'pending':\n          query.status = 'pending';\n          break;\n        case 'urgent':\n          query.priority = 'high';\n          query.status = 'pending';\n          break;\n        case 'processing':\n          query.status = 'processing';\n          break;\n        case 'recent':\n          query.sort = 'requested_at';\n          query.order = 'desc';\n          break;\n      }\n      this.$router.push({\n        path: '/admin/requests',\n        query: query\n      });\n    },\n    // View request details\n    viewRequestDetails(requestId) {\n      this.$router.push({\n        path: '/admin/requests',\n        query: {\n          view: requestId\n        }\n      });\n    },\n    // Get progress percentage for processing overview\n    getProgressPercentage(type) {\n      const total = this.stats.totalRequests || 1; // Avoid division by zero\n\n      switch (type) {\n        case 'pending':\n          return Math.round(this.stats.pendingRequests / total * 100);\n        case 'processing':\n          return Math.round(this.stats.processingRequests / total * 100);\n        case 'completed':\n          return Math.round(this.stats.completedRequests / total * 100);\n        default:\n          return 0;\n      }\n    },\n    // Format currency for display\n    formatCurrency(amount) {\n      if (!amount) return '0.00';\n      return parseFloat(amount).toLocaleString('en-US', {\n        minimumFractionDigits: 2,\n        maximumFractionDigits: 2\n      });\n    },\n    // Real-time features\n    async initializeRealTimeFeatures() {\n      console.log('Initializing real-time features for AdminDashboard');\n      try {\n        // Add connection reference (will connect if needed)\n        await notificationService.addConnectionRef('admin');\n\n        // Listen for dashboard-relevant notifications\n        notificationService.on('notification', this.handleRealTimeNotification);\n        notificationService.on('new_request', this.handleNewRequest);\n        notificationService.on('request_status_changed', this.handleStatusChange);\n        notificationService.on('dashboard_update', this.handleDashboardUpdate);\n      } catch (error) {\n        console.error('Failed to initialize real-time features:', error);\n      }\n    },\n    cleanupRealTimeFeatures() {\n      console.log('Cleaning up real-time features for AdminDashboard');\n\n      // Remove notification listeners\n      notificationService.off('notification', this.handleRealTimeNotification);\n      notificationService.off('new_request', this.handleNewRequest);\n      notificationService.off('request_status_changed', this.handleStatusChange);\n      notificationService.off('dashboard_update', this.handleDashboardUpdate);\n    },\n    handleRealTimeNotification(notification) {\n      console.log('Dashboard received real-time notification:', notification);\n\n      // Handle different notification types\n      switch (notification.type) {\n        case 'new_request':\n          this.handleNewRequest(notification.data);\n          break;\n        case 'request_status_changed':\n          this.handleStatusChange(notification.data);\n          break;\n        case 'dashboard_update':\n          this.handleDashboardUpdate(notification.data);\n          break;\n        default:\n          console.log('Unhandled notification type:', notification.type);\n      }\n    },\n    handleNewRequest(data) {\n      console.log('New request received on dashboard:', data);\n\n      // Update statistics\n      this.stats.totalRequests++;\n      this.stats.pendingRequests++;\n      this.stats.todayRequests++;\n\n      // Refresh dashboard data to get accurate counts\n      this.loadDashboardStats();\n      this.loadRecentActivity();\n      this.loadRecentRequests();\n    },\n    handleStatusChange(data) {\n      console.log('Request status changed on dashboard:', data);\n\n      // Refresh dashboard statistics and recent activity\n      this.loadDashboardStats();\n      this.loadRecentActivity();\n      this.loadPriorityRequests();\n      this.loadRecentRequests();\n    },\n    handleDashboardUpdate(data) {\n      console.log('Dashboard update received:', data);\n\n      // Refresh all dashboard data\n      this.loadDashboardData();\n    }\n  },\n  // Auto-refresh dashboard data every 5 minutes\n  created() {\n    this.refreshInterval = setInterval(() => {\n      this.loadDashboardStats();\n      this.loadRecentActivity();\n      this.loadPriorityRequests();\n      this.loadRecentRequests();\n    }, 5 * 60 * 1000); // 5 minutes\n  }\n};", "map": {"version": 3, "names": ["adminAuthService", "adminDocumentService", "notificationService", "Ad<PERSON><PERSON><PERSON><PERSON>", "AdminSidebar", "name", "components", "data", "loading", "sidebarCollapsed", "showUserDropdown", "isMobile", "adminData", "stats", "totalRequests", "pendingRequests", "approvedRequests", "completedRequests", "processingRequests", "urgentRequests", "totalRevenue", "todayRequests", "todayRevenue", "totalUsers", "activeRequests", "completedToday", "pendingApproval", "recentActivity", "priorityRequests", "recentRequests", "errorMessage", "mounted", "isLoggedIn", "$router", "push", "initializeUI", "loadDashboardData", "initializeRealTimeFeatures", "beforeUnmount", "handleResize", "window", "removeEventListener", "refreshInterval", "clearInterval", "cleanupRealTimeFeatures", "computed", "activeMenu", "path", "$route", "includes", "methods", "innerWidth", "saved", "localStorage", "getItem", "JSON", "parse", "was<PERSON><PERSON><PERSON>", "addEventListener", "handleSidebarToggle", "setItem", "stringify", "handleMenuChange", "menu", "routes", "handleUserDropdownToggle", "handleMenuAction", "action", "closeMobileSidebar", "handleLogout", "logout", "loadAdminProfile", "loadDashboardStats", "loadRecentActivity", "error", "console", "errorData", "parseError", "message", "status", "response", "getProfile", "success", "getAdminData", "getDashboardStats", "loadPriorityRequests", "loadRecentRequests", "getAllRequests", "priority", "limit", "requests", "sort", "order", "getRecentActivity", "navigateTo", "route", "log", "formatDate", "dateString", "date", "Date", "now", "diffInMinutes", "Math", "floor", "hours", "days", "formatTime", "toLocaleTimeString", "hour", "minute", "hour12", "refreshDashboard", "exportActivity", "getActivityIcon", "type", "icons", "default", "getActivityIconClass", "classes", "getActivityBadgeClass", "toLowerCase", "formatTimeAgo", "filterRequestsByStatus", "query", "filterRequestsByDate", "period", "today", "date_from", "toISOString", "split", "date_to", "navigateToRequests", "filter", "viewRequestDetails", "requestId", "view", "getProgressPercentage", "total", "round", "formatCurrency", "amount", "parseFloat", "toLocaleString", "minimumFractionDigits", "maximumFractionDigits", "addConnectionRef", "on", "handleRealTimeNotification", "handleNewRequest", "handleStatusChange", "handleDashboardUpdate", "off", "notification", "created", "setInterval"], "sources": ["D:\\rhai_front_and_back\\BOSFDR\\src\\components\\admin\\AdminDashboard.vue"], "sourcesContent": ["<template>\n  <div class=\"admin-dashboard\">\n    <AdminHeader\n      :userName=\"adminData?.first_name || 'Admin'\"\n      :showUserDropdown=\"showUserDropdown\"\n      :sidebarCollapsed=\"sidebarCollapsed\"\n      :activeMenu=\"activeMenu\"\n      @sidebar-toggle=\"handleSidebarToggle\"\n      @user-dropdown-toggle=\"handleUserDropdownToggle\"\n      @menu-action=\"handleMenuAction\"\n      @logout=\"handleLogout\"\n    />\n\n    <!-- Mobile Overlay -->\n    <div\n      class=\"mobile-overlay\"\n      :class=\"{ active: !sidebarCollapsed && isMobile }\"\n      @click=\"closeMobileSidebar\"\n    ></div>\n\n    <div class=\"dashboard-container\">\n      <AdminSidebar\n        :collapsed=\"sidebarCollapsed\"\n        :activeMenu=\"activeMenu\"\n        :totalUsers=\"stats.totalUsers\"\n        :pendingRequests=\"stats.activeRequests\"\n        :totalReports=\"stats.completedToday\"\n        @menu-change=\"handleMenuChange\"\n        @logout=\"handleLogout\"\n        @toggle-sidebar=\"handleSidebarToggle\"\n      />\n      <main class=\"main-content\" :class=\"{ 'sidebar-collapsed': sidebarCollapsed }\">\n        <div class=\"container-fluid p-4\">\n          <div class=\"row mb-4\">\n            <div class=\"col-12\">\n              <div class=\"d-flex justify-content-between align-items-center flex-wrap\">\n\n                <div class=\"d-flex gap-2\">\n                  <button class=\"btn btn-outline-success btn-sm\" @click=\"refreshDashboard\" :disabled=\"loading\">\n                    <i class=\"fas fa-sync-alt me-1\" :class=\"{ 'fa-spin': loading }\"></i>\n                    Refresh\n                  </button>\n                  <div class=\"dropdown\">\n                    <button class=\"btn btn-success btn-sm dropdown-toggle\" type=\"button\" data-bs-toggle=\"dropdown\">\n                      <i class=\"fas fa-plus me-1\"></i>\n                      Quick Actions\n                    </button>\n                    <ul class=\"dropdown-menu\">\n                      <li><a class=\"dropdown-item\" href=\"#\" @click=\"navigateTo('/admin/users')\">\n                        <i class=\"fas fa-user-plus me-2\"></i>Add User\n                      </a></li>\n                      <li><a class=\"dropdown-item\" href=\"#\" @click=\"navigateTo('/admin/requests')\">\n                        <i class=\"fas fa-file-alt me-2\"></i>New Request\n                      </a></li>\n                      <li><hr class=\"dropdown-divider\"></li>\n                      <li><a class=\"dropdown-item\" href=\"#\" @click=\"navigateTo('/admin/reports')\">\n                        <i class=\"fas fa-chart-bar me-2\"></i>Generate Report\n                      </a></li>\n                    </ul>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Enhanced Request Management Dashboard Stats -->\n          <div class=\"row mb-4\">\n            <!-- Total Requests -->\n            <div class=\"col-xl-3 col-md-6 mb-4\">\n              <div class=\"card border-left-primary shadow h-100 py-2 stat-card\" @click=\"navigateTo('/admin/requests')\">\n                <div class=\"card-body\">\n                  <div class=\"row no-gutters align-items-center\">\n                    <div class=\"col mr-2\">\n                      <div class=\"text-xs font-weight-bold text-primary text-uppercase mb-1\">\n                        Total Requests\n                      </div>\n                      <div class=\"h5 mb-0 font-weight-bold text-gray-800\">{{ stats.totalRequests || 0 }}</div>\n                      <div class=\"text-xs text-muted mt-1\">\n                        <i class=\"fas fa-file-alt text-primary me-1\"></i>\n                        All document requests\n                      </div>\n                    </div>\n                    <div class=\"col-auto\">\n                      <div class=\"icon-circle bg-primary\">\n                        <i class=\"fas fa-file-alt text-white\"></i>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <!-- Pending Requests (High Priority) -->\n            <div class=\"col-xl-3 col-md-6 mb-4\">\n              <div class=\"card border-left-warning shadow h-100 py-2 stat-card\" @click=\"filterRequestsByStatus('pending')\">\n                <div class=\"card-body\">\n                  <div class=\"row no-gutters align-items-center\">\n                    <div class=\"col mr-2\">\n                      <div class=\"text-xs font-weight-bold text-warning text-uppercase mb-1\">\n                        Pending Requests\n                      </div>\n                      <div class=\"h5 mb-0 font-weight-bold text-gray-800\">{{ stats.pendingRequests || 0 }}</div>\n                      <div class=\"text-xs text-muted mt-1\">\n                        <i class=\"fas fa-clock text-warning me-1\"></i>\n                        <span class=\"badge badge-danger ms-1\" v-if=\"stats.urgentRequests > 0\">{{ stats.urgentRequests }} urgent</span>\n                        <span v-else>Awaiting review</span>\n                      </div>\n                    </div>\n                    <div class=\"col-auto\">\n                      <div class=\"icon-circle bg-warning\">\n                        <i class=\"fas fa-clock text-white\"></i>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <!-- Processing Requests -->\n            <div class=\"col-xl-3 col-md-6 mb-4\">\n              <div class=\"card border-left-info shadow h-100 py-2 stat-card\" @click=\"filterRequestsByStatus('processing')\">\n                <div class=\"card-body\">\n                  <div class=\"row no-gutters align-items-center\">\n                    <div class=\"col mr-2\">\n                      <div class=\"text-xs font-weight-bold text-info text-uppercase mb-1\">\n                        Processing\n                      </div>\n                      <div class=\"h5 mb-0 font-weight-bold text-gray-800\">{{ stats.processingRequests || 0 }}</div>\n                      <div class=\"text-xs text-muted mt-1\">\n                        <i class=\"fas fa-cog text-info me-1\"></i>\n                        Currently being processed\n                      </div>\n                    </div>\n                    <div class=\"col-auto\">\n                      <div class=\"icon-circle bg-info\">\n                        <i class=\"fas fa-cog text-white\"></i>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <!-- Completed Today -->\n            <div class=\"col-xl-3 col-md-6 mb-4\">\n              <div class=\"card border-left-success shadow h-100 py-2 stat-card\" @click=\"filterRequestsByDate('today')\">\n                <div class=\"card-body\">\n                  <div class=\"row no-gutters align-items-center\">\n                    <div class=\"col mr-2\">\n                      <div class=\"text-xs font-weight-bold text-success text-uppercase mb-1\">\n                        Completed Today\n                      </div>\n                      <div class=\"h5 mb-0 font-weight-bold text-gray-800\">{{ stats.todayRequests || 0 }}</div>\n                      <div class=\"text-xs text-muted mt-1\">\n                        <i class=\"fas fa-check-circle text-success me-1\"></i>\n                        ₱{{ (stats.todayRevenue || 0).toLocaleString() }} revenue\n                      </div>\n                    </div>\n                    <div class=\"col-auto\">\n                      <div class=\"icon-circle bg-success\">\n                        <i class=\"fas fa-check-circle text-white\"></i>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Request Management Quick Actions -->\n          <div class=\"row mb-4\">\n            <div class=\"col-12\">\n              <div class=\"card shadow\">\n                <div class=\"card-header py-3 d-flex justify-content-between align-items-center\">\n                  <h6 class=\"m-0 font-weight-bold text-primary\">\n                    <i class=\"fas fa-tasks me-2\"></i>\n                    Request Management Center\n                  </h6>\n                  <div class=\"d-flex gap-2\">\n                    <button class=\"btn btn-outline-primary btn-sm\" @click=\"refreshDashboard\" :disabled=\"loading\">\n                      <i class=\"fas fa-sync-alt me-1\" :class=\"{ 'fa-spin': loading }\"></i>\n                      Refresh\n                    </button>\n                    <div class=\"dropdown\">\n                      <button class=\"btn btn-primary btn-sm dropdown-toggle\" type=\"button\" data-bs-toggle=\"dropdown\">\n                        <i class=\"fas fa-bolt me-1\"></i>\n                        Quick Actions\n                      </button>\n                      <ul class=\"dropdown-menu\">\n                        <li><a class=\"dropdown-item\" href=\"#\" @click=\"navigateToRequests('pending')\">\n                          <i class=\"fas fa-clock me-2 text-warning\"></i>Review Pending Requests\n                        </a></li>\n                        <li><a class=\"dropdown-item\" href=\"#\" @click=\"navigateToRequests('urgent')\">\n                          <i class=\"fas fa-exclamation-triangle me-2 text-danger\"></i>Handle Urgent Requests\n                        </a></li>\n                        <li><a class=\"dropdown-item\" href=\"#\" @click=\"navigateToRequests('processing')\">\n                          <i class=\"fas fa-cog me-2 text-info\"></i>Monitor Processing\n                        </a></li>\n                        <li><hr class=\"dropdown-divider\"></li>\n                        <li><a class=\"dropdown-item\" href=\"#\" @click=\"navigateTo('/admin/reports')\">\n                          <i class=\"fas fa-chart-bar me-2 text-success\"></i>Generate Reports\n                        </a></li>\n                      </ul>\n                    </div>\n                  </div>\n                </div>\n                <div class=\"card-body\">\n                  <div class=\"row\">\n                    <!-- Priority Requests -->\n                    <div class=\"col-lg-4 mb-3\">\n                      <div class=\"border rounded p-3 h-100\">\n                        <h6 class=\"text-danger mb-3\">\n                          <i class=\"fas fa-exclamation-triangle me-2\"></i>\n                          Priority Requests\n                        </h6>\n                        <div v-if=\"priorityRequests.length === 0\" class=\"text-center text-muted py-3\">\n                          <i class=\"fas fa-check-circle fa-2x mb-2 text-success\"></i>\n                          <p class=\"mb-0\">No urgent requests</p>\n                        </div>\n                        <div v-else>\n                          <div v-for=\"request in priorityRequests.slice(0, 3)\" :key=\"request.id\"\n                               class=\"d-flex justify-content-between align-items-center mb-2 p-2 bg-light rounded\">\n                            <div>\n                              <small class=\"text-muted\">{{ request.request_number }}</small>\n                              <div class=\"fw-bold\">{{ request.document_type }}</div>\n                              <small class=\"text-danger\">{{ request.priority }} Priority</small>\n                            </div>\n                            <button class=\"btn btn-sm btn-outline-primary\" @click=\"viewRequestDetails(request.id)\">\n                              <i class=\"fas fa-eye\"></i>\n                            </button>\n                          </div>\n                          <div v-if=\"priorityRequests.length > 3\" class=\"text-center mt-2\">\n                            <button class=\"btn btn-sm btn-outline-danger\" @click=\"navigateToRequests('urgent')\">\n                              View {{ priorityRequests.length - 3 }} more urgent\n                            </button>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n\n                    <!-- Recent Submissions -->\n                    <div class=\"col-lg-4 mb-3\">\n                      <div class=\"border rounded p-3 h-100\">\n                        <h6 class=\"text-info mb-3\">\n                          <i class=\"fas fa-clock me-2\"></i>\n                          Recent Submissions\n                        </h6>\n                        <div v-if=\"recentRequests.length === 0\" class=\"text-center text-muted py-3\">\n                          <i class=\"fas fa-inbox fa-2x mb-2 text-gray-300\"></i>\n                          <p class=\"mb-0\">No recent submissions</p>\n                        </div>\n                        <div v-else>\n                          <div v-for=\"request in recentRequests.slice(0, 3)\" :key=\"request.id\"\n                               class=\"d-flex justify-content-between align-items-center mb-2 p-2 bg-light rounded\">\n                            <div>\n                              <small class=\"text-muted\">{{ formatTimeAgo(request.requested_at) }}</small>\n                              <div class=\"fw-bold\">{{ request.document_type }}</div>\n                              <small class=\"text-info\">{{ request.client_name }}</small>\n                            </div>\n                            <button class=\"btn btn-sm btn-outline-primary\" @click=\"viewRequestDetails(request.id)\">\n                              <i class=\"fas fa-eye\"></i>\n                            </button>\n                          </div>\n                          <div v-if=\"recentRequests.length > 3\" class=\"text-center mt-2\">\n                            <button class=\"btn btn-sm btn-outline-info\" @click=\"navigateToRequests('recent')\">\n                              View {{ recentRequests.length - 3 }} more recent\n                            </button>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n\n                    <!-- Processing Overview -->\n                    <div class=\"col-lg-4 mb-3\">\n                      <div class=\"border rounded p-3 h-100\">\n                        <h6 class=\"text-success mb-3\">\n                          <i class=\"fas fa-cogs me-2\"></i>\n                          Processing Overview\n                        </h6>\n                        <div class=\"mb-2\">\n                          <div class=\"d-flex justify-content-between\">\n                            <span class=\"small\">Pending Review</span>\n                            <span class=\"badge bg-warning\">{{ stats.pendingRequests || 0 }}</span>\n                          </div>\n                          <div class=\"progress mb-2\" style=\"height: 4px;\">\n                            <div class=\"progress-bar bg-warning\" :style=\"{ width: getProgressPercentage('pending') + '%' }\"></div>\n                          </div>\n                        </div>\n                        <div class=\"mb-2\">\n                          <div class=\"d-flex justify-content-between\">\n                            <span class=\"small\">Processing</span>\n                            <span class=\"badge bg-info\">{{ stats.processingRequests || 0 }}</span>\n                          </div>\n                          <div class=\"progress mb-2\" style=\"height: 4px;\">\n                            <div class=\"progress-bar bg-info\" :style=\"{ width: getProgressPercentage('processing') + '%' }\"></div>\n                          </div>\n                        </div>\n                        <div class=\"mb-2\">\n                          <div class=\"d-flex justify-content-between\">\n                            <span class=\"small\">Completed</span>\n                            <span class=\"badge bg-success\">{{ stats.completedRequests || 0 }}</span>\n                          </div>\n                          <div class=\"progress mb-2\" style=\"height: 4px;\">\n                            <div class=\"progress-bar bg-success\" :style=\"{ width: getProgressPercentage('completed') + '%' }\"></div>\n                          </div>\n                        </div>\n                        <div class=\"text-center mt-3\">\n                          <button class=\"btn btn-sm btn-success\" @click=\"navigateTo('/admin/requests')\">\n                            <i class=\"fas fa-list me-1\"></i>\n                            View All Requests\n                          </button>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Recent Activity -->\n          <div class=\"row\">\n            <div class=\"col-lg-8 mb-4\">\n              <div class=\"card shadow\">\n                <div class=\"card-header py-3 d-flex flex-row align-items-center justify-content-between\">\n                  <h6 class=\"m-0 font-weight-bold text-primary\">\n                    <i class=\"fas fa-history me-2\"></i>\n                    Recent Activity\n                    <span class=\"badge bg-success ms-2 pulse\" v-if=\"recentActivity.length > 0\">\n                      <i class=\"fas fa-circle\"></i>\n                      Live\n                    </span>\n                  </h6>\n                  <div class=\"dropdown no-arrow\">\n                    <button class=\"btn btn-link text-gray-400 p-0\" type=\"button\" data-bs-toggle=\"dropdown\">\n                      <i class=\"fas fa-ellipsis-v\"></i>\n                    </button>\n                    <div class=\"dropdown-menu dropdown-menu-end shadow\">\n                      <div class=\"dropdown-header\">Actions:</div>\n                      <a class=\"dropdown-item\" href=\"#\" @click=\"navigateTo('/admin/activity-logs')\">\n                        <i class=\"fas fa-list me-2\"></i>View All\n                      </a>\n                      <a class=\"dropdown-item\" href=\"#\" @click=\"exportActivity\">\n                        <i class=\"fas fa-download me-2\"></i>Export\n                      </a>\n                      <div class=\"dropdown-divider\"></div>\n                      <a class=\"dropdown-item\" href=\"#\" @click=\"refreshDashboard\">\n                        <i class=\"fas fa-sync-alt me-2\"></i>Refresh\n                      </a>\n                    </div>\n                  </div>\n                </div>\n                <div class=\"card-body\">\n                  <div v-if=\"loading\" class=\"text-center py-4\">\n                    <div class=\"spinner-border text-primary\" role=\"status\">\n                      <span class=\"visually-hidden\">Loading...</span>\n                    </div>\n                    <p class=\"text-muted mt-2\">Loading recent activity...</p>\n                  </div>\n                  <div v-else-if=\"recentActivity.length === 0\" class=\"text-center text-muted py-4\">\n                    <i class=\"fas fa-inbox fa-3x mb-3 text-gray-300\"></i>\n                    <h6 class=\"text-gray-600\">No recent activity</h6>\n                    <p class=\"small\">Activity will appear here as users interact with the system.</p>\n                  </div>\n                  <div v-else class=\"activity-list\">\n                    <div v-for=\"(activity, index) in recentActivity.slice(0, 5)\" :key=\"activity.id\"\n                         class=\"activity-item d-flex align-items-start mb-3 pb-3\"\n                         :class=\"{ 'border-bottom': index < recentActivity.slice(0, 5).length - 1 }\">\n                      <div class=\"me-3\">\n                        <div class=\"icon-circle\" :class=\"getActivityIconClass(activity.type)\">\n                          <i :class=\"getActivityIcon(activity.type)\" class=\"text-white\"></i>\n                        </div>\n                      </div>\n                      <div class=\"flex-grow-1\">\n                        <div class=\"d-flex justify-content-between align-items-start\">\n                          <div>\n                            <h6 class=\"mb-1 text-dark\">{{ activity.title || activity.description }}</h6>\n                            <p class=\"mb-1 text-muted small\">{{ activity.details || activity.description }}</p>\n                            <div class=\"small text-gray-500\">\n                              <i class=\"fas fa-clock me-1\"></i>\n                              {{ formatDate(activity.created_at) }}\n                            </div>\n                          </div>\n                          <span class=\"badge\" :class=\"getActivityBadgeClass(activity.status)\">\n                            {{ activity.status || 'Completed' }}\n                          </span>\n                        </div>\n                      </div>\n                    </div>\n                    <div v-if=\"recentActivity.length > 5\" class=\"text-center pt-2\">\n                      <button class=\"btn btn-outline-primary btn-sm\" @click=\"navigateTo('/admin/activity-logs')\">\n                        <i class=\"fas fa-plus me-1\"></i>\n                        View {{ recentActivity.length - 5 }} more activities\n                      </button>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <!-- Quick Actions -->\n            <div class=\"col-lg-4 mb-4\">\n              <div class=\"card shadow\">\n                <div class=\"card-header py-3\">\n                  <h6 class=\"m-0 font-weight-bold text-primary\">Quick Actions</h6>\n                </div>\n                <div class=\"card-body\">\n                  <div class=\"d-grid gap-2\">\n                    <button class=\"btn btn-primary btn-sm\" @click=\"navigateTo('/admin/users')\">\n                      <i class=\"fas fa-users me-2\"></i>\n                      Manage Users\n                    </button>\n                    <button class=\"btn btn-success btn-sm\" @click=\"navigateTo('/admin/requests')\">\n                      <i class=\"fas fa-file-alt me-2\"></i>\n                      View Requests\n                    </button>\n                    <button class=\"btn btn-info btn-sm\" @click=\"navigateTo('/admin/reports')\">\n                      <i class=\"fas fa-chart-bar me-2\"></i>\n                      Generate Reports\n                    </button>\n                    <button class=\"btn btn-warning btn-sm\" @click=\"navigateTo('/admin/settings')\">\n                      <i class=\"fas fa-cog me-2\"></i>\n                      System Settings\n                    </button>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </main>\n    </div>\n  </div>\n</template>\n\n<script>\nimport adminAuthService from '@/services/adminAuthService';\nimport adminDocumentService from '@/services/adminDocumentService';\nimport notificationService from '@/services/notificationService';\nimport AdminHeader from './AdminHeader.vue';\nimport AdminSidebar from './AdminSidebar.vue';\n\nexport default {\n  name: 'AdminDashboard',\n  components: {\n    AdminHeader,\n    AdminSidebar\n  },\n  data() {\n    return {\n      loading: true,\n      // UI State\n      sidebarCollapsed: false,\n      showUserDropdown: false,\n      isMobile: false,\n      adminData: null,\n      // Dashboard Data\n      stats: {\n        totalRequests: 0,\n        pendingRequests: 0,\n        approvedRequests: 0,\n        completedRequests: 0,\n        processingRequests: 0,\n        urgentRequests: 0,\n        totalRevenue: 0,\n        todayRequests: 0,\n        todayRevenue: 0,\n        totalUsers: 0,\n        activeRequests: 0,\n        completedToday: 0,\n        pendingApproval: 0\n      },\n      recentActivity: [],\n      priorityRequests: [],\n      recentRequests: [],\n      errorMessage: ''\n    };\n  },\n\n  async mounted() {\n    // Check authentication\n    if (!adminAuthService.isLoggedIn()) {\n      this.$router.push('/admin/login');\n      return;\n    }\n\n    // Initialize UI state\n    this.initializeUI();\n\n    // Load dashboard data\n    await this.loadDashboardData();\n\n    // Initialize real-time features\n    this.initializeRealTimeFeatures();\n  },\n\n  beforeUnmount() {\n    if (this.handleResize) {\n      window.removeEventListener('resize', this.handleResize);\n    }\n    if (this.refreshInterval) {\n      clearInterval(this.refreshInterval);\n    }\n\n    // Clean up real-time features\n    this.cleanupRealTimeFeatures();\n  },\n\n  computed: {\n    activeMenu() {\n      const path = this.$route.path;\n      if (path.includes('/admin/users')) return 'users';\n      if (path.includes('/admin/requests')) return 'requests';\n      if (path.includes('/admin/reports')) return 'reports';\n      if (path.includes('/admin/settings')) return 'settings';\n      if (path.includes('/admin/activity-logs')) return 'activity';\n      if (path.includes('/admin/profile')) return 'profile';\n      return 'dashboard';\n    }\n  },\n\n  methods: {\n    // Initialize UI state\n    initializeUI() {\n      this.isMobile = window.innerWidth <= 768;\n\n      // Load saved sidebar state (only on desktop)\n      if (!this.isMobile) {\n        const saved = localStorage.getItem('adminSidebarCollapsed');\n        this.sidebarCollapsed = saved ? JSON.parse(saved) : false;\n      } else {\n        this.sidebarCollapsed = true; // Always collapsed on mobile\n      }\n\n      // Setup resize listener\n      this.handleResize = () => {\n        const wasMobile = this.isMobile;\n        this.isMobile = window.innerWidth <= 768;\n\n        if (this.isMobile && !wasMobile) {\n          this.sidebarCollapsed = true; // Collapse when switching to mobile\n        } else if (!this.isMobile && wasMobile) {\n          // Restore saved state when switching to desktop\n          const saved = localStorage.getItem('adminSidebarCollapsed');\n          this.sidebarCollapsed = saved ? JSON.parse(saved) : false;\n        }\n      };\n      window.addEventListener('resize', this.handleResize);\n    },\n\n    // Sidebar toggle\n    handleSidebarToggle() {\n      this.sidebarCollapsed = !this.sidebarCollapsed;\n      localStorage.setItem('adminSidebarCollapsed', JSON.stringify(this.sidebarCollapsed));\n    },\n\n    // Menu navigation\n    handleMenuChange(menu) {\n      const routes = {\n        'dashboard': '/admin/dashboard',\n        'users': '/admin/users',\n        'requests': '/admin/requests',\n        'reports': '/admin/reports',\n        'settings': '/admin/settings',\n        'activity': '/admin/activity-logs',\n        'profile': '/admin/profile'\n      };\n\n      // Close sidebar on mobile after navigation\n      if (this.isMobile) {\n        this.sidebarCollapsed = true;\n      }\n\n      if (routes[menu]) {\n        this.$router.push(routes[menu]);\n      }\n    },\n\n    // User dropdown toggle\n    handleUserDropdownToggle() {\n      this.showUserDropdown = !this.showUserDropdown;\n    },\n\n    // Menu actions\n    handleMenuAction(action) {\n      if (action === 'profile') {\n        this.$router.push('/admin/profile');\n      } else if (action === 'settings') {\n        this.$router.push('/admin/settings');\n      }\n      this.showUserDropdown = false;\n    },\n\n    // Close mobile sidebar\n    closeMobileSidebar() {\n      if (this.isMobile) {\n        this.sidebarCollapsed = true;\n      }\n    },\n\n    // Logout\n    handleLogout() {\n      adminAuthService.logout();\n      this.$router.push('/admin/login');\n    },\n\n    // Load dashboard data\n    async loadDashboardData() {\n      this.loading = true;\n\n      try {\n        // Load admin profile\n        await this.loadAdminProfile();\n\n        // Load dashboard statistics\n        await this.loadDashboardStats();\n\n        // Load recent activity\n        await this.loadRecentActivity();\n\n      } catch (error) {\n        console.error('Failed to load dashboard data:', error);\n        const errorData = adminAuthService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to load dashboard data';\n\n        // If unauthorized, redirect to login\n        if (errorData.status === 401) {\n          adminAuthService.logout();\n          this.$router.push('/admin/login');\n        }\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    // Load admin profile\n    async loadAdminProfile() {\n      try {\n        const response = await adminAuthService.getProfile();\n        if (response.success) {\n          this.adminData = response.data;\n        }\n      } catch (error) {\n        console.error('Failed to load admin profile:', error);\n        this.adminData = adminAuthService.getAdminData();\n      }\n    },\n\n\n\n    // Load dashboard statistics\n    async loadDashboardStats() {\n      try {\n        const response = await adminDocumentService.getDashboardStats();\n        if (response.success) {\n          // Update stats with enhanced request management data\n          this.stats = {\n            totalRequests: response.data.totalRequests || 0,\n            pendingRequests: response.data.pendingRequests || 0,\n            approvedRequests: response.data.approvedRequests || 0,\n            completedRequests: response.data.completedRequests || 0,\n            processingRequests: response.data.processingRequests || 0,\n            urgentRequests: response.data.urgentRequests || 0,\n            totalRevenue: response.data.totalRevenue || 0,\n            todayRequests: response.data.todayRequests || 0,\n            todayRevenue: response.data.todayRevenue || 0,\n            // Keep legacy fields for backward compatibility\n            totalUsers: response.data.totalUsers || 0,\n            activeRequests: response.data.pendingRequests || 0,\n            completedToday: response.data.todayRequests || 0,\n            pendingApproval: response.data.pendingRequests || 0\n          };\n        }\n\n        // Load priority and recent requests\n        await this.loadPriorityRequests();\n        await this.loadRecentRequests();\n      } catch (error) {\n        console.error('Failed to load dashboard stats:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to load dashboard statistics';\n      }\n    },\n\n    // Load priority requests\n    async loadPriorityRequests() {\n      try {\n        const response = await adminDocumentService.getAllRequests({\n          priority: 'high',\n          limit: 5,\n          status: 'pending'\n        });\n        if (response.success) {\n          this.priorityRequests = response.data.requests || [];\n        }\n      } catch (error) {\n        console.error('Failed to load priority requests:', error);\n        this.priorityRequests = [];\n      }\n    },\n\n    // Load recent requests\n    async loadRecentRequests() {\n      try {\n        const response = await adminDocumentService.getAllRequests({\n          limit: 5,\n          sort: 'requested_at',\n          order: 'desc'\n        });\n        if (response.success) {\n          this.recentRequests = response.data.requests || [];\n        }\n      } catch (error) {\n        console.error('Failed to load recent requests:', error);\n        this.recentRequests = [];\n      }\n    },\n\n    // Load recent activity\n    async loadRecentActivity() {\n      try {\n        const response = await adminDocumentService.getRecentActivity(10);\n        if (response.success) {\n          this.recentActivity = response.data || [];\n        }\n      } catch (error) {\n        console.error('Failed to load recent activity:', error);\n        const errorData = adminDocumentService.parseError(error);\n        console.error('Recent activity error details:', errorData);\n        this.recentActivity = [];\n      }\n    },\n\n    // Navigate to specific route\n    navigateTo(route) {\n      try {\n        console.log('Navigating to:', route);\n        this.$router.push(route);\n      } catch (error) {\n        console.error('Navigation error:', error);\n        this.errorMessage = 'Navigation failed. Please try again.';\n      }\n    },\n\n    // Format date for display\n    formatDate(dateString) {\n      if (!dateString) return '';\n\n      const date = new Date(dateString);\n      const now = new Date();\n      const diffInMinutes = Math.floor((now - date) / (1000 * 60));\n\n      if (diffInMinutes < 1) {\n        return 'Just now';\n      } else if (diffInMinutes < 60) {\n        return `${diffInMinutes} minute${diffInMinutes > 1 ? 's' : ''} ago`;\n      } else if (diffInMinutes < 1440) {\n        const hours = Math.floor(diffInMinutes / 60);\n        return `${hours} hour${hours > 1 ? 's' : ''} ago`;\n      } else {\n        const days = Math.floor(diffInMinutes / 1440);\n        return `${days} day${days > 1 ? 's' : ''} ago`;\n      }\n    },\n\n    // Format time for display\n    formatTime(dateString) {\n      if (!dateString) return '';\n\n      const date = new Date(dateString);\n      return date.toLocaleTimeString('en-US', {\n        hour: '2-digit',\n        minute: '2-digit',\n        hour12: true\n      });\n    },\n\n    // Refresh dashboard data\n    async refreshDashboard() {\n      await this.loadDashboardData();\n    },\n\n    // Export activity logs\n    exportActivity() {\n      // Implement export functionality\n      console.log('Exporting activity logs...');\n      // This would typically generate and download a CSV/Excel file\n    },\n\n    // Get activity icon based on type\n    getActivityIcon(type) {\n      const icons = {\n        'user_registration': 'fas fa-user-plus',\n        'document_request': 'fas fa-file-alt',\n        'document_approved': 'fas fa-check-circle',\n        'document_rejected': 'fas fa-times-circle',\n        'system_update': 'fas fa-cog',\n        'login': 'fas fa-sign-in-alt',\n        'logout': 'fas fa-sign-out-alt',\n        'default': 'fas fa-info-circle'\n      };\n      return icons[type] || icons.default;\n    },\n\n    // Get activity icon circle class based on type\n    getActivityIconClass(type) {\n      const classes = {\n        'user_registration': 'bg-success',\n        'document_request': 'bg-primary',\n        'document_approved': 'bg-success',\n        'document_rejected': 'bg-danger',\n        'system_update': 'bg-warning',\n        'login': 'bg-info',\n        'logout': 'bg-secondary',\n        'default': 'bg-primary'\n      };\n      return classes[type] || classes.default;\n    },\n\n    // Get activity badge class based on status\n    getActivityBadgeClass(status) {\n      const classes = {\n        'completed': 'badge-success',\n        'pending': 'badge-warning',\n        'failed': 'badge-danger',\n        'in_progress': 'badge-info',\n        'default': 'badge-secondary'\n      };\n      return classes[status?.toLowerCase()] || classes.default;\n    },\n\n    // Enhanced Request Management Methods\n\n    // Format time ago for display\n    formatTimeAgo(dateString) {\n      if (!dateString) return '';\n\n      const date = new Date(dateString);\n      const now = new Date();\n      const diffInMinutes = Math.floor((now - date) / (1000 * 60));\n\n      if (diffInMinutes < 1) {\n        return 'Just now';\n      } else if (diffInMinutes < 60) {\n        return `${diffInMinutes}m ago`;\n      } else if (diffInMinutes < 1440) {\n        const hours = Math.floor(diffInMinutes / 60);\n        return `${hours}h ago`;\n      } else {\n        const days = Math.floor(diffInMinutes / 1440);\n        return `${days}d ago`;\n      }\n    },\n\n    // Filter requests by status and navigate\n    filterRequestsByStatus(status) {\n      this.$router.push({\n        path: '/admin/requests',\n        query: { status: status }\n      });\n    },\n\n    // Filter requests by date and navigate\n    filterRequestsByDate(period) {\n      const query = {};\n      const today = new Date();\n\n      if (period === 'today') {\n        query.date_from = today.toISOString().split('T')[0];\n        query.date_to = today.toISOString().split('T')[0];\n      }\n\n      this.$router.push({\n        path: '/admin/requests',\n        query: query\n      });\n    },\n\n    // Navigate to requests with specific filters\n    navigateToRequests(filter) {\n      const query = {};\n\n      switch (filter) {\n        case 'pending':\n          query.status = 'pending';\n          break;\n        case 'urgent':\n          query.priority = 'high';\n          query.status = 'pending';\n          break;\n        case 'processing':\n          query.status = 'processing';\n          break;\n        case 'recent':\n          query.sort = 'requested_at';\n          query.order = 'desc';\n          break;\n      }\n\n      this.$router.push({\n        path: '/admin/requests',\n        query: query\n      });\n    },\n\n    // View request details\n    viewRequestDetails(requestId) {\n      this.$router.push({\n        path: '/admin/requests',\n        query: { view: requestId }\n      });\n    },\n\n    // Get progress percentage for processing overview\n    getProgressPercentage(type) {\n      const total = this.stats.totalRequests || 1; // Avoid division by zero\n\n      switch (type) {\n        case 'pending':\n          return Math.round((this.stats.pendingRequests / total) * 100);\n        case 'processing':\n          return Math.round((this.stats.processingRequests / total) * 100);\n        case 'completed':\n          return Math.round((this.stats.completedRequests / total) * 100);\n        default:\n          return 0;\n      }\n    },\n\n    // Format currency for display\n    formatCurrency(amount) {\n      if (!amount) return '0.00';\n      return parseFloat(amount).toLocaleString('en-US', {\n        minimumFractionDigits: 2,\n        maximumFractionDigits: 2\n      });\n    },\n\n    // Real-time features\n    async initializeRealTimeFeatures() {\n      console.log('Initializing real-time features for AdminDashboard');\n\n      try {\n        // Add connection reference (will connect if needed)\n        await notificationService.addConnectionRef('admin');\n\n        // Listen for dashboard-relevant notifications\n        notificationService.on('notification', this.handleRealTimeNotification);\n        notificationService.on('new_request', this.handleNewRequest);\n        notificationService.on('request_status_changed', this.handleStatusChange);\n        notificationService.on('dashboard_update', this.handleDashboardUpdate);\n      } catch (error) {\n        console.error('Failed to initialize real-time features:', error);\n      }\n    },\n\n    cleanupRealTimeFeatures() {\n      console.log('Cleaning up real-time features for AdminDashboard');\n\n      // Remove notification listeners\n      notificationService.off('notification', this.handleRealTimeNotification);\n      notificationService.off('new_request', this.handleNewRequest);\n      notificationService.off('request_status_changed', this.handleStatusChange);\n      notificationService.off('dashboard_update', this.handleDashboardUpdate);\n    },\n\n    handleRealTimeNotification(notification) {\n      console.log('Dashboard received real-time notification:', notification);\n\n      // Handle different notification types\n      switch (notification.type) {\n        case 'new_request':\n          this.handleNewRequest(notification.data);\n          break;\n        case 'request_status_changed':\n          this.handleStatusChange(notification.data);\n          break;\n        case 'dashboard_update':\n          this.handleDashboardUpdate(notification.data);\n          break;\n        default:\n          console.log('Unhandled notification type:', notification.type);\n      }\n    },\n\n    handleNewRequest(data) {\n      console.log('New request received on dashboard:', data);\n\n      // Update statistics\n      this.stats.totalRequests++;\n      this.stats.pendingRequests++;\n      this.stats.todayRequests++;\n\n      // Refresh dashboard data to get accurate counts\n      this.loadDashboardStats();\n      this.loadRecentActivity();\n      this.loadRecentRequests();\n    },\n\n    handleStatusChange(data) {\n      console.log('Request status changed on dashboard:', data);\n\n      // Refresh dashboard statistics and recent activity\n      this.loadDashboardStats();\n      this.loadRecentActivity();\n      this.loadPriorityRequests();\n      this.loadRecentRequests();\n    },\n\n    handleDashboardUpdate(data) {\n      console.log('Dashboard update received:', data);\n\n      // Refresh all dashboard data\n      this.loadDashboardData();\n    }\n\n  },\n\n  // Auto-refresh dashboard data every 5 minutes\n  created() {\n    this.refreshInterval = setInterval(() => {\n      this.loadDashboardStats();\n      this.loadRecentActivity();\n      this.loadPriorityRequests();\n      this.loadRecentRequests();\n    }, 5 * 60 * 1000); // 5 minutes\n  }\n};\n</script>\n\n<style scoped>\n@import './css/adminDashboard.css';\n</style>\n"], "mappings": ";AAobA,OAAOA,gBAAe,MAAO,6BAA6B;AAC1D,OAAOC,oBAAmB,MAAO,iCAAiC;AAClE,OAAOC,mBAAkB,MAAO,gCAAgC;AAChE,OAAOC,WAAU,MAAO,mBAAmB;AAC3C,OAAOC,YAAW,MAAO,oBAAoB;AAE7C,eAAe;EACbC,IAAI,EAAE,gBAAgB;EACtBC,UAAU,EAAE;IACVH,WAAW;IACXC;EACF,CAAC;EACDG,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,OAAO,EAAE,IAAI;MACb;MACAC,gBAAgB,EAAE,KAAK;MACvBC,gBAAgB,EAAE,KAAK;MACvBC,QAAQ,EAAE,KAAK;MACfC,SAAS,EAAE,IAAI;MACf;MACAC,KAAK,EAAE;QACLC,aAAa,EAAE,CAAC;QAChBC,eAAe,EAAE,CAAC;QAClBC,gBAAgB,EAAE,CAAC;QACnBC,iBAAiB,EAAE,CAAC;QACpBC,kBAAkB,EAAE,CAAC;QACrBC,cAAc,EAAE,CAAC;QACjBC,YAAY,EAAE,CAAC;QACfC,aAAa,EAAE,CAAC;QAChBC,YAAY,EAAE,CAAC;QACfC,UAAU,EAAE,CAAC;QACbC,cAAc,EAAE,CAAC;QACjBC,cAAc,EAAE,CAAC;QACjBC,eAAe,EAAE;MACnB,CAAC;MACDC,cAAc,EAAE,EAAE;MAClBC,gBAAgB,EAAE,EAAE;MACpBC,cAAc,EAAE,EAAE;MAClBC,YAAY,EAAE;IAChB,CAAC;EACH,CAAC;EAED,MAAMC,OAAOA,CAAA,EAAG;IACd;IACA,IAAI,CAAC/B,gBAAgB,CAACgC,UAAU,CAAC,CAAC,EAAE;MAClC,IAAI,CAACC,OAAO,CAACC,IAAI,CAAC,cAAc,CAAC;MACjC;IACF;;IAEA;IACA,IAAI,CAACC,YAAY,CAAC,CAAC;;IAEnB;IACA,MAAM,IAAI,CAACC,iBAAiB,CAAC,CAAC;;IAE9B;IACA,IAAI,CAACC,0BAA0B,CAAC,CAAC;EACnC,CAAC;EAEDC,aAAaA,CAAA,EAAG;IACd,IAAI,IAAI,CAACC,YAAY,EAAE;MACrBC,MAAM,CAACC,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACF,YAAY,CAAC;IACzD;IACA,IAAI,IAAI,CAACG,eAAe,EAAE;MACxBC,aAAa,CAAC,IAAI,CAACD,eAAe,CAAC;IACrC;;IAEA;IACA,IAAI,CAACE,uBAAuB,CAAC,CAAC;EAChC,CAAC;EAEDC,QAAQ,EAAE;IACRC,UAAUA,CAAA,EAAG;MACX,MAAMC,IAAG,GAAI,IAAI,CAACC,MAAM,CAACD,IAAI;MAC7B,IAAIA,IAAI,CAACE,QAAQ,CAAC,cAAc,CAAC,EAAE,OAAO,OAAO;MACjD,IAAIF,IAAI,CAACE,QAAQ,CAAC,iBAAiB,CAAC,EAAE,OAAO,UAAU;MACvD,IAAIF,IAAI,CAACE,QAAQ,CAAC,gBAAgB,CAAC,EAAE,OAAO,SAAS;MACrD,IAAIF,IAAI,CAACE,QAAQ,CAAC,iBAAiB,CAAC,EAAE,OAAO,UAAU;MACvD,IAAIF,IAAI,CAACE,QAAQ,CAAC,sBAAsB,CAAC,EAAE,OAAO,UAAU;MAC5D,IAAIF,IAAI,CAACE,QAAQ,CAAC,gBAAgB,CAAC,EAAE,OAAO,SAAS;MACrD,OAAO,WAAW;IACpB;EACF,CAAC;EAEDC,OAAO,EAAE;IACP;IACAf,YAAYA,CAAA,EAAG;MACb,IAAI,CAACxB,QAAO,GAAI6B,MAAM,CAACW,UAAS,IAAK,GAAG;;MAExC;MACA,IAAI,CAAC,IAAI,CAACxC,QAAQ,EAAE;QAClB,MAAMyC,KAAI,GAAIC,YAAY,CAACC,OAAO,CAAC,uBAAuB,CAAC;QAC3D,IAAI,CAAC7C,gBAAe,GAAI2C,KAAI,GAAIG,IAAI,CAACC,KAAK,CAACJ,KAAK,IAAI,KAAK;MAC3D,OAAO;QACL,IAAI,CAAC3C,gBAAe,GAAI,IAAI,EAAE;MAChC;;MAEA;MACA,IAAI,CAAC8B,YAAW,GAAI,MAAM;QACxB,MAAMkB,SAAQ,GAAI,IAAI,CAAC9C,QAAQ;QAC/B,IAAI,CAACA,QAAO,GAAI6B,MAAM,CAACW,UAAS,IAAK,GAAG;QAExC,IAAI,IAAI,CAACxC,QAAO,IAAK,CAAC8C,SAAS,EAAE;UAC/B,IAAI,CAAChD,gBAAe,GAAI,IAAI,EAAE;QAChC,OAAO,IAAI,CAAC,IAAI,CAACE,QAAO,IAAK8C,SAAS,EAAE;UACtC;UACA,MAAML,KAAI,GAAIC,YAAY,CAACC,OAAO,CAAC,uBAAuB,CAAC;UAC3D,IAAI,CAAC7C,gBAAe,GAAI2C,KAAI,GAAIG,IAAI,CAACC,KAAK,CAACJ,KAAK,IAAI,KAAK;QAC3D;MACF,CAAC;MACDZ,MAAM,CAACkB,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAACnB,YAAY,CAAC;IACtD,CAAC;IAED;IACAoB,mBAAmBA,CAAA,EAAG;MACpB,IAAI,CAAClD,gBAAe,GAAI,CAAC,IAAI,CAACA,gBAAgB;MAC9C4C,YAAY,CAACO,OAAO,CAAC,uBAAuB,EAAEL,IAAI,CAACM,SAAS,CAAC,IAAI,CAACpD,gBAAgB,CAAC,CAAC;IACtF,CAAC;IAED;IACAqD,gBAAgBA,CAACC,IAAI,EAAE;MACrB,MAAMC,MAAK,GAAI;QACb,WAAW,EAAE,kBAAkB;QAC/B,OAAO,EAAE,cAAc;QACvB,UAAU,EAAE,iBAAiB;QAC7B,SAAS,EAAE,gBAAgB;QAC3B,UAAU,EAAE,iBAAiB;QAC7B,UAAU,EAAE,sBAAsB;QAClC,SAAS,EAAE;MACb,CAAC;;MAED;MACA,IAAI,IAAI,CAACrD,QAAQ,EAAE;QACjB,IAAI,CAACF,gBAAe,GAAI,IAAI;MAC9B;MAEA,IAAIuD,MAAM,CAACD,IAAI,CAAC,EAAE;QAChB,IAAI,CAAC9B,OAAO,CAACC,IAAI,CAAC8B,MAAM,CAACD,IAAI,CAAC,CAAC;MACjC;IACF,CAAC;IAED;IACAE,wBAAwBA,CAAA,EAAG;MACzB,IAAI,CAACvD,gBAAe,GAAI,CAAC,IAAI,CAACA,gBAAgB;IAChD,CAAC;IAED;IACAwD,gBAAgBA,CAACC,MAAM,EAAE;MACvB,IAAIA,MAAK,KAAM,SAAS,EAAE;QACxB,IAAI,CAAClC,OAAO,CAACC,IAAI,CAAC,gBAAgB,CAAC;MACrC,OAAO,IAAIiC,MAAK,KAAM,UAAU,EAAE;QAChC,IAAI,CAAClC,OAAO,CAACC,IAAI,CAAC,iBAAiB,CAAC;MACtC;MACA,IAAI,CAACxB,gBAAe,GAAI,KAAK;IAC/B,CAAC;IAED;IACA0D,kBAAkBA,CAAA,EAAG;MACnB,IAAI,IAAI,CAACzD,QAAQ,EAAE;QACjB,IAAI,CAACF,gBAAe,GAAI,IAAI;MAC9B;IACF,CAAC;IAED;IACA4D,YAAYA,CAAA,EAAG;MACbrE,gBAAgB,CAACsE,MAAM,CAAC,CAAC;MACzB,IAAI,CAACrC,OAAO,CAACC,IAAI,CAAC,cAAc,CAAC;IACnC,CAAC;IAED;IACA,MAAME,iBAAiBA,CAAA,EAAG;MACxB,IAAI,CAAC5B,OAAM,GAAI,IAAI;MAEnB,IAAI;QACF;QACA,MAAM,IAAI,CAAC+D,gBAAgB,CAAC,CAAC;;QAE7B;QACA,MAAM,IAAI,CAACC,kBAAkB,CAAC,CAAC;;QAE/B;QACA,MAAM,IAAI,CAACC,kBAAkB,CAAC,CAAC;MAEjC,EAAE,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD,MAAME,SAAQ,GAAI5E,gBAAgB,CAAC6E,UAAU,CAACH,KAAK,CAAC;QACpD,IAAI,CAAC5C,YAAW,GAAI8C,SAAS,CAACE,OAAM,IAAK,+BAA+B;;QAExE;QACA,IAAIF,SAAS,CAACG,MAAK,KAAM,GAAG,EAAE;UAC5B/E,gBAAgB,CAACsE,MAAM,CAAC,CAAC;UACzB,IAAI,CAACrC,OAAO,CAACC,IAAI,CAAC,cAAc,CAAC;QACnC;MACF,UAAU;QACR,IAAI,CAAC1B,OAAM,GAAI,KAAK;MACtB;IACF,CAAC;IAED;IACA,MAAM+D,gBAAgBA,CAAA,EAAG;MACvB,IAAI;QACF,MAAMS,QAAO,GAAI,MAAMhF,gBAAgB,CAACiF,UAAU,CAAC,CAAC;QACpD,IAAID,QAAQ,CAACE,OAAO,EAAE;UACpB,IAAI,CAACtE,SAAQ,GAAIoE,QAAQ,CAACzE,IAAI;QAChC;MACF,EAAE,OAAOmE,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACrD,IAAI,CAAC9D,SAAQ,GAAIZ,gBAAgB,CAACmF,YAAY,CAAC,CAAC;MAClD;IACF,CAAC;IAID;IACA,MAAMX,kBAAkBA,CAAA,EAAG;MACzB,IAAI;QACF,MAAMQ,QAAO,GAAI,MAAM/E,oBAAoB,CAACmF,iBAAiB,CAAC,CAAC;QAC/D,IAAIJ,QAAQ,CAACE,OAAO,EAAE;UACpB;UACA,IAAI,CAACrE,KAAI,GAAI;YACXC,aAAa,EAAEkE,QAAQ,CAACzE,IAAI,CAACO,aAAY,IAAK,CAAC;YAC/CC,eAAe,EAAEiE,QAAQ,CAACzE,IAAI,CAACQ,eAAc,IAAK,CAAC;YACnDC,gBAAgB,EAAEgE,QAAQ,CAACzE,IAAI,CAACS,gBAAe,IAAK,CAAC;YACrDC,iBAAiB,EAAE+D,QAAQ,CAACzE,IAAI,CAACU,iBAAgB,IAAK,CAAC;YACvDC,kBAAkB,EAAE8D,QAAQ,CAACzE,IAAI,CAACW,kBAAiB,IAAK,CAAC;YACzDC,cAAc,EAAE6D,QAAQ,CAACzE,IAAI,CAACY,cAAa,IAAK,CAAC;YACjDC,YAAY,EAAE4D,QAAQ,CAACzE,IAAI,CAACa,YAAW,IAAK,CAAC;YAC7CC,aAAa,EAAE2D,QAAQ,CAACzE,IAAI,CAACc,aAAY,IAAK,CAAC;YAC/CC,YAAY,EAAE0D,QAAQ,CAACzE,IAAI,CAACe,YAAW,IAAK,CAAC;YAC7C;YACAC,UAAU,EAAEyD,QAAQ,CAACzE,IAAI,CAACgB,UAAS,IAAK,CAAC;YACzCC,cAAc,EAAEwD,QAAQ,CAACzE,IAAI,CAACQ,eAAc,IAAK,CAAC;YAClDU,cAAc,EAAEuD,QAAQ,CAACzE,IAAI,CAACc,aAAY,IAAK,CAAC;YAChDK,eAAe,EAAEsD,QAAQ,CAACzE,IAAI,CAACQ,eAAc,IAAK;UACpD,CAAC;QACH;;QAEA;QACA,MAAM,IAAI,CAACsE,oBAAoB,CAAC,CAAC;QACjC,MAAM,IAAI,CAACC,kBAAkB,CAAC,CAAC;MACjC,EAAE,OAAOZ,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;QACvD,MAAME,SAAQ,GAAI3E,oBAAoB,CAAC4E,UAAU,CAACH,KAAK,CAAC;QACxD,IAAI,CAAC5C,YAAW,GAAI8C,SAAS,CAACE,OAAM,IAAK,qCAAqC;MAChF;IACF,CAAC;IAED;IACA,MAAMO,oBAAoBA,CAAA,EAAG;MAC3B,IAAI;QACF,MAAML,QAAO,GAAI,MAAM/E,oBAAoB,CAACsF,cAAc,CAAC;UACzDC,QAAQ,EAAE,MAAM;UAChBC,KAAK,EAAE,CAAC;UACRV,MAAM,EAAE;QACV,CAAC,CAAC;QACF,IAAIC,QAAQ,CAACE,OAAO,EAAE;UACpB,IAAI,CAACtD,gBAAe,GAAIoD,QAAQ,CAACzE,IAAI,CAACmF,QAAO,IAAK,EAAE;QACtD;MACF,EAAE,OAAOhB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;QACzD,IAAI,CAAC9C,gBAAe,GAAI,EAAE;MAC5B;IACF,CAAC;IAED;IACA,MAAM0D,kBAAkBA,CAAA,EAAG;MACzB,IAAI;QACF,MAAMN,QAAO,GAAI,MAAM/E,oBAAoB,CAACsF,cAAc,CAAC;UACzDE,KAAK,EAAE,CAAC;UACRE,IAAI,EAAE,cAAc;UACpBC,KAAK,EAAE;QACT,CAAC,CAAC;QACF,IAAIZ,QAAQ,CAACE,OAAO,EAAE;UACpB,IAAI,CAACrD,cAAa,GAAImD,QAAQ,CAACzE,IAAI,CAACmF,QAAO,IAAK,EAAE;QACpD;MACF,EAAE,OAAOhB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;QACvD,IAAI,CAAC7C,cAAa,GAAI,EAAE;MAC1B;IACF,CAAC;IAED;IACA,MAAM4C,kBAAkBA,CAAA,EAAG;MACzB,IAAI;QACF,MAAMO,QAAO,GAAI,MAAM/E,oBAAoB,CAAC4F,iBAAiB,CAAC,EAAE,CAAC;QACjE,IAAIb,QAAQ,CAACE,OAAO,EAAE;UACpB,IAAI,CAACvD,cAAa,GAAIqD,QAAQ,CAACzE,IAAG,IAAK,EAAE;QAC3C;MACF,EAAE,OAAOmE,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;QACvD,MAAME,SAAQ,GAAI3E,oBAAoB,CAAC4E,UAAU,CAACH,KAAK,CAAC;QACxDC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEE,SAAS,CAAC;QAC1D,IAAI,CAACjD,cAAa,GAAI,EAAE;MAC1B;IACF,CAAC;IAED;IACAmE,UAAUA,CAACC,KAAK,EAAE;MAChB,IAAI;QACFpB,OAAO,CAACqB,GAAG,CAAC,gBAAgB,EAAED,KAAK,CAAC;QACpC,IAAI,CAAC9D,OAAO,CAACC,IAAI,CAAC6D,KAAK,CAAC;MAC1B,EAAE,OAAOrB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;QACzC,IAAI,CAAC5C,YAAW,GAAI,sCAAsC;MAC5D;IACF,CAAC;IAED;IACAmE,UAAUA,CAACC,UAAU,EAAE;MACrB,IAAI,CAACA,UAAU,EAAE,OAAO,EAAE;MAE1B,MAAMC,IAAG,GAAI,IAAIC,IAAI,CAACF,UAAU,CAAC;MACjC,MAAMG,GAAE,GAAI,IAAID,IAAI,CAAC,CAAC;MACtB,MAAME,aAAY,GAAIC,IAAI,CAACC,KAAK,CAAC,CAACH,GAAE,GAAIF,IAAI,KAAK,IAAG,GAAI,EAAE,CAAC,CAAC;MAE5D,IAAIG,aAAY,GAAI,CAAC,EAAE;QACrB,OAAO,UAAU;MACnB,OAAO,IAAIA,aAAY,GAAI,EAAE,EAAE;QAC7B,OAAO,GAAGA,aAAa,UAAUA,aAAY,GAAI,IAAI,GAAE,GAAI,EAAE,MAAM;MACrE,OAAO,IAAIA,aAAY,GAAI,IAAI,EAAE;QAC/B,MAAMG,KAAI,GAAIF,IAAI,CAACC,KAAK,CAACF,aAAY,GAAI,EAAE,CAAC;QAC5C,OAAO,GAAGG,KAAK,QAAQA,KAAI,GAAI,IAAI,GAAE,GAAI,EAAE,MAAM;MACnD,OAAO;QACL,MAAMC,IAAG,GAAIH,IAAI,CAACC,KAAK,CAACF,aAAY,GAAI,IAAI,CAAC;QAC7C,OAAO,GAAGI,IAAI,OAAOA,IAAG,GAAI,IAAI,GAAE,GAAI,EAAE,MAAM;MAChD;IACF,CAAC;IAED;IACAC,UAAUA,CAACT,UAAU,EAAE;MACrB,IAAI,CAACA,UAAU,EAAE,OAAO,EAAE;MAE1B,MAAMC,IAAG,GAAI,IAAIC,IAAI,CAACF,UAAU,CAAC;MACjC,OAAOC,IAAI,CAACS,kBAAkB,CAAC,OAAO,EAAE;QACtCC,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE,SAAS;QACjBC,MAAM,EAAE;MACV,CAAC,CAAC;IACJ,CAAC;IAED;IACA,MAAMC,gBAAgBA,CAAA,EAAG;MACvB,MAAM,IAAI,CAAC5E,iBAAiB,CAAC,CAAC;IAChC,CAAC;IAED;IACA6E,cAAcA,CAAA,EAAG;MACf;MACAtC,OAAO,CAACqB,GAAG,CAAC,4BAA4B,CAAC;MACzC;IACF,CAAC;IAED;IACAkB,eAAeA,CAACC,IAAI,EAAE;MACpB,MAAMC,KAAI,GAAI;QACZ,mBAAmB,EAAE,kBAAkB;QACvC,kBAAkB,EAAE,iBAAiB;QACrC,mBAAmB,EAAE,qBAAqB;QAC1C,mBAAmB,EAAE,qBAAqB;QAC1C,eAAe,EAAE,YAAY;QAC7B,OAAO,EAAE,oBAAoB;QAC7B,QAAQ,EAAE,qBAAqB;QAC/B,SAAS,EAAE;MACb,CAAC;MACD,OAAOA,KAAK,CAACD,IAAI,KAAKC,KAAK,CAACC,OAAO;IACrC,CAAC;IAED;IACAC,oBAAoBA,CAACH,IAAI,EAAE;MACzB,MAAMI,OAAM,GAAI;QACd,mBAAmB,EAAE,YAAY;QACjC,kBAAkB,EAAE,YAAY;QAChC,mBAAmB,EAAE,YAAY;QACjC,mBAAmB,EAAE,WAAW;QAChC,eAAe,EAAE,YAAY;QAC7B,OAAO,EAAE,SAAS;QAClB,QAAQ,EAAE,cAAc;QACxB,SAAS,EAAE;MACb,CAAC;MACD,OAAOA,OAAO,CAACJ,IAAI,KAAKI,OAAO,CAACF,OAAO;IACzC,CAAC;IAED;IACAG,qBAAqBA,CAACzC,MAAM,EAAE;MAC5B,MAAMwC,OAAM,GAAI;QACd,WAAW,EAAE,eAAe;QAC5B,SAAS,EAAE,eAAe;QAC1B,QAAQ,EAAE,cAAc;QACxB,aAAa,EAAE,YAAY;QAC3B,SAAS,EAAE;MACb,CAAC;MACD,OAAOA,OAAO,CAACxC,MAAM,EAAE0C,WAAW,CAAC,CAAC,KAAKF,OAAO,CAACF,OAAO;IAC1D,CAAC;IAED;;IAEA;IACAK,aAAaA,CAACxB,UAAU,EAAE;MACxB,IAAI,CAACA,UAAU,EAAE,OAAO,EAAE;MAE1B,MAAMC,IAAG,GAAI,IAAIC,IAAI,CAACF,UAAU,CAAC;MACjC,MAAMG,GAAE,GAAI,IAAID,IAAI,CAAC,CAAC;MACtB,MAAME,aAAY,GAAIC,IAAI,CAACC,KAAK,CAAC,CAACH,GAAE,GAAIF,IAAI,KAAK,IAAG,GAAI,EAAE,CAAC,CAAC;MAE5D,IAAIG,aAAY,GAAI,CAAC,EAAE;QACrB,OAAO,UAAU;MACnB,OAAO,IAAIA,aAAY,GAAI,EAAE,EAAE;QAC7B,OAAO,GAAGA,aAAa,OAAO;MAChC,OAAO,IAAIA,aAAY,GAAI,IAAI,EAAE;QAC/B,MAAMG,KAAI,GAAIF,IAAI,CAACC,KAAK,CAACF,aAAY,GAAI,EAAE,CAAC;QAC5C,OAAO,GAAGG,KAAK,OAAO;MACxB,OAAO;QACL,MAAMC,IAAG,GAAIH,IAAI,CAACC,KAAK,CAACF,aAAY,GAAI,IAAI,CAAC;QAC7C,OAAO,GAAGI,IAAI,OAAO;MACvB;IACF,CAAC;IAED;IACAiB,sBAAsBA,CAAC5C,MAAM,EAAE;MAC7B,IAAI,CAAC9C,OAAO,CAACC,IAAI,CAAC;QAChBa,IAAI,EAAE,iBAAiB;QACvB6E,KAAK,EAAE;UAAE7C,MAAM,EAAEA;QAAO;MAC1B,CAAC,CAAC;IACJ,CAAC;IAED;IACA8C,oBAAoBA,CAACC,MAAM,EAAE;MAC3B,MAAMF,KAAI,GAAI,CAAC,CAAC;MAChB,MAAMG,KAAI,GAAI,IAAI3B,IAAI,CAAC,CAAC;MAExB,IAAI0B,MAAK,KAAM,OAAO,EAAE;QACtBF,KAAK,CAACI,SAAQ,GAAID,KAAK,CAACE,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACnDN,KAAK,CAACO,OAAM,GAAIJ,KAAK,CAACE,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACnD;MAEA,IAAI,CAACjG,OAAO,CAACC,IAAI,CAAC;QAChBa,IAAI,EAAE,iBAAiB;QACvB6E,KAAK,EAAEA;MACT,CAAC,CAAC;IACJ,CAAC;IAED;IACAQ,kBAAkBA,CAACC,MAAM,EAAE;MACzB,MAAMT,KAAI,GAAI,CAAC,CAAC;MAEhB,QAAQS,MAAM;QACZ,KAAK,SAAS;UACZT,KAAK,CAAC7C,MAAK,GAAI,SAAS;UACxB;QACF,KAAK,QAAQ;UACX6C,KAAK,CAACpC,QAAO,GAAI,MAAM;UACvBoC,KAAK,CAAC7C,MAAK,GAAI,SAAS;UACxB;QACF,KAAK,YAAY;UACf6C,KAAK,CAAC7C,MAAK,GAAI,YAAY;UAC3B;QACF,KAAK,QAAQ;UACX6C,KAAK,CAACjC,IAAG,GAAI,cAAc;UAC3BiC,KAAK,CAAChC,KAAI,GAAI,MAAM;UACpB;MACJ;MAEA,IAAI,CAAC3D,OAAO,CAACC,IAAI,CAAC;QAChBa,IAAI,EAAE,iBAAiB;QACvB6E,KAAK,EAAEA;MACT,CAAC,CAAC;IACJ,CAAC;IAED;IACAU,kBAAkBA,CAACC,SAAS,EAAE;MAC5B,IAAI,CAACtG,OAAO,CAACC,IAAI,CAAC;QAChBa,IAAI,EAAE,iBAAiB;QACvB6E,KAAK,EAAE;UAAEY,IAAI,EAAED;QAAU;MAC3B,CAAC,CAAC;IACJ,CAAC;IAED;IACAE,qBAAqBA,CAACtB,IAAI,EAAE;MAC1B,MAAMuB,KAAI,GAAI,IAAI,CAAC7H,KAAK,CAACC,aAAY,IAAK,CAAC,EAAE;;MAE7C,QAAQqG,IAAI;QACV,KAAK,SAAS;UACZ,OAAOZ,IAAI,CAACoC,KAAK,CAAE,IAAI,CAAC9H,KAAK,CAACE,eAAc,GAAI2H,KAAK,GAAI,GAAG,CAAC;QAC/D,KAAK,YAAY;UACf,OAAOnC,IAAI,CAACoC,KAAK,CAAE,IAAI,CAAC9H,KAAK,CAACK,kBAAiB,GAAIwH,KAAK,GAAI,GAAG,CAAC;QAClE,KAAK,WAAW;UACd,OAAOnC,IAAI,CAACoC,KAAK,CAAE,IAAI,CAAC9H,KAAK,CAACI,iBAAgB,GAAIyH,KAAK,GAAI,GAAG,CAAC;QACjE;UACE,OAAO,CAAC;MACZ;IACF,CAAC;IAED;IACAE,cAAcA,CAACC,MAAM,EAAE;MACrB,IAAI,CAACA,MAAM,EAAE,OAAO,MAAM;MAC1B,OAAOC,UAAU,CAACD,MAAM,CAAC,CAACE,cAAc,CAAC,OAAO,EAAE;QAChDC,qBAAqB,EAAE,CAAC;QACxBC,qBAAqB,EAAE;MACzB,CAAC,CAAC;IACJ,CAAC;IAED;IACA,MAAM5G,0BAA0BA,CAAA,EAAG;MACjCsC,OAAO,CAACqB,GAAG,CAAC,oDAAoD,CAAC;MAEjE,IAAI;QACF;QACA,MAAM9F,mBAAmB,CAACgJ,gBAAgB,CAAC,OAAO,CAAC;;QAEnD;QACAhJ,mBAAmB,CAACiJ,EAAE,CAAC,cAAc,EAAE,IAAI,CAACC,0BAA0B,CAAC;QACvElJ,mBAAmB,CAACiJ,EAAE,CAAC,aAAa,EAAE,IAAI,CAACE,gBAAgB,CAAC;QAC5DnJ,mBAAmB,CAACiJ,EAAE,CAAC,wBAAwB,EAAE,IAAI,CAACG,kBAAkB,CAAC;QACzEpJ,mBAAmB,CAACiJ,EAAE,CAAC,kBAAkB,EAAE,IAAI,CAACI,qBAAqB,CAAC;MACxE,EAAE,OAAO7E,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MAClE;IACF,CAAC;IAED9B,uBAAuBA,CAAA,EAAG;MACxB+B,OAAO,CAACqB,GAAG,CAAC,mDAAmD,CAAC;;MAEhE;MACA9F,mBAAmB,CAACsJ,GAAG,CAAC,cAAc,EAAE,IAAI,CAACJ,0BAA0B,CAAC;MACxElJ,mBAAmB,CAACsJ,GAAG,CAAC,aAAa,EAAE,IAAI,CAACH,gBAAgB,CAAC;MAC7DnJ,mBAAmB,CAACsJ,GAAG,CAAC,wBAAwB,EAAE,IAAI,CAACF,kBAAkB,CAAC;MAC1EpJ,mBAAmB,CAACsJ,GAAG,CAAC,kBAAkB,EAAE,IAAI,CAACD,qBAAqB,CAAC;IACzE,CAAC;IAEDH,0BAA0BA,CAACK,YAAY,EAAE;MACvC9E,OAAO,CAACqB,GAAG,CAAC,4CAA4C,EAAEyD,YAAY,CAAC;;MAEvE;MACA,QAAQA,YAAY,CAACtC,IAAI;QACvB,KAAK,aAAa;UAChB,IAAI,CAACkC,gBAAgB,CAACI,YAAY,CAAClJ,IAAI,CAAC;UACxC;QACF,KAAK,wBAAwB;UAC3B,IAAI,CAAC+I,kBAAkB,CAACG,YAAY,CAAClJ,IAAI,CAAC;UAC1C;QACF,KAAK,kBAAkB;UACrB,IAAI,CAACgJ,qBAAqB,CAACE,YAAY,CAAClJ,IAAI,CAAC;UAC7C;QACF;UACEoE,OAAO,CAACqB,GAAG,CAAC,8BAA8B,EAAEyD,YAAY,CAACtC,IAAI,CAAC;MAClE;IACF,CAAC;IAEDkC,gBAAgBA,CAAC9I,IAAI,EAAE;MACrBoE,OAAO,CAACqB,GAAG,CAAC,oCAAoC,EAAEzF,IAAI,CAAC;;MAEvD;MACA,IAAI,CAACM,KAAK,CAACC,aAAa,EAAE;MAC1B,IAAI,CAACD,KAAK,CAACE,eAAe,EAAE;MAC5B,IAAI,CAACF,KAAK,CAACQ,aAAa,EAAE;;MAE1B;MACA,IAAI,CAACmD,kBAAkB,CAAC,CAAC;MACzB,IAAI,CAACC,kBAAkB,CAAC,CAAC;MACzB,IAAI,CAACa,kBAAkB,CAAC,CAAC;IAC3B,CAAC;IAEDgE,kBAAkBA,CAAC/I,IAAI,EAAE;MACvBoE,OAAO,CAACqB,GAAG,CAAC,sCAAsC,EAAEzF,IAAI,CAAC;;MAEzD;MACA,IAAI,CAACiE,kBAAkB,CAAC,CAAC;MACzB,IAAI,CAACC,kBAAkB,CAAC,CAAC;MACzB,IAAI,CAACY,oBAAoB,CAAC,CAAC;MAC3B,IAAI,CAACC,kBAAkB,CAAC,CAAC;IAC3B,CAAC;IAEDiE,qBAAqBA,CAAChJ,IAAI,EAAE;MAC1BoE,OAAO,CAACqB,GAAG,CAAC,4BAA4B,EAAEzF,IAAI,CAAC;;MAE/C;MACA,IAAI,CAAC6B,iBAAiB,CAAC,CAAC;IAC1B;EAEF,CAAC;EAED;EACAsH,OAAOA,CAAA,EAAG;IACR,IAAI,CAAChH,eAAc,GAAIiH,WAAW,CAAC,MAAM;MACvC,IAAI,CAACnF,kBAAkB,CAAC,CAAC;MACzB,IAAI,CAACC,kBAAkB,CAAC,CAAC;MACzB,IAAI,CAACY,oBAAoB,CAAC,CAAC;MAC3B,IAAI,CAACC,kBAAkB,CAAC,CAAC;IAC3B,CAAC,EAAE,IAAI,EAAC,GAAI,IAAI,CAAC,EAAE;EACrB;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}