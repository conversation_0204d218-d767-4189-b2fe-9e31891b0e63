const DocumentRequestService = require('./src/services/documentRequestService');

async function testCancelRequest() {
  try {
    console.log('Testing cancel request functionality...');

    const requestId = 12;
    const clientId = 12;
    const reason = 'Testing cancel functionality';

    console.log('Cancelling request:', { requestId, clientId, reason });
    const result = await DocumentRequestService.cancelRequest(requestId, clientId, reason);
    console.log('Cancel Success:', result);

  } catch (error) {
    console.error('Cancel Error:', error);
  }

  process.exit(0);
}

testCancelRequest();
